import React, { useState, useRef, useEffect } from "react";
import { isset } from "../common/formCommfun";

function ISQSelect({ item, index, saveIsqs }) {
  const [otherSelected, setOtherSelected] = useState(false);
  const [selectedValue, setSelectedValue] = useState("");
  const [selectColor, setSelectColor] = useState('rgb(158, 158, 158)');
  const otherInputRef = useRef(null);

  // useEffect(() => {
  //   const mcatid = window.forms_param.mcatId; 
  //   let userFilledData = [];
  //       try{
  //         userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
  //       }
  //       catch(e){
  //         userFilledData=[];
  //       }
  
  //       if (isset(()=>userFilledData) && isset(()=>userFilledData[mcatid]))  {
  //     const filledData = userFilledData[mcatid];
  
  //     Object.keys(filledData).forEach((key) => {
  //       const value = filledData[key];
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];          
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         setSelectedValue(value);
  //         if (value === "") {
  //           setSelectColor('rgb(158, 158, 158)');
  //         } else {
  //           setSelectColor('rgb(51, 51, 51)');
  //         }
  //       }
  //     });
  //   } 
  //   else if (window.forms_param && window.forms_param.plsqArr) {
  //     const params = window.forms_param.plsqArr.split('#');
  //     params.forEach(param => {
  //       const [encodedKey, encodedValue] = param.split(':');
  //       const key = decodeURIComponent(encodedKey);
  //       const value = decodeURIComponent(encodedValue);
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];          
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         setSelectedValue(value);
  //         if (value === "") {
  //           setSelectColor('rgb(158, 158, 158)');
  //         } else {
  //           setSelectColor('rgb(51, 51, 51)');
  //         }
  //       }
  //     });
  //   }
  // }, []);


  const handleSelectChange = (e) => {
    if (e.target.value.toLowerCase() === "other" || e.target.value.toLowerCase() === "others") {
      setOtherSelected(true);
    } else {
      setOtherSelected(false);
    }

    // Update color based on selected option
    if (e.target.value === "") {
      setSelectColor('rgb(158, 158, 158)');
    } else {
      setSelectColor('rgb(51, 51, 51)');
    }

    const selectedOptionId = item.IM_SPEC_OPTIONS_ID.split("##")[e.target.selectedIndex-1]; // Get selected option ID
    setSelectedValue(e.target.value);
    saveIsqs(e.target.value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, selectedOptionId);
  };

  useEffect(() => {
    if (otherSelected && otherInputRef.current) {
      otherInputRef.current.focus();
    }
  }, [otherSelected]);

  return (
    <div className="widt100">
      {item.IM_SPEC_MASTER_TYPE === "3" ? (
        <div className="row-be txtRw" key={index}>
          <div className="bedblk bepr ber-w50 lgcont">
            <div className="lbtool">
              <div className="tllb pr">
                <label id={`t0901ques${index}`} quesid={item.IM_SPEC_MASTER_ID}>
                  {item.IM_SPEC_MASTER_DESC}
                </label>
              </div>
            </div>
            <select
              className="ber-input grb bewfull betextclr pr20"
              name={`t0901select_name${index}`}
              id={`t0901select_name${index}`}
              style={{ color: selectColor }}
              onChange={handleSelectChange}
              value={selectedValue} 
            >
              <option value="">Select a Value</option>
              {item.IM_SPEC_OPTIONS_DESC.split("##").map((desc, i) => (
                <option key={i} value={desc} optionid={item.IM_SPEC_OPTIONS_ID.split("##")[i]}>
                  {desc}
                </option>
              ))}
            </select>
            {otherSelected && (
              <input
                type="text"
                className="ber-input othersel"
                id={`other_t0901select_name${index}`}
                placeholder=""
                ref={otherInputRef}
              />
            )}
          </div>
        </div>
      ) : <div></div>}
    </div>
  );
}

export default ISQSelect;
