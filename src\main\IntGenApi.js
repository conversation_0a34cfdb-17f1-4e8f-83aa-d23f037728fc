import { readCookieREC, isset, getparamValREC, reqFormGATrackREC,curUrlAppend, getActivityIdREC, ISSET, getloginmode, LoginNewUiPDP, imageabtest} from '../common/formCommfun';
import CS<PERSON>pi from './CSLApi';
export default async function IntGenApi( form_param , screenname, country_iso) {
    const form_type = form_param.formType === "Enq" ? "SE" : "PBL";
    const landing_ref_url = isset(() => readCookieREC('site-entry-page')) ? readCookieREC('site-entry-page') : '';
    const flag = form_param.formType === "Enq" ? ( screenname == "loginscreen" ? 17 : (form_param.ctaType == "Image" || form_param.ctaType == "Video" )? 16 : form_param.ctaType == 'pdf' ? 19 :(form_param.ctaName.toLowerCase().trim() == " send email" || form_param.ctaName.toLowerCase().trim() == "send sms" || form_param.ctaName.toLowerCase().trim().includes("next") || form_param.ctaName.toLowerCase().trim().includes("pre"))? 1: 12) :form_param.formType === "BL"? ( screenname == "loginscreen" ? 18 : form_param.ctaName.toLowerCase().trim() == "inactive" ? 11 : 14 ):"";
    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    const s_ip = getparamValREC(iploc, 'gip');
    const s_ip_country = getparamValREC(iploc, 'gcnnm');
    const s_ip_country_iso = getparamValREC(iploc, 'gcniso');

    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const name = getparamValREC(imesh, 'fn') || "";
    const ph_no = getparamValREC(imesh, 'mb1') || "";
    const glid = getparamValREC(imesh, 'glid');

    try {
        const parmObj = {
            modref_type: form_param.modrefType,
            modref_id: form_param.pDispId,
            s_prod_name: form_param.prodName,
            flag: flag,
            s_mobile: ph_no,
            modid: form_param.modId,
            mcatID: form_param.mcatId,
            catID: form_param.catId,
            login_mode: window.LoginmodeReact || getloginmode(),
            s_glusrid: glid,
            s_country_iso: country_iso || window.countryiso || s_ip_country_iso,
            s_ip_country: s_ip_country,
            s_ip: s_ip,
            s_ip_country_iso: s_ip_country_iso,
            curr_page_url: curUrlAppend(form_param),
            landing_ref_url : landing_ref_url,
            prev_page_url : document.referrer,
            form_type: form_type,
            _: new Date().getTime(),
            rfq_query_ref_text : "ctaName="+form_param.ctaName+"|ctaType="+form_param.ctaType+"|PT="+form_param.pageType+"|Section="+form_param.section+"|Position="+form_param.position+"|ScriptVer="+form_param.scriptVersion+"|compRank=|searchTerm="+form_param.mcatName+"|"+form_param.prodServ+"|React" + (LoginNewUiPDP(form_param) ? "|ABlogin" : "") + (imageabtest(form_param) ? "|FBimage" : ""),
        };
        if(form_param && form_param.modId === "PRODDTL"){
            parmObj.pdp = true;
        }
        if(isset(()=>form_param.rcvGlid) &&form_param.rcvGlid != ''){
            parmObj.r_glusrid = form_param.rcvGlid;
        }
        if(isset(()=>name) && name != ''){
            parmObj.s_first_name = name;
        }
        
        const queryParams = new URLSearchParams(parmObj);
        let activity_id = getActivityIdREC(form_param);
        if(ISSET(form_param.firecsl) && form_param.firecsl && activity_id !== ""){
            let request_url = `index.php?r=Newreqform/IntentGeneration?${queryParams}`;
            CSLApi(form_param, request_url, activity_id);
        }
        
        const webAddressLocation = location.host
        const ServerName = webAddressLocation.match(/^(dev)/)? "dev-":(webAddressLocation.match(/^stg/)?"stg-":"");
        const response = await fetch(`https://${ServerName}apps.imimg.com/index.php?r=Newreqform/IntentGeneration&${queryParams}`);
        
        if (!response.ok) {
            reqFormGATrackREC("service:IntentGeneration:failure",form_param);
            throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        reqFormGATrackREC("service:IntentGeneration:success",form_param);
        return data;
        
    } catch (error) {
        // Handle errors
        reqFormGATrackREC("service:IntentGeneration:failure",form_param);
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'IntentFail','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}
