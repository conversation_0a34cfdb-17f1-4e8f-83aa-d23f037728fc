import React from 'react';
import './ContactSupplierButton.css';

const ContactSupplierButton = ({ phoneNumber }) => {
    return (
        <div className="contact-supplier-btn">
            <svg
                className="phone-icon"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 100 100"
            >
                <path d="M67.983,79.999c-5.312,0-10.967-1.508-16.801-4.475c-5.379-2.74-10.689-6.662-15.365-11.342s-8.59-9.998-11.326-15.379
    c-2.965-5.84-4.471-11.492-4.471-16.805c0-3.443,3.211-6.771,4.588-8.039c1.984-1.826,5.104-3.961,7.371-3.961
    c1.129,0,2.449,0.738,4.16,2.322c1.277,1.182,2.713,2.783,4.148,4.631c0.867,1.117,5.191,6.816,5.191,9.547
    c0,2.24-2.535,3.801-5.215,5.449c-1.039,0.635-2.109,1.293-2.883,1.914c-0.828,0.666-0.979,1.016-1.002,1.094
    c2.848,7.098,11.553,15.803,18.645,18.645c0.064-0.02,0.416-0.158,1.09-1c0.621-0.773,1.283-1.848,1.916-2.883
    c1.652-2.68,3.211-5.217,5.449-5.217c2.734,0,8.432,4.324,9.545,5.191c1.852,1.436,3.451,2.873,4.635,4.148
    c1.582,1.711,2.32,3.033,2.32,4.16c0,2.271-2.133,5.4-3.955,7.393c-1.271,1.385-4.6,4.607-8.045,4.607L67.983,79.999z"/>
            </svg>

            <span>
                Contact Supplier at <strong>{phoneNumber}</strong>
            </span>
        </div>
    );
};

export default ContactSupplierButton;
