import { readCookieREC, updateimeshCombined } from "../common/formCommfun";

export default async function SessionDetailApi(sessKey, modIdf) {
  const webAddressLocation = location.hostname;
  const ServerName = webAddressLocation.match(/^(dev)/) ? "":(webAddressLocation.match(/^stg/)?"stg-":"");
  const url =  "https://"+ServerName+"apps.imimg.com/index.php?r=Newreqform/SessionDtl";
  const formData = new URLSearchParams();
  formData.append('modid', modIdf);
  formData.append('sessionKey', sessKey);
  formData.append('_', new Date().getTime());
  try {
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        body: formData
      });

    if (!response.ok) {
      throw new Error('Failed to call API');
    }

    let responseData = await response.json();
    // console.log(responseData.DataCookie);
    
    if(responseData.DataCookie){
        responseData.DataCookie = updateimeshCombined(responseData.DataCookie,true);
        const imesh = readCookieREC('ImeshVisitor');
        if(!imesh){if (typeof getLoginStringv1 === "function") getLoginStringv1();}
        return responseData;
    }
  } catch (error) {
    console.error('Failed to call API:', error);
    imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'LoginSessionAPi','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
  }
}
