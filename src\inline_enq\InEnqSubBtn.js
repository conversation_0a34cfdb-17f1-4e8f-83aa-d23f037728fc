import React, { useEffect } from "react";
import { useGlobalState } from "../context/store";
import IntGenApi from "../main/IntGenApi";
import { validateEmailId, validatePhoneNumber } from "../Login/VldNmEm";
import { Eventtracking, getparamValREC, isset, readCookieREC, updateimeshCombined } from "../common/formCommfun";
import LoginApiCall from "../Login/LoginApiCall";
import GlusrUpdate from "../Login/GlusrUpdate";
import LoginWtgoogle from "../Login/LoginWtgoogle";
import SessionDetailApi from "../Login/SessionDeatilApi";
function InEnqSubBtn({form_param,seterr_msg,usrnm,isGDPRcheck,country_iso, GDPRiso ,setGDPRerrmsg ,mob_value,email_val, setusrnm,id,setemail_val,close_Form ,setImeshExist}){
    const { state, dispatch } = useGlobalState();
    async function afterlogin(gusrname,gusremail ,withGoogle , fromlogin,fn){
        if(withGoogle){
            usrnm = gusrname;
            email_val = gusremail;
        }
        const imesh = readCookieREC('ImeshVisitor');
        const imeshglid = getparamValREC(imesh, 'glid');
        const fname = getparamValREC(imesh, 'fn') || fn;
        if(country_iso != "IN" && usrnm != "" && fname == ""){
            const user_data = { glid: imeshglid, name: usrnm, s_email: email_val , country_iso: country_iso}
            await GlusrUpdate(form_param, user_data);
        }
        if(fromlogin){
            IntGenApi(form_param, "loginscreen", country_iso);
            Eventtracking(`SS1-UserLogin`,state.prevtrack,form_param,false);
            dispatch({ type: 'prevtrack', payload: { prevtrack: `-UserLogin` } });
        }else{
            IntGenApi(form_param,"",country_iso);
            Eventtracking(`SS1-CTA`,state.prevtrack,form_param,false);
            dispatch({ type: 'prevtrack', payload: { prevtrack: `-CTA` } });
        }
        window.screencount++;
        if(isset(()=>form_param.formState)){
            form_param.formState(true);
        }
        dispatch({ type: 'openPopup', payload: { openPopup: true } });
    }
    async function openpopup(gusrname,gusremail ,withGoogle){
        if(withGoogle){
            usrnm = gusrname;
            email_val = gusremail;
        }
        const ismshexist = readCookieREC("ImeshVisitor");
        if(!ismshexist){
            let valid_check = "mob";
            let fld_val = mob_value;
            if (country_iso == "IN") {
                valid_check = validatePhoneNumber(mob_value);
            }else{
                valid_check = validateEmailId(email_val);
                fld_val = email_val;
            }
            if(valid_check !=""){
                seterr_msg(valid_check);
                return;
            }
            if(country_iso != "IN" && usrnm == ""){
                seterr_msg("Please enter you name");
                return;
            }
            if(GDPRiso && !isGDPRcheck){
                setGDPRerrmsg("Please Agree to the Terms and Conditions");
                return;
            }
            const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
            const s_ip = getparamValREC(iploc, 'gip');
            const loginres = await LoginApiCall(form_param, fld_val,"",s_ip,country_iso) ;
            if(loginres.code == '200'){
                if (loginres.DataCookie) {
                    dispatch({ type: 'UserData', payload: { UserData: loginres.DataCookie } });
                    dispatch({ type: 'country_iso', payload: { country_iso: loginres.iso } });
                    setImeshExist(readCookieREC("ImeshVisitor"));
                    if(withGoogle){
                        setusrnm(usrnm);
                    }else if(loginres.DataCookie.fn){
                        setusrnm(loginres.DataCookie.fn);
                    }
                }
                dispatch({ type: "Imeshform", payload: { Imeshform: true } });
                afterlogin(gusrname,gusremail ,withGoogle,true,loginres.DataCookie.fn);
            }
        }
        if(ismshexist){
            let responseData = {};
            if(getparamValREC(ismshexist, 'sessionKey')!=''){
                responseData = await SessionDetailApi(getparamValREC(ismshexist, 'sessionKey'),form_param.modId);
                if(responseData && responseData.iso){
                    dispatch({ type: 'country_iso', payload: { country_iso: responseData.iso } });
                }
            }
            else{
                responseData.DataCookie = updateimeshCombined(ismshexist,false);
            }
            if(responseData && responseData.DataCookie){
            dispatch({ type: 'UserData', payload: { UserData: responseData.DataCookie } });}
            afterlogin(gusrname,gusremail ,withGoogle,false,responseData.DataCookie.fn);
        }
    }
    async function handleecomlgn(gusrname,gusremail,withGoogle) {
        const ismshexist = readCookieREC("ImeshVisitor");
        if(!ismshexist){
            if(withGoogle){
                usrnm = gusrname;
                email_val = gusremail;
            }
            let valid_check = "mob";
            let fld_val = mob_value;
            const redirect = () => {
                window.open(form_param.ecomUrl, "_blank");
                close_Form();
            }
            if (country_iso == "IN") {
                valid_check = validatePhoneNumber(mob_value);
            }else{
                valid_check = validateEmailId(email_val);
                fld_val = email_val;
            }
            if(valid_check !=""){
                seterr_msg(valid_check);
                redirect();
                return;
            }
            if(country_iso != "IN" && usrnm == ""){
                seterr_msg("Please enter you name");
                redirect();
                return;
            }
            if(GDPRiso && !isGDPRcheck){
                setGDPRerrmsg("Please Agree to the Terms and Conditions");
                redirect();
                return;
            }
            const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
            const s_ip = getparamValREC(iploc, 'gip');
            const loginres = await LoginApiCall(form_param, fld_val,"",s_ip,country_iso) ;
            if(loginres.code == '200'){
                dispatch({ type: 'UserData', payload: { UserData: loginres.DataCookie } });
                dispatch({ type: 'country_iso', payload: { country_iso: loginres.iso } });
                dispatch({ type: "Imeshform", payload: { Imeshform: true } });
            }
            redirect();
        }
        else{
            redirect();
        }
    }
    useEffect(() => {
        window.userdata = state.UserData;
    }, [state.UserData]);
    return(
        <>
            <div id="t0401_q_send_req_button" className="ber-mdl">
                <div id="t0401_submit" className="inlinebtn" onClick={form_param.isEcom == 1 ? handleecomlgn : openpopup}>Submit Requirement <i className="arrow-right"></i></div>
            </div>
            {(country_iso != "IN" && !state.Imeshform) && <LoginWtgoogle handlelgnSub={form_param.isEcom == 1 ? handleecomlgn : openpopup} id={id} country_iso={country_iso} setemail_val={setemail_val} setusrnm={setusrnm} />}
        </>
    )
}
export default InEnqSubBtn;