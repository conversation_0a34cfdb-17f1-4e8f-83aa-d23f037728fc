.cnamecont{
    margin-top: 15px;
    margin-bottom: 25px;
    width: 60%;
}
.lablcont .tooltip {
    position: relative;
    cursor: pointer;
    background-position: -8px -897px;
    line-height: 21px;
    background-image: url(https://apps.imimg.com/gifs/blform-sprite22.png);
    background-repeat: no-repeat;
    width: 27px;
    height: 27px;
    left: 5px;
    top: 4px;
    display: inline-block;
  }
  .lablcont{
    display: flex;
  }
  .lablcont label{
    position: relative !important;
    padding: 12px 0px 9px 0px ;
    font-size: 15px ;
    color: #696969;
    pointer-events: none;
    line-height: 14px !important;
    display: block !important;
  }
  .cnamebl{
    width: 331px;
    height: 42px;
    padding: 10px !important;
    border: 1px solid #ccc !important;
  }
  .cnamebl[placeholder]{
    color: #888;
    font-size: 12px;
  }
  .warhead{
    font-size: 15px;
    font-weight: 700;
    color: #000;
  }
  
  .lablcont .tooltip .tooltiptext {
    visibility: hidden;
    position: absolute;
    display: block;
    width: 295px;
    border: 1px solid #dcdcdc;
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;
    box-sizing: border-box;
    color: #333;
    box-shadow: 0 0 7px #ccc;
  }
  .lablcont .tooltip .normaltool{
    top: -60px;
    left: 30px;
  }
  .lablcont .tooltip .imagetool{
    top: 35px;
    left: 25%;
    z-index: 2;
    transform: translateX(-65%);
  }
  .normaltool::before {
    border-top: 12px solid transparent;
    border-right: 12px solid #dcdcdc;
    border-bottom: 12px solid transparent;
    border-left: 12px solid transparent;
    top: 60px;
    left: -24px;
    position: absolute;
    content: "";
  }
  .normaltool::after {
    border-top: 10px solid transparent;
    border-right: 10px solid #fff;
    border-bottom: 10px solid transparent;
    border-left: 10px solid transparent;
    top: 62px;
    left: -20px;
    position: absolute;
    content: "";
}
.imagetool::before {
  border-top: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 12px solid #dcdcdc;
  border-left: 12px solid transparent;
  top: -24px;
  left: 65%;
  transform: translateX(-50%);
  position: absolute;
  content: "";
}

.imagetool::after {
  border-top: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #fff;
  border-left: 10px solid transparent;
  top: -20px;
  left: 65%;
  transform: translateX(-50%);
  position: absolute;
  content: "";
}


.tooltiptext li {
    padding: 5px 0;
    list-style: disc;
    font-size: 14px;
    color: #333;
    line-height: 14px;
}
  .tooltiptext ul {
    padding-left: 17px;
 }
  
  .lablcont .tooltip:hover .tooltiptext {
    visibility: visible;
  }
  