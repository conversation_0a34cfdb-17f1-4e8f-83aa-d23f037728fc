import React, {useEffect, useRef} from 'react';
function LoginWtgoogle({handlelgnSub, id,country_iso,setemail_val,setusrnm}){
    let clientID = "";
    if (
        location.hostname.match(/^dev/)  || location.hostname.match(/^stg/) 
      ) {
        clientID =
          "335658149809-o25hpstdu2tdo43j8ppg8l6n6i0dtfl0.apps.googleusercontent.com";
      } else {
        clientID =
          "432055510365-4for8jpqviklkgt2lssm41sfhhfo0ovs.apps.googleusercontent.com";
      }
    const google_signin = useRef("");
    useEffect(() => {
        if (typeof gapi !== "undefined"){
            gapi.load("auth2", function() {
                var auth2 = gapi.auth2.init({
                    client_id: clientID
                });
            fn(google_signin.current, auth2);
            })
        }
    },[])
    function fn(element, auth2) {
        auth2.attachClickHandler(element, {}, function(googleUser) {
            var gusrname = googleUser.getBasicProfile().getName();
            var gusremail = googleUser.getBasicProfile().getEmail();
            if (gusremail != "") {
            // let current_elem = window.countryiso == "IN" && country_iso == "IN"? `t${id}_login_field_mob` :`t${id}_login_field_email`;
            if(id == "0401"){
                // setusrnm(gusrname);
                // setemail_val(gusremail);
                handlelgnSub(gusrname,gusremail,'withGoogle');
            }
            else{
                document.getElementById(`t${id}_login_field_email`).value = gusremail;
                document.getElementById(`t${id}_q_first_nm1`) !== null ? document.getElementById(`t${id}_q_first_nm1`).value = gusrname : "";
                handlelgnSub();
            }
            
            // console.log("Hello");
            }
          }, function(error) {
            console.log("Error: Login with Google not working");
          })
     }
    return(
        <div id="t0901_gwrap"> 
            <p className='alignTp' style={{ fontSize: "14px", fontWeight: 600, textAlign: "center", marginTop: "10px" }}>OR</p> 
            <div id="t0901_gSignInWrapper" style={{textAlign: "center", margin: "10px 0px 15px 0px" }}> 
                <div id="t0901signinBtnFr" className="customG"> 
                    <span className="Gicon"> </span> 
                    <span className="buttonTextfr" ref={google_signin} > Login with Google </span>
                    <input type="hidden" value="0" style={{display:"none"}}id="LWG" /> 
                </div>
            </div>
        </div>
        
    )
}
export default LoginWtgoogle;