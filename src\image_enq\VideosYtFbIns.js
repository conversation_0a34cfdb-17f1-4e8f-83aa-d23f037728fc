import { isset,loadInstaScriptREC,loadScriptREC } from '../common/formCommfun';
import getVideo from './getvideo';




export default async function VideosFn(VideObj,setPiframe, setReset,setLoader) {
      if (isset(()=>setLoader)) {
      setLoader(true);
    }
     let videoKey= isset(()=>VideObj.data["vidKey"]) ? VideObj.data["vidKey"] : "";
      
     if(videoKey == "" || videoKey == "1" || videoKey == "2"){
       loadScriptREC();    
       if (typeof YT === "undefined" && window.IframeApiloaded === 0) {
         loadScriptREC();
         window.onYouTubeIframeAPIReady = function () {
            VideosFn(VideObj,setPiframe, setReset,setLoader);
         };
       } else if (
         (typeof YT === "undefined" && window.IframeApiloaded === 1) ||
         (isset(()=>YT) && YT.loading === 1 && YT.loaded === 0) ||
         window.IframeApiloaded === 0
       ) {
         loadScriptREC();
         window.onYouTubeIframeAPIReady = function () {
            VideosFn(VideObj,setPiframe, setReset,setLoader);
         };
       } else if (isset(()=>YT) && YT.loading === 1 && YT.loaded === 1) {
         window.IframeApiloaded = 2;
         getVideo(VideObj,setPiframe, setReset,setLoader);
       }
     } 
     else if(videoKey == "3"){
       getVideo(VideObj,setPiframe, setReset,setLoader);
     }
     else if(videoKey == "5" || videoKey == "6"){
       loadInstaScriptREC();
       getVideo(VideObj,setPiframe, setReset,setLoader);
   
     }
 
}

