import React from 'react';
import './imgEnq.css';
import { isset, ReturntoPropREC, reqFormGATrackREC, wrapWithRAF } from '../common/formCommfun';
import { useGlobalState } from '../context/store';

const PrevNext = ({ form_param, blurImg, incrementProdCount, prodCountRef,setLoader,showRightSec,toBlur}) => {
    const { state, dispatch } = useGlobalState();
    const tmpId = '0901';
    const imgclass = 'ber-arRN pnMsg imgvid ber-arRNDir';

    const changeProd = wrapWithRAF((event) => {
        if (isset(() => event) && isset(() => event.data)) {
            var todo = event.data.todo;

            if(isset(()=>showRightSec)){
                showRightSec(true);
            }
            if (todo === "prev" && isset(() => form_param.prev) && typeof form_param.prev === "function") {
                window.screencount = 1;
                window.updatedVal = '';
                state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: 1} });
                if(isset(()=>setLoader))
                setLoader(false);
                var returnObj = ReturntoPropREC(form_param);
                form_param.prev(returnObj);
                dispatch({ type: 'formsParamUpdate', payload: { PrevNext: 1 } });
                reqFormGATrackREC('Prev_Product',form_param)
                if(isset(()=>toBlur) && toBlur){
                    reqFormGATrackREC('BlurFormPre',form_param)
                }
            }
            else if (todo === "next" && isset(() => form_param.next) && typeof form_param.next === "function") {
                window.screencount = 1;
                window.updatedVal = '';
                state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: 1} });
                if(isset(()=>setLoader))
                setLoader(false);
                var returnObj = ReturntoPropREC(form_param);
                form_param.next(returnObj);
                dispatch({ type: 'formsParamUpdate', payload: { PrevNext: 1 } });
                reqFormGATrackREC('Next_Product',form_param)
                if(isset(()=>toBlur) && toBlur){
                    reqFormGATrackREC('BlurFormNext',form_param)
                }
            }
            if(incrementProdCount!=undefined){
                incrementProdCount(1); 
                if (prodCountRef.current > 2) {
                    if(blurImg!=undefined){
                        
                    blurImg(true); 
                    incrementProdCount(0);
    
                    }
                }
            }
        }
    });

    return (
        <div id={`t${tmpId}_nextprediv`}>
            {(isset(() => form_param.prev) && typeof form_param.prev === "function") ?
                <button id={`t${tmpId}_prebtnR`} className='ber-arLN imgvid pnMsg' onClick={() => changeProd({ data: { todo: 'prev' } })}>
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.61539 20.7692C4.61539 29.6907 11.8477 36.9231 20.7692 36.9231C29.6908 36.9231 36.9231 29.6907 36.9231 20.7692C36.9231 11.8477 29.6908 4.61538 20.7692 4.61538C11.8477 4.61538 4.61539 11.8477 4.61539 20.7692Z" fill="#333333"/>
                        <path d="M20 0C23.9556 0 27.8224 1.17298 31.1114 3.37061C34.4004 5.56823 36.9638 8.69181 38.4776 12.3463C39.9913 16.0008 40.3874 20.0222 39.6157 23.9018C38.844 27.7814 36.9392 31.3451 34.1421 34.1421C31.3451 36.9392 27.7814 38.844 23.9018 39.6157C20.0222 40.3874 16.0009 39.9913 12.3463 38.4776C8.69181 36.9638 5.56823 34.4004 3.37061 31.1114C1.17298 27.8224 -2.20077e-07 23.9556 -2.20077e-07 20C-2.20077e-07 14.6957 2.10714 9.60859 5.85786 5.85786C9.60859 2.10714 14.6957 0 20 0ZM14.4461 21.5385L22.1385 30C22.3424 30.2244 22.5886 30.4064 22.863 30.5356C23.1373 30.6647 23.4344 30.7386 23.7373 30.7529C24.0402 30.7672 24.343 30.7216 24.6283 30.6188C24.9136 30.516 25.1758 30.358 25.4 30.1538C25.6244 29.9499 25.8064 29.7037 25.9356 29.4293C26.0647 29.155 26.1386 28.8579 26.1529 28.555C26.1672 28.2521 26.1216 27.9493 26.0188 27.664C25.916 27.3787 25.758 27.1165 25.5538 26.8923L19.2769 20L25.5538 13.0923C25.8999 12.6314 26.061 12.0576 26.0053 11.4839C25.9496 10.9102 25.6812 10.3781 25.253 9.99224C24.8247 9.60644 24.2676 9.39486 23.6912 9.39914C23.1148 9.40342 22.5609 9.62325 22.1385 10.0154L14.4461 18.4769C14.072 18.899 13.8654 19.4436 13.8654 20.0077C13.8654 20.5718 14.072 21.1163 14.4461 21.5385Z" fill="white"/>
                    </svg>
                </button>
                : ''}

            {(isset(() => form_param.next) && typeof form_param.next === "function") ?
                <button id={`t${tmpId}_nextbtnR`} className={imgclass} onClick={() => changeProd({ data: { todo: 'next' } })}>
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M35.3846 20.7692C35.3846 29.6907 28.1523 36.9231 19.2308 36.9231C10.3092 36.9231 3.07692 29.6907 3.07692 20.7692C3.07692 11.8477 10.3092 4.61538 19.2308 4.61538C28.1523 4.61538 35.3846 11.8477 35.3846 20.7692Z" fill="#333333"/>
                        <path d="M20 0C16.0444 0 12.1776 1.17298 8.8886 3.37061C5.59962 5.56823 3.03617 8.69181 1.52242 12.3463C0.00866569 16.0008 -0.387401 20.0222 0.384303 23.9018C1.15601 27.7814 3.06082 31.3451 5.85787 34.1421C8.65492 36.9392 12.2186 38.844 16.0982 39.6157C19.9778 40.3874 23.9991 39.9913 27.6537 38.4776C31.3082 36.9638 34.4318 34.4004 36.6294 31.1114C38.827 27.8224 40 23.9556 40 20C40 14.6957 37.8929 9.60859 34.1421 5.85786C30.3914 2.10714 25.3043 0 20 0ZM25.5539 21.5385L17.8615 30C17.6576 30.2244 17.4114 30.4064 17.137 30.5356C16.8627 30.6647 16.5656 30.7386 16.2627 30.7529C15.9598 30.7672 15.657 30.7216 15.3717 30.6188C15.0864 30.516 14.8242 30.358 14.6 30.1538C14.3756 29.9499 14.1936 29.7037 14.0644 29.4293C13.9353 29.155 13.8614 28.8579 13.8471 28.555C13.8328 28.2521 13.8784 27.9493 13.9812 27.664C14.084 27.3787 14.242 27.1165 14.4462 26.8923L20.7231 20L14.4462 13.0923C14.1001 12.6314 13.939 12.0576 13.9947 11.4839C14.0504 10.9102 14.3188 10.3781 14.747 9.99224C15.1753 9.60644 15.7324 9.39486 16.3088 9.39914C16.8852 9.40342 17.4391 9.62325 17.8615 10.0154L25.5539 18.4769C25.928 18.899 26.1346 19.4436 26.1346 20.0077C26.1346 20.5718 25.928 21.1163 25.5539 21.5385Z" fill="white"/>
                    </svg>
                </button>
                 : ''} 
        </div>
    );
};

export default PrevNext;
