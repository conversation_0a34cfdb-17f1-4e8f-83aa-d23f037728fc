variables:
  GIT_STRATEGY: none

stages:
  - deploy
  - run


deploy-to-dev:
  stage: deploy
  script:
     - ssh -o StrictHostKeyChecking=no intermsh@************** "(cd /home3/indiamart/public_html/dev-forms_react; git pull;git status)"
     - eval $(ssh-agent -k)
  only:
     - development
     
deploy-to-stage:
  stage: deploy
  script:
     - ssh -o StrictHostKeyChecking=no intermsh@************** "(cd /home3/indiamart/public_html/stg-forms_react; git pull;git status)"
     - echo "Kill Ssh-agent"
     - eval $(ssh-agent -k)
  only:
     changes:
      - fileVersions.json

run-to-dev:
  stage: run
  script:
     - ssh -o StrictHostKeyChecking=no intermsh@************** "(cd /home3/indiamart/public_html/dev-forms_react; npm start;)"
     - eval $(ssh-agent -k)
  only:
     - development

run-to-stage:
  stage: run
  script:
     - ssh -o StrictHostKeyChecking=no intermsh@************** "(cd /home3/indiamart/public_html/stg-forms_react; npm start;)"
     - eval $(ssh-agent -k)
  only:
     changes:
      - fileVersions.json

deploy-to-production:
 stage: deploy
 script:
    - ssh -o StrictHostKeyChecking=no intermsh@************* "(cd /home3/indiamart/public_html/prod-forms_react; git pull;git status)"
    - ssh -o StrictHostKeyChecking=no intermsh@************ "(cd /home3/indiamart/public_html/prod-forms_react; git pull;git status)"
    - echo "Kill Ssh-agent"
    - eval $(ssh-agent -k)
 only:
    - production 

run-to-production:
  stage: run
  script:
     - ssh -o StrictHostKeyChecking=no intermsh@************* "(cd /home3/indiamart/public_html/prod-forms_react; npm start;)"
     - ssh -o StrictHostKeyChecking=no intermsh@************ "(cd /home3/indiamart/public_html/prod-forms_react; npm start;)"
     - eval $(ssh-agent -k)
  only:
     - production
