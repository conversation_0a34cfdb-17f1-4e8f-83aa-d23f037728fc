import { isset } from "../common/formCommfun";

export default async function getMcatDetail (mcatMatch,form_param) {  
        const webAddressLocation = location.hostname;
        const ServerName = webAddressLocation.match(/^(dev)/) ? "dev-":(webAddressLocation.match(/^stg/)?"stg-":"");
        const url =  "https://"+ServerName+`apps.imimg.com/models/mcatid-suggestion.php?search_param=${encodeURIComponent(mcatMatch)}`;
        try {
            const response = await fetch(url, {
              method: 'GET',
              mode: 'cors', 
              cache: 'no-store'
            });
      
          if (!response.ok) {
            form_param.mcatId = -1;
            form_param.catId = -1;
            form_param.mcatType = "";
            return;
          }
          const responseData = await response.json();
          if (responseData) {
            form_param.mcatId = isset(()=>responseData["mcatid"])
              ? responseData["mcatid"]
              : -1;
            form_param.catId = isset(()=>responseData["catid"]) ? responseData["catid"] : -1;
            form_param.mcatType = isset(()=>responseData["type"]) ? responseData["type"] : "";
            form_param.prodDispName = "";
            form_param.mcatName = "";
            form_param.prodServ = isset(()=>responseData["type"]) ? responseData["type"] : "";
          } else {
            form_param.mcatId = -1;
            form_param.catId = -1;
            form_param.mcatType = "";
          }
        } catch (error) {
            form_param.mcatId = -1;
            form_param.catId = -1;
            form_param.mcatType = "";
            imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'mcatid-suggestion-failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
            return;
        }
    }