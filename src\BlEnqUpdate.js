import { getparamValREC, isset, readCookieREC, reqFormGATrackREC,isPresent } from "./common/formCommfun";
export default async function BlEnqUpdate(form_param , queryid , queryDestination ,state) {
    const ObjectTrim = (object) => {
        let trimmedObject = {};
        for (let key in object) {
          if (typeof object[key] === 'string') object[key] = object[key].trim();
          if (object[key] !== '') trimmedObject[key] = object[key];
        }
        return trimmedObject;
      };
    const getBlEnqData = () => {
        let data = {};
        let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
        let iploccookie = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
      
        data['modId'] = window.forms_param.modId;
        data['s_ip'] = getparamValREC(iploccookie, 'gip');
        data['s_ip_country'] = getparamValREC(iploccookie, 'gcnnm');
        data['s_ip_country_iso'] = getparamValREC(iploccookie, 'gcniso');
        data['s_first_name'] = getparamValREC(imeshcookie, 'fn') || "";
        data['s_email'] = getparamValREC(imeshcookie, 'em') || "";
        data['s_mobile'] = getparamValREC(imeshcookie, 'mb1') || "";
        let glusrid  = getparamValREC(imeshcookie, 'glid');
        data['s_glusrid'] = glusrid;
        data['s_country_name'] = getparamValREC(iploccookie, 'gcnnm');
        isset(()=>state.md_resp) && isset(()=>state.md_resp[glusrid])
        data['generationId'] = queryid;
        data['s_city_id'] =  getparamValREC(imeshcookie, 'ctid') && getparamValREC(imeshcookie, 'ctid') != "1" ? getparamValREC(imeshcookie, 'ctid') : "";
        data['flag'] =window.forms_param.formType;
      
        if (data['s_country_name'] == 'India' && state.userDetails) {
          data['s_state_name'] =
            isset(()=>state.userDetails['statename']) &&
            state.userDetails['statename'] !== ''
              ? state.userDetails['statename']
              : '';
        }
        if (window.forms_param.formType === 'Enq') {
          data['r_glusrid'] = form_param.rcvGlid;
          data['rfq_queryDestination'] = queryDestination;
        }
      
        return ObjectTrim(data);
      };
    const appsServerName = location.hostname.match(/^dev/) ? "//dev-apps.imimg.com/" : location.hostname.match(/^stg/) ? "//stg-apps.imimg.com/" : "//apps.imimg.com/";
    const data = getBlEnqData();
    const queryParams = new URLSearchParams(data);
    const url = `${appsServerName}index.php?r=Newreqform/BLEnqUpdate&${queryParams}`;
    try {
        const response = await fetch(url,
            {method: 'GET',
            mode: 'cors', 
            cache: 'no-store'});
        if (!response.ok) {
          reqFormGATrackREC("service:PostBlenqUpdate:failure ",form_param);
            throw new Error('Network response was not ok');
            
        }
        const data = await response.json();
        reqFormGATrackREC("service:PostBlenqUpdate:success " ,form_param);
        return data;
    } catch (error) {
        // Handle errors
        reqFormGATrackREC("service:PostBlenqUpdate:failure ", form_param);
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'BlEnqUpdate','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}