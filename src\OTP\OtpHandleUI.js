import {isset,readCookieREC,updateFulllogin,cookieDecodeRE<PERSON>,setCookieREC, deleteCookieRE<PERSON>, checkblockedUser, IsCookieHaveValue} from '../common/formCommfun';
import UserVerAPI from './UserVerAPI';
export default async function OtpHandleUI(resend,type,otpentered,setErrordisp,disableres,setShouldFocus,clearMyTimeout,setValidated ,form_param) {
  let verified = '';
  // Function to increment the count
  const incrementotpcountRes = () => {
    window.otpcountRes += 1;
    // console.log('Count:',window.otpcountRes); // This will log the updated count to the console
  };

  const res = await UserVerAPI(resend,type,otpentered);
  // console.log(res);
  if(isset(() => res)){
     window.ratelimitstat = res.Response.Status;
     window.ratelimitmsg = res.Response.Message;
    let rescode=res.Response.Code;
  

    if (type==1 && window.otpcountRes !== 0 && window.ratelimitstat !== "Access Denied") {
      setErrordisp("resend clicked")
    }

    if(window.ratelimitstat == "Access Denied"){
      setErrordisp(window.ratelimitmsg)      
      if ( window.otpcountReser == 1) {
        clearMyTimeout();  // clearTimeout(myTimeout);
      }
      disableres (1);
    }
    if (isset(() => res.Response) &&  window.ratelimitstat == "Success"){
      const imissCookie = isset(() => readCookieREC('imiss')) ? readCookieREC('im_iss') : '';
      let block_case = 1;
        
      //check blocked user
      if(checkblockedUser() && imissCookie== ""){
        block_case=2;
        //add here line
      }

      if (type == 1 ) {
        incrementotpcountRes();
      }else if(type==2){
        verified="V";
        setValidated(true);
        deleteCookieREC("imEqGl");
        updateFulllogin(res);
     }
    }
    else{  //incorrect otp
      if ( rescode == 204 && window.ratelimitmsg == "OTP not Verified") {
        setErrordisp("incorrect");
        setShouldFocus(true);
        // RemoveObjFromHit(verifyObject, tmpId);
        // return;
      }
    }

}

return verified;
}
