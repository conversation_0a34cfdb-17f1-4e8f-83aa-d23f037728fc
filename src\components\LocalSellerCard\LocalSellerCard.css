/* Local Seller Card Styles */
.local-seller-card {
    /* border-radius: 8px; */
    /* box-shadow: 0 2px 8px rgb(130 220 212); */
    margin: 16px 0px;
    overflow: hidden;
    /* width: 100%; */
    /* border: 1px solid #d3e7dc; */
    /* max-width: 515px; */
    /* transition: opacity 0.3s ease, transform 0.3s ease; */
    /* display: flex; */
    flex-direction: row;
    justify-content: center;
    align-items: center;
    /* animation: fadeInUp 0.6s ease-out; */
    /* background-color: #e4f3f2; */
    padding: 3px;
    width: calc(100% + 50px);
    margin-left: -20px;
    text-align: center;
}


/* Text Section */
.text {
  color: #323232;
  padding: 10px 16px 0px;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
}

/* City styling */
.city-name {
  color: #029085; /* Teal green */
  font-weight: 600;
}

/* Company/Seller name link styling */
.seller-name {
  color: #0066cc; /* Professional blue */
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.seller-name:hover {
  color: #0052a3; /* Darker blue on hover */
  text-decoration: underline;
}

/* Product title styling - both clickable and non-clickable should look identical */
.text .product-name-local,
.text .product-name-no-link {
  color: #1f437b; /* Darker blue */
  text-decoration: none;
  font-weight: 500;
  display: inline;
  font-size: inherit;
  line-height: inherit;
}

/* Only add hover effects for clickable version */
.text .product-name-local {
  transition: all 0.3s ease;
}

.text .product-name-local:hover {
  color: #1e3a8a; /* Even darker blue on hover */
  text-decoration: underline;
}

/* Seller name styling - both clickable and non-clickable should look identical */
.text .seller-name,
.text .seller-name-no-link {
  color: #0057af; /* Professional blue */
  font-weight: 500;
  text-decoration: none;
  display: inline;
  font-size: inherit;
  line-height: inherit;
}

/* Only add hover effects for clickable version */
.text .seller-name {
  transition: all 0.3s ease;
}

.text .seller-name:hover {
  color: #0052a3; /* Darker blue on hover */
  text-decoration: underline;
}

/* Confirmation Message */
.confirmation-message {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 12px;
}

.tick-mark {
  background: #007a6e;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  animation: tickAnimation 0.5s ease-in-out;
}

.confirmation-text {
  color: #323232;
  font-size: 15px;
  font-weight: 500;
}

@keyframes tickAnimation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Action Section */
.action-section {
  /* padding: 6px 16px 16px; */
}

.action-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
}

/* Radio Button Styling */
.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.radio-input {
  display: none; /* Hide default radio button */
}

.radio-custom {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-custom::after {
  content: '';
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.3s ease;
}

.radio-label {
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* Yes Radio Button */
.yes-radio {
  border-color: #007a6e;
  background: white;
}

.radio-input:checked + .yes-radio {
  background: #007a6e;
  border-color: #007a6e;
}

.radio-input:checked + .yes-radio::after {
  background: #007a6e;
}

.radio-option:hover .yes-radio {
  border-color: #0d665a;
}

.radio-option .radio-label {
  color: #007a6e;
}

/* No Radio Button */
.no-radio {
  border-color: #bec1c7;
  background: white;
}

.radio-input:checked + .no-radio {
  background: #6b7280;
  border-color: #6b7280;
}

.radio-input:checked + .no-radio::after {
  background: white;
}

.radio-option:hover .no-radio {
  border-color: #9ca3af;
}

.radio-option:has(.no-radio) .radio-label {
  color: #6b7280;
}

/* Smooth entrance animation */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .local-seller-card {
    max-width: 100%;
  }

  .text {
    padding: 12px;
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: row;
    gap: 8px;
    align-items: center;
  }
}

/* Remove .confirmation-message, .tick-mark, and .local-seller-card.unmounting styles */
.confirmation-message, .tick-mark, .local-seller-card.unmounting {
  display: none !important;
}

/* Tick mark inside Yes radio button */
.yes-radio.ticked {
  background: #007a6e;
  border-color: #007a6e;
  position: relative;
}
.yes-radio.ticked .tick-mark-radio {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  position: absolute;
  left: 3px;
  top: 0px;
  pointer-events: none;
}

/* Remove pointer cursor and add visual feedback when No is disabled */
.radio-option input:disabled + .no-radio,
.radio-option input:disabled ~ .radio-label {
  cursor: default;
  opacity: 0.6;
}
.radio-option input:disabled {
  cursor: default;
}

/* Remove pointer cursor for entire No radio option when disabled */
.radio-option.no-disabled {
  cursor: default !important;
}
.radio-option.no-disabled * {
  cursor: default !important;
}

.updatedct{
  visibility: hidden!important;
}