import React from "react";
import { useGlobalState } from '../context/store';

function Enquirenow({form_param}){
    const { state, dispatch } = useGlobalState();

    const handleEnquireClick = async () => {
        dispatch({ type: 'enquirenow', payload: { enquirenow: false } });
    }
    return (
        <>
            <div className="form-group form-group-img"><button type="submit" onClick={handleEnquireClick} className="submit-button-img">Contact Supplier</button></div>      
        </>
    );
    
}
export default Enquirenow;




    