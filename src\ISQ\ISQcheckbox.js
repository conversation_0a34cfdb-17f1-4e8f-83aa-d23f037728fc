import React, { useState,useEffect } from "react";
import { isset } from "../common/formCommfun";

function ISQcheckbox({ item,index ,savecheckbox}) {
  const [checkedItems, setCheckedItems] = useState({});

  // useEffect(() => {
  //   const mcatid = window.forms_param.mcatId; 
  //   let userFilledData = [];
  //       try{
  //         userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
  //       }
  //       catch(e){
  //         userFilledData=[];
  //       }
  
  //   if (isset(()=>userFilledData) && isset(()=>userFilledData[mcatid])) {
  //     const filledData = userFilledData[mcatid];
  
  //     Object.keys(filledData).forEach((key) => {
  //       const value = filledData[key];
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];          
  //         savecheckbox(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         handleCheckboxClick(index,optionIndex);
  //       }
  //     });
  //   } 
  //   else if (window.forms_param && window.forms_param.plsqArr) {
  //     const params = window.forms_param.plsqArr.split('#');
  //     params.forEach(param => {
  //       const [encodedKey, encodedValue] = param.split(':');
  //       const key = decodeURIComponent(encodedKey);
  //       const value = decodeURIComponent(encodedValue);
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];          
  //         savecheckbox(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         handleCheckboxClick(index,optionIndex);
  //       }
  //     });
  //   }
  // }, []);


  const handleCheckboxClick = (index, optionIndex) => {
    const key = `${index}_${optionIndex}`;
    setCheckedItems((prev) => ({
      ...prev,
      [key]: !prev[key], // Toggle the checkbox state
    }));
  };

  return (
    <div>
      {(item.IM_SPEC_MASTER_TYPE === "4") ?
        (
            <div className="row-be txtRw" key={index}>
              <div className="lbtool">
                <div className="beclr"></div>
                <div className="ber-dvtxt bewauto bepr tllb">
                  <label id={`t0901ques${index}`} quesid={item.IM_SPEC_MASTER_ID}>
                    {item.IM_SPEC_MASTER_DESC}
                  </label>
                </div>
                <div className="beclr"></div>
              </div>
              <div className="bemgb15 nwDt">
                {item.IM_SPEC_OPTIONS_DESC.split("##").map((optionDesc, i) => {
                  const checkboxKey = `${index}_${i}`;
                  return (
                    (optionDesc.toLowerCase() === "other" || optionDesc.toLowerCase() === "others") ? 

                    <div className={`chkBox oth_bx other_check`} key={i}>
                    <input
                    type="text"
                    className="ber-input isqoth"
                    name={`t0901other_checkbox_name${index}`}
                    id={`t0901other_checkbox_name${index}_option${i + 1}`}
                    value=''
                    placeholder="Other Option"
                    /> 
                    <label htmlFor={`t0901other_checkbox_name${index}_option${i + 1}`} optionid={item.IM_SPEC_OPTIONS_ID.split("##")[i]} >
                    </label>
                    </div>:
                    <div className={`chkBox sl-box eqchkbx ${checkedItems[checkboxKey] ? 'checkSl' : ''}`} key={i}>
                       <input
                        type="checkbox"
                        className="eqVam bedblk radioClick"
                        name={`t0901checkbox_name${index}`}
                        id={`t0901checkbox_name${index}_option${i + 1}`}
                        value={optionDesc}
                        checked={!!checkedItems[checkboxKey]} 
                        onChange={ () => {handleCheckboxClick(index, i); savecheckbox(optionDesc,item.IM_SPEC_MASTER_DESC,item.IM_SPEC_MASTER_ID,   item.IM_SPEC_OPTIONS_ID.split("##")[i]); }}
                      /> 
                      <label htmlFor={`t0901checkbox_name${index}_option${i + 1}`} optionid={item.IM_SPEC_OPTIONS_ID.split("##")[i]} className="bepr">
                        <div className="bechkin bebr4">
                          <div className="beticked"></div>
                        </div>
                        <span className="bevtCss bedblk beisq3 webKflow">
                          {optionDesc}
                        </span>
                      </label>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : <div></div>
        }
    </div>
  );
}

export default ISQcheckbox;

