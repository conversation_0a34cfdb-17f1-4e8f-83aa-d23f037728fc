export default function getstartindex(form_param,multislider) {

    if (
        form_param.ctaType === "Image" ||
        form_param.ctaType === "Video" ||
        form_param.ctaType === "pdf"
      ) {
        let len = multislider.length;
        for (let i = 0; i < len; i++) {
          if (
            multislider[i]["type"].toLowerCase() ===
            form_param.ctaType.toLowerCase()
          ) {
            if (
              (multislider[i]["displayImage"] === form_param.displayImage && multislider[i]["displayImage"] !== "" && form_param.displayImage !== "") ||
              (multislider[i]["zoomImage"] === form_param.zoomImage && form_param.zoomImage !== "" && multislider[i]["zoomImage"] !== "") ||
              (multislider[i]["vidUrl"] === form_param.vidUrl && multislider[i]["vidUrl"] !== "" && form_param.vidUrl !== "") ||
              (multislider[i]["pdfUrl"] === form_param.pdfurl && multislider[i]["pdfUrl"] !== "" && form_param.pdfurl !== "")
            ) {
              return i;
            }
          }
        }
        return 0;
      }
      return 0;
  

}