import React from 'react';
import { LoginNewUiPDP } from './formCommfun';

const getLogin_Heading = (hash, ctanamemodid, tmpId, ctaname) => {
  if(LoginNewUiPDP(hash)){
    ctanamemodid = "NewLoginUI";
  }
  switch (ctanamemodid) {
    case "NewLoginUI":
      return returnSpanNewUI()
    case "get best quote":
    case "get quote":
      return loginGetmsg(hash, tmpId, ctaname, "");
    case "get latest price":
      return loginGetmsg(hash, tmpId, ctaname, "and details");
    case  "get free download":
      return loginGetmsg(hash, tmpId, ctaname, "and details");
    case "inlineenq":
      return loginDefaultmsg(hash, tmpId, ctaname);
    case "send email":
    case "send sms":
    case "click to call":
      return loginDefaultmsg(hash, tmpId, ctaname, "Connect with ");
    case "contact seller":
    case "contact seller_next":
    case "contact seller_pre":
    case "comvideo":
    case "comphoto":
    case "contact supplier":
      return loginGetmsgSpecialCase(hash, tmpId, "Contact Seller", "and get details");
    case "ask price":
    case "ask for price":
    case "no price":
      return loginGetmsg(hash, tmpId, "Ask for price", "and details");
    case "request a call back":
      return loginGetmsg(hash, tmpId, "", "Request a Call Back ");
    case "contact now":
      return contactLogMsg(hash, tmpId, ctaname, "Contact ");
    case "inactivei":
      return loginGetmsg(hash, tmpId, "Get Latest Price", "and details");
    case "get more photos":
    case "get more photos_next":
    case "get more photos_pre":
      return loginGetmsg(hash, tmpId, "Get More Photos", "and details");
    default:
      return loginDefaultmsg(hash, tmpId, ctaname, "Connect with ");
  }
};

const loginGetmsg = (hash, tmpId, msg, addon) => {
  const usertype = window.countryiso !== "IN" ? "email" : "mobile";
  const hcls = "befwt";
  return (
    <>
      {returnSpan(hash, "", "", msg, hcls)} {addon} from "{hash.rcvName}" on your {usertype} quickly
    </>
  );
};

const loginDefaultmsg = (hash, tmpId, ctaname, msg) => {
  if (isImageVidEnq(hash, tmpId)) {
    return (
      <>
        {returnSpan(hash, "", "", "Get Best Quotes", "befwt")} for {hash.prodName}
      </>
    );
  } else {
    if (hash.ctaName.toLowerCase() === "click to call" || hash.ctaName.toLowerCase() === "view mob e") {
      return <span>Please login to view Supplier's Mobile Number</span>;
    }
    return (
      <>
        {msg}"{returnSpan(hash, "", "", hash.rcvName, "befwt")}"
      </>
    );
  }
};

const loginGetmsgSpecialCase = (hash, tmpId, msg, addon) => {
  const usertype = window.countryiso !== "IN" ? "email" : "mobile";
  return (
    <>
      {returnSpan(hash, "", "", msg, "befwt")} {addon} on your {usertype} quickly
    </>
  );
};

const contactLogMsg = (hash, tmpId, ctaname, msg) => {
  return (
    <>
      {msg}"{hash.rcvName}" for more information
    </>
  );
};

const returnSpan = (hash, id, className, text, hcls) => {
  return <span id={id} className={`${className} ${hcls}`}>{text}</span>
};
const returnSpanNewUI = () => {
  return <>
    <span className='newhdcls'>Login to get best deals instantly</span>
    <span className="subhdcls">Just One Step to Connect with Verified Seller</span>
  </>;
};

const isImageVidEnq = (hash, tmpId) => {
  return hash.formType.toLowerCase() === "enq" &&
    isSet(hash.typeofform) &&
    (hash.typeofform.toLowerCase() === "image" ||
      hash.typeofform.toLowerCase() === "video")
    ? true
    : false;
};

const isSet = (value) => {
  return value !== null && value !== undefined;
};

// Main Component
const Login_Heading = ({hash, id}) => {
  let ctanamemodid = hash.ctaName.toLowerCase().trim();
  return (
    <div className="ber-hdg-r">
      {getLogin_Heading(hash, ctanamemodid, hash.tmpId, hash.ctaName)}
    </div>
  );
};

export default Login_Heading;
