import { readCookieRE<PERSON>, isset, getparam<PERSON>alRE<PERSON>, setCookieREC,isPresent } from '../common/formCommfun';
import IntGenApi from "../main/IntGenApi";
// import PostReq from "../PostReqApi";
import LoginApiCall from './LoginApiCall';
import GlusrUpdate from "./GlusrUpdate";
import callMiniDetAPI from '../MinidtlsAPI';
export function validatePhoneNumber(phoneNumber) {
    // Reset error message
    let err_msg = '';

    // Check if phone number is not blank and has a length of 10
    if (!phoneNumber) {
      err_msg = 'Please enter Mobile number';
    }else if (!/^\d+$/.test(phoneNumber)) {
      err_msg = 'Phone number must contain only digits.';
    }else if (phoneNumber.match(/^[0,2,3,4,5]/)) {
      err_msg = 'Phone number must start with 6, 7, 8, or 9.';
    }else if (phoneNumber.length !== 10) {
      err_msg = 'Phone number must be 10 digits long.';
    }

    // If all conditions are met, phone number is valid
    return err_msg;
}
export function validateEmailId(email) {
    let err_msg = '';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
        err_msg = 'Email address cannot be blank.';
    }else if (!emailRegex.test(email)) {
        err_msg = 'Invalid email address.';
    }

return err_msg 
}
export function validateName(name) {
  let err_msg = '';
  if (!name) {
    err_msg = 'Please enter you name';
  }

return err_msg 
}
export function validpfem(country_iso, id){
  let current_elem = country_iso == "IN"? `t${id}_login_field_mob` :`t${id}_login_field_email`
  let fld_val = document.getElementById(current_elem) ? document.getElementById(current_elem).value : "";
  let valid_res = country_iso == "IN"? validatePhoneNumber(fld_val):validateEmailId(fld_val);
  return {valid_res, fld_val};
    
}
export async function identifiedcall(country_iso, id, fld_val, form_param,seterr_msg){
  let identresponse = {} 
    try {
        const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
        const s_ip = getparamValREC(iploc, 'gip');
        const s_ip_country = getparamValREC(iploc, 'gcnnm');
        const s_ip_country_iso = getparamValREC(iploc, 'gcniso');
        const iploc_arr = {'s_ip': s_ip,'s_ip_country': s_ip_country,'s_ip_country_iso': s_ip_country_iso,}

        const success = await LoginApiCall(form_param, fld_val,"",iploc_arr.s_ip,country_iso) ;
        if(form_param.isEcom !== 1){
        if(success && success.code == 200){
          identresponse.DataCookie = success.DataCookie;
          identresponse.iso = success.iso;
          if ((country_iso == "IN" || (success.DataCookie.fn != "" ))) {
            identresponse.servresp= true;
            let afterlogindata = await AfterLoginhit(iploc_arr, form_param,success.DataCookie);
            // identresponse.postreq= afterlogindata.postreq;
            identresponse.NECcon= afterlogindata.NECcon;
            identresponse.OTPcon= afterlogindata.OTPcon;
            identresponse.Isqform= afterlogindata.Isqform;
              // dispatch({ type: 'Imeshform', payload: { Imeshform: true } });
            identresponse.Imeshform = true;
            identresponse.UserName = success.DataCookie.fn;
          } else if (success.DataCookie.fn == "") {
              identresponse.UserName = success.DataCookie.fn;
              let vldname = validateName(document.getElementById(`t${id}_q_first_nm1`).value);
              if(vldname !== ""){
                identresponse.vldname = vldname;
                  // setname_err(vldname);
              }else{
                  // setname_err("");
                  let usr_name = document.getElementById(`t${id}_q_first_nm1`).value;
                  let user_data = { glid: success.DataCookie.glid, name: usr_name, s_email: fld_val, country_iso:country_iso, flds_mobile_val: fld_val}
                  // setCookieREC('ImeshVisitor', success.DataCookie, 365);
                  identresponse.glusrdata = await GlusrUpdate(form_param, user_data, success.DataCookie);
                  identresponse.servresp= true ;
                  let afterlogindata = await AfterLoginhit(iploc_arr, form_param, id);
                  // identresponse.postreq= afterlogindata.postreq;
                  identresponse.NECcon= afterlogindata.NECcon;
                  identresponse.OTPcon= afterlogindata.OTPcon;
                  identresponse.Isqform= afterlogindata.Isqform;
                  identresponse.Imeshform = true;
              }
              // dispatch({ type: 'Imeshform', payload: { Imeshform: false } });
          }else if (success.code != 200) {
            identresponse.servresp = false;
            identresponse.UserName = success.DataCookie.fn;
            seterr_msg(success.msg );
          } else {
            console.log("Error");
              identresponse.Imeshform = false;
              return false;
              
          }

        }       
        else{
          console.log("Error");
          identresponse.Imeshform = false;
          seterr_msg(success.msg );
          return false;

          } 
        } 
      
        return identresponse;
    } catch (error) {
      identresponse.Imeshform = false;
        console.error("Error:", error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'ValidNmEm','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }

}

export async function AfterLoginhit(iploc_arr, form_param , imeshcookie){
  let afterlogindata = {}
  const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
  const glid = getparamValREC(imesh, 'glid');
  if(imesh != ""){
    let mdtlres = null;
    let city = "";
    try {
      mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
    }
    catch (e) {
      mdtlres = null;
    }
    if (mdtlres && mdtlres[getparamValREC(imesh, "glid")]) {
      city = isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[0]) ? mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] : "";
    } else {
      if (getparamValREC(imesh, "glid") != "") {
        const data = await callMiniDetAPI(form_param);
        city = data && data.Response && data.Response.Data &&  isPresent(data.Response.Data[0])? data.Response.Data[0] : "";
      }
    }
    const visitorFn = imeshcookie.fn;
    const visitorMb = imeshcookie.mb1;
    const visitorCity = imeshcookie.ctid ? imeshcookie.ctid : city;
    const visitorIso = imeshcookie.iso;
    const uv = imeshcookie.uv; 
    // const paramobjpostreq = {
    //     name: visitorFn,
    //     s_city_name: visitorCity,
    //     glid: glid,
    //     s_country_iso: iploc_arr.s_ip_country_iso,
    //     s_ip_country: iploc_arr.s_ip_country,
    //     s_ip: iploc_arr.s_ip,
    // }  
    // let resdta = ""; 
    // if(visitorFn ==""){
        IntGenApi(form_param, "loginscreen");
    // }
    // else{
    //     resdta = await PostReq(form_param,paramobjpostreq);
    //     if(resdta.queryid!="" &&  resdta.queryid!=undefined){
    //         afterlogindata.postreq = resdta.queryid;
    //         window.rfq_queryDestinationRec = resdta.query_destination;
    //         window.queryid = resdta.queryid;
    //     }
    // }                     
    const nec_con = (visitorFn =='' || visitorCity =='') || (visitorMb =='' && visitorIso !='IN') ? true : false;
    const otp_con = (uv=='' && visitorIso == 'IN') ? true : false;
    if(  nec_con == true){
      afterlogindata.NECcon = true;
    }
    else{
        afterlogindata.NECcon = false;
        if(otp_con==true){
          afterlogindata.OTPcon = true;
        }
        else{
          afterlogindata.OTPcon = false;
          afterlogindata.Isqform = true;
        }
    }
      
  }
  return afterlogindata;
}