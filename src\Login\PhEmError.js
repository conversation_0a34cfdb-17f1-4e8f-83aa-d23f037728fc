import React from 'react';
import { isInlineBl } from '../common/formCommfun';
function PhEmError({form_param, id, err_msg}){
    let blerrcomp = false;
    let err_cls = "beerrp";
    if(isInlineBl(form_param) || id =='0401'){
        blerrcomp = true;
        if(id =='0401'){
            err_cls = "errcls inenqerr";
        }else{
            err_cls = "errcls inblerr";
            if(err_msg == "Enter product/service name"){
                err_cls = "errcls prodnmerr";
            }
        }
    }else if(form_param.formType == "BL"){
        err_cls = "errcls prodnmerrpopup";
        blerrcomp = true;
    }

    return(
        <div className={`ber-erbx ${err_cls}`}>
            <div data-role="content">{err_msg}</div>
            {blerrcomp && <a className="ber-erarw" data-role="arrow"></a>}
        </div> 
    )
}
export default PhEmError;