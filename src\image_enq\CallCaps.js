
import {isset} from '../common/formCommfun';
import CapsAPI from './Capsapi';



export default async function CallCaps(form_param){

    window.capsres='';    
    const res = await CapsAPI(form_param);  
    if(isset(() => res) && isset(() => res["STATUS"]) && res["STATUS"] == 200 && isset(() => res["RECOMMMENDATON_PRODUCT_BASED"]) && res["RECOMMMENDATON_PRODUCT_BASED"].length > 0){
        window.CapsData = res["RECOMMMENDATON_PRODUCT_BASED"]; 
    }
    return window.CapsData ;

  }
  