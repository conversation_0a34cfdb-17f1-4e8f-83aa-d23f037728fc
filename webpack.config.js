const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const path = require("path");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const deps = require('./package.json').dependencies;
const fileVersion = require('./fileVersions.json')
const Version = fileVersion['Version'];
const chunkVersion = fileVersion['chunkVersion'];

const mode = process.env.NODE_ENV === 'production' ? 'production' : 'development';
const getShortName = (name) => {
  const parts = name.split('_');
  return parts.length > 1 ? parts[parts.length - 2] : name;
};
 
const envChecker = (function () {
  let envObj = {};
  if(typeof process !== 'undefined' && process.env.environ == 'prod'){
    envObj.mode = "production";
    envObj.buildPath = "/home3/indiamart/public_html/apps-imimg-com/forms_react";
  }
  else if(typeof process !== 'undefined' && process.env.environ == 'stg'){
    envObj.mode = "production";
    envObj.buildPath = "/home3/indiamart/public_html/stg-apps-imimg-com/forms_react";
  }
  else if(typeof process !== 'undefined' && process.env.environ == 'dev'){
    envObj.mode = "development";
    envObj.buildPath = "/home3/indiamart/public_html/dev-apps-imimg-com/forms_react";
  }
  else{
    envObj.mode = "development";
    envObj.buildPath = path.resolve(__dirname, 'public');
  }
  return envObj;
})();

module.exports = {
  mode: envChecker.mode,
  entry: './src/main/OpenForm.js',
  output: {
    path: envChecker.buildPath,
    filename: `[name].bundle-${chunkVersion}.js`,
    chunkFilename: ({ chunk }) => {
      const chunkName = chunk.id || path.basename(chunk.filename, '.js');
      return `${getShortName(chunkName)}-${chunkVersion}.js`;
    },
  },
  resolve: {
    modules: [
        path.join(__dirname, "src"),  // Adjusted path
        "node_modules"
    ],
    extensions: ['*', '.js', '.jsx']
  },
  devServer: {
    port: 3012,
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'sideApp',
      filename: `remoteEntry-${Version}.js`,
      exposes: {
        './page': './src/main/OpenForm',
      },
      shared: {
        ...deps,
        "react": {
          singleton: true,
          requiredVersion: deps.react,
        },
      }
    }),
    new MiniCssExtractPlugin({
      ignoreOrder: true,
      chunkFilename: ({ chunk }) => {
        // Use chunk.id if available, otherwise derive from chunk.filename
        const chunkName = chunk.id || path.basename(chunk.filename, '.css');
        return `${getShortName(chunkName)}-${chunkVersion}.css`;
      },
    }),
  ],
  module: {
    rules: [
        {
          loader: "babel-loader",
          options: {
            configFile: path.join(process.cwd(), "./.babelrc"),
          },
          test: /\.jsx?$/,
          exclude: /(node_modules|bower_components)/,
        },
        {
            test: /\.css$/,
            use: [
              {
                  loader: MiniCssExtractPlugin.loader,
                  options: {
                      esModule: true,
                  },
              },
              'css-loader',
            ],
          },
      ],
  },
  optimization: {
    chunkIds: 'named', // Ensure named chunk IDs
  },
}
