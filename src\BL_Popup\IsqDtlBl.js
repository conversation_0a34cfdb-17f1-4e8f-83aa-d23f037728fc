import React, { useState, useEffect } from 'react';
import ISQAPICall from '../ISQ/ISQAPICall';
import Head_scr from '../common/heading';
import IsqQues from '../ISQ/IsqQues';
import SubmitButtonISQ from '../ISQ/SubmitButtonISQ';
import { isset, Eventtracking} from '../common/formCommfun';
import { useGlobalState } from '../context/store';

function IsqDtlBl({ form_param ,blsearchval , seterr_msg }) {
    const [responseData, setResponseData] = useState(null);
    const { state, dispatch } = useGlobalState();
    const [selectedOptions, setSelectedOptions] = useState([]); // State to hold selected options
    const [selectedOptscr, setSelectedOptscr] = useState([]); 
    const [cansubmit, setCansubmit] = useState(1);
    const [isqQuesIndices, setIsqQuesIndices] = useState([0, 1, 2]);
    const [isqleft, setisqleft] = useState(1);
    const [firstscreen, setFirstscreen] = useState('');
    const [warning, setWarning] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            try {
                const data = await ISQAPICall(form_param);
                setResponseData(data);
            } catch (error) {
                console.error("Error fetching ISQ data:", error);
                imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'ISQApiFetch_failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
                // Handle error
                dispatch({ type: 'IsqformBL', payload: { IsqformBL: false } });
                dispatch({ type: 'RDformBL', payload: { RDformBL: true } });
            }
        };

        fetchData();
    }, [state.openBLPopup]);

    useEffect(() => {
        if ((responseData && (responseData.CODE != 200 || !responseData.DATA || responseData.DATA.length === 0)) ) {
            dispatch({ type: 'IsqformBL', payload: { IsqformBL: false } });
            dispatch({ type: 'RDformBL', payload: { RDformBL: true } });                
            
        }
    }, [responseData]);  




    const savecheckbox = (optionval, q_desc, q_id, optionid) => {
        optionval=isset(()=>optionval) ? optionval : '';
        q_desc=isset(()=>q_desc) ? q_desc : '';
        q_id=isset(()=>q_id) ? q_id : '';
        optionid=isset(()=>optionid) ? optionid : '';
        
        if (optionval !== '') {
            setSelectedOptions(prevOptions => {
                const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id && opt.b_response === optionval);
    
                let updatedOptions = [...prevOptions];
    
                if (existingIndex !== -1) {
                    // Remove the existing option
                    updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
                } else {
                    // Add a new option
                    updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
                }
    
                // console.log(updatedOptions);
                return updatedOptions;
            });
            setSelectedOptscr(prevOptions => {
                const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id && opt.b_response === optionval);
    
                let updatedOptions = [...prevOptions];
    
                if (existingIndex !== -1) {
                    // Remove the existing option
                    updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
                } else {
                    // Add a new option
                    updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
                }
    
                // console.log(updatedOptions);
                return updatedOptions;
            });
        }
    };


    const saveIsqs = (optionval, q_desc, q_id, optionid) => {
        optionval=isset(()=>optionval) ? optionval : '';
        q_desc=isset(()=>q_desc) ? q_desc : '';
        q_id=isset(()=>q_id) ? q_id : '';
        optionid=isset(()=>optionid) ? optionid : '';
        setSelectedOptions(prevOptions => {
            const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id);
    
            let updatedOptions = [...prevOptions];
    
            if (existingIndex !== -1) {
                updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
            } 
            if (optionval !== '') {
                updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
            }
    
            // console.log(updatedOptions);
            return updatedOptions;
        });
        setSelectedOptscr(prevOptions => {
            const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id);
    
            let updatedOptions = [...prevOptions];
    
            if (existingIndex !== -1) {
                updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
            } 
            if (optionval !== '') {
                updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
            }
    
            // console.log(updatedOptions);
            return updatedOptions;
        });
    };

    const saveQt = (quantity, quantityUnit, quantityMasterId, quantityOptionId, unitMasterId, unitOptionId) => {
        quantityMasterId= isset(()=>quantityMasterId) ? quantityMasterId : '';
        quantityOptionId= isset(()=>quantityOptionId) ? quantityOptionId : '';
        unitMasterId= isset(()=>unitMasterId) ? unitMasterId : '';
        unitOptionId= isset(()=>unitOptionId) ? unitOptionId : '';
        setSelectedOptions(prevOptions => {
            const updatedOptions = [...prevOptions];
    
           
                const existingQuantityIndex = updatedOptions.findIndex(opt => opt.q_id === quantityMasterId);
                if (existingQuantityIndex !== -1) {
                    updatedOptions.splice(existingQuantityIndex, 1);
                }
            if (quantity !== '') {    
                updatedOptions.push({ b_response: quantity, q_desc: 'Quantity', q_id: quantityMasterId, b_id: quantityOptionId });
            }
    
            
                const existingUnitIndex = updatedOptions.findIndex(opt => opt.q_id === unitMasterId);
                if (existingUnitIndex !== -1) {
                    updatedOptions.splice(existingUnitIndex, 1);
                }
                if (quantity !== '' && quantityUnit !== '') {
                updatedOptions.push({ b_response: quantityUnit, q_desc: 'Quantity Unit', q_id: unitMasterId, b_id: unitOptionId });
            }
    
            // console.log(updatedOptions);
            return updatedOptions;
        });
        setSelectedOptscr(prevOptions => {
            const updatedOptions = [...prevOptions];
    
           
                const existingQuantityIndex = updatedOptions.findIndex(opt => opt.q_id === quantityMasterId);
                if (existingQuantityIndex !== -1) {
                    updatedOptions.splice(existingQuantityIndex, 1);
                }
            if (quantity !== '') {    
                updatedOptions.push({ b_response: quantity, q_desc: 'Quantity', q_id: quantityMasterId, b_id: quantityOptionId });
            }
    
            
                const existingUnitIndex = updatedOptions.findIndex(opt => opt.q_id === unitMasterId);
                if (existingUnitIndex !== -1) {
                    updatedOptions.splice(existingUnitIndex, 1);
                }
                if (quantity !== '' && quantityUnit !== '') {
                updatedOptions.push({ b_response: quantityUnit, q_desc: 'Quantity Unit', q_id: unitMasterId, b_id: unitOptionId });
            }
    
            // console.log(updatedOptions);
            return updatedOptions;
        });
    };
    const handleIndexChange = (val) => {
        if(val){
            let a=3*val;
            setIsqQuesIndices([a, a+1, a+2]);
            setisqleft(val+1);
        }
    };
    

    let res = responseData;

    // if(isset(()=>res) && isset(()=>res.DATA) && currentISO()!='IN'){
    //     res.DATA=res.DATA.slice(0, 1);
    // }
    useEffect(() => {
        if (isset(() => res) && isset(() => res.DATA)) {
            const totalQuestions = res.DATA.length;
            const startIdx = isqQuesIndices[0];
            const endIdx = Math.min(startIdx + 3, totalQuestions);
            
            let trackingStr = `DS${window.screencount}-ISQ`;
            
            for (let i = startIdx; i < endIdx; i++) {
                const isQuantity = res.DATA && res.DATA[i] && res.DATA[i].length === 2 ? 'quantity' : '';
                if(i==0 ){
                    isQuantity ? setFirstscreen('quantity') : setFirstscreen('other');
                }   
                trackingStr += `-ISQ${i + 1}` + (isQuantity ? `-${isQuantity}` : '');
            }
            
            Eventtracking(trackingStr, state.prevtrack, form_param, false);
            dispatch({ type: 'currentscreen', payload: { currentscreen: trackingStr } });
        }
    }, [responseData, isqQuesIndices]);
    return (
        <div className="form-container">
            {isset(()=>res) && isset(()=>res.DATA) ? (
                <div>
                    {(state.frscr !=1) && <Head_scr scr={"isqBL"} hash={form_param}/>}
                    {(state.frscr !=1) && isqleft==1 && <div className="eqs16" style={{ marginBottom: '20px' }}>Add a few details to connect with relevant suppliers</div>}
                    {isqleft >= 2 && <div className="eqs16" style={{ marginBottom: '20px' }}>Add a few more details to submit your requirement</div>}
                    <IsqQues data={res.DATA.filter((_, index) => isqQuesIndices.includes(index))} savecheckbox={savecheckbox} saveIsqs={saveIsqs} setCansubmit={setCansubmit} saveQt={saveQt} responseData={responseData} form_param={form_param} warning={warning} setWarning={setWarning}/>
                    <SubmitButtonISQ form_param={form_param} isqsLeft={isqleft} selectedOptions={selectedOptions} setSelectedOptions={setSelectedOptions} selectedOptscr={selectedOptscr} setSelectedOptscr={setSelectedOptscr} handleIndexChange={handleIndexChange} cansubmit={cansubmit} setCansubmit={setCansubmit} remainingisq={res.DATA.length - (3*isqleft)} firstscreen={firstscreen} resdata = {res.DATA.length} blsearchval={blsearchval} seterr_msg={seterr_msg} setWarning={setWarning}/>
                </div>
            ) : (
                <div></div>
            )}
        </div>
    );
}

export default IsqDtlBl;
