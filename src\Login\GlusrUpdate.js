import { readCookieREC, isset, getparamValREC, reqFormGATrackREC ,currentISO, updateimeshCombined, IsCookieHaveValue} from '../common/formCommfun';
import SessionDetailApi from './SessionDeatilApi';
export default async function GlusrUpdate( form_param, user_data) {

    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    const s_ip = getparamValREC(iploc, 'gip');
    const s_ip_country = getparamValREC(iploc, 'gcnnm');
    const s_ip_country_iso = getparamValREC(iploc, 'gcniso');
    const iploc_arr = {'s_ip': s_ip,'s_ip_country': s_ip_country,'s_ip_country_iso': s_ip_country_iso,}
    try {
        const parmObj = {
            s_glusrid: user_data.glid,
            curr_page_url: window.location.href,
            s_ip: iploc_arr.s_ip,
            s_ip_country: iploc_arr.s_ip_country,
            s_ip_country_iso: iploc_arr.s_ip_country_iso,
            flag: form_param.formType,
            modid: form_param.modId,
            s_country_iso: user_data.country_iso,
            _: new Date().getTime()
        };
        
        const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
        if(isset(()=>user_data.name) &&user_data.name != '' && user_data.name != '1'){
            parmObj.s_first_name = user_data.name;
        }
        if(getparamValREC(imesh, 'sessionKey')){
            parmObj.SESSION_KEY = getparamValREC(imesh, 'sessionKey');
        }
       if(isset(()=>user_data.s_email) &&user_data.s_email != '' && user_data.s_email != '1'){
            parmObj.s_email = user_data.s_email;
        }
        if(isset(()=>user_data.s_mobile) &&user_data.s_mobile != '' && user_data.s_mobile != '1'){
            parmObj.s_mobile = user_data.s_mobile;
        }
        if(isset(()=>user_data.cityName) && user_data.cityName != ''){
            parmObj.s_city_name = user_data.cityName;
        }
        let app='';
        let scrn=''
        if(isset(()=>user_data.companyName) && user_data.companyName != ''){
            parmObj.s_companyName = user_data.companyName;
            scrn+='CompanyName/';
        }  
       
        if((window.userdata && window.userdata.iso ? window.userdata.iso : currentISO())=='IN'){
            if(isset(()=> user_data.gst) &&  user_data.gst != ''){
                parmObj.gst =  user_data.gst;
                scrn+='GST/';
            }                          
            app=" Desktop Enquiry/BL Forms";           
        }
        else{   
            if(isset(()=> user_data.url) &&  user_data.url != ''){
                parmObj.url =  user_data.url;
                scrn+='URL/';
            }
            app=" Desktop Enquiry/BL Forms Foreign"
        }  
        if (scrn.endsWith('/')) {
            scrn = scrn.slice(0, scrn.lastIndexOf('/'));
          }    
        if(scrn!=''){
            parmObj.scrnNm= scrn + app ;
        }    


        const queryParams = new URLSearchParams(parmObj);
        const webAddressLocation = location.host
        const ServerName = webAddressLocation.match(/^(dev)/)? "dev-":(webAddressLocation.match(/^stg/)?"stg-":"");
        const response = await fetch(`https://${ServerName}apps.imimg.com/index.php?r=Newreqform/GlusrUpdate&${queryParams}`);
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        let data = await response.json();
        reqFormGATrackREC("service:GlusrUpdate:success",form_param);
        if (typeof getLoginStringv1 === "function") getLoginStringv1();
        if(IsCookieHaveValue() && data && (data.fname || data.lname || data.mobile || data.user_email)){
            if(typeof maskimesh === "function") maskimesh({"fn" : data.fname, "ln": data.lname, "mb1" : data.mobile, "em" : data.user_email});
        }
        const imeshvis = readCookieREC('ImeshVisitor');
        if(imeshvis && getparamValREC(imeshvis, 'sessionKey') && data && data.msg && data.msg.MESSAGE && data.msg.MESSAGE.CODE == 200){
            data = await SessionDetailApi(getparamValREC(imeshvis, 'sessionKey'),form_param.modId);    
        }
        else{
            data.DataCookie = updateimeshCombined(imeshvis,false);
        }
        return data;
        
        
        
    } catch (error) {
        // Handle errors
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Glusr_fetch','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}
