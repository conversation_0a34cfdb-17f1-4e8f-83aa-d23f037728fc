import React, { useState, useEffect,useRef } from "react";
import { isset, reqFormGATrackREC } from '../common/formCommfun';
import PrevNext from './PrevNext';
import SliderMulti from "./SliderMulti";
import Slider<PERSON>ingle from "./SliderSingle";
import { useGlobalState } from "../context/store";
import './imgEnq.css';
import VideosFn from "./VideosYtFbIns";

function ImgLeft_sec({ form_param, id, blurImg, incrementProdCount, prodCountRef, download, setDownload, setReset, reset, toBlur}) {
    const { state, dispatch } = useGlobalState();
    let imageUrl = isset(() => form_param.displayImage) ? form_param.displayImage : isset(() => form_param.zoomImage) ? form_param.zoomImage : "";
    const [currentImage, setCurrentImage] = useState(imageUrl);
    const [isZoomed, setIsZoomed] = useState(false);
    const [transform, setTransform] = useState("translate(-50%, -50%) scale(1)");
    const [cursor, setCursor] = useState("default");
    const [disabled, setDisabled] = useState(false);
    const [piframe, setPiframe] = useState(false);
    const [loader, setLoader] = useState(false);
    const [imgHeight, setImgHeight] = useState('10');
    const [imgHeightadd, setImgHeightadd] = useState('10');
    const [lowerresol, setLowerresol] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);




    if (typeof window.IframeApiloaded === "undefined") window.IframeApiloaded = 0;

    const makeBlur = () => {
        window.downBlur = 1;
        blurImg(true);
        incrementProdCount(0);
    }

    const fullscreen = () => {
        createFullscreenDiv();
        reqFormGATrackREC("fullscreenButton Clicked",form_param);
    };

    const closeFullscreen = () => {
        const fullscreenDiv = document.getElementById('fullscreen-container');
        if (fullscreenDiv) {
            fullscreenDiv.parentNode.removeChild(fullscreenDiv);
        }
    };

    const createFullscreenDiv = () => {
        const fullscreenDiv = document.createElement('div');
        fullscreenDiv.id = 'fullscreen-container';
        fullscreenDiv.className = 'fullscreen-mode';

        const backButton = document.createElement('button');
        backButton.innerText = 'Go Back';
        backButton.className = 'back-button';
        backButton.onclick = closeFullscreen;
        const fullscreenImage = document.createElement('img');
        fullscreenImage.src = currentImage;
        fullscreenImage.className = 'fullscreen-image';

        fullscreenDiv.appendChild(fullscreenImage);
        fullscreenDiv.appendChild(backButton);
        document.body.insertBefore(fullscreenDiv, document.body.firstChild);
    };




    const downloadImage = () => {
        reqFormGATrackREC("Download Clicked",form_param);
        const image = document.getElementById("t0901_zoomimage");
        imageUrl = image.src;
        imageUrl = imageUrl.replace('http://', 'https://');
        fetch(imageUrl,{ cache: "no-cache" })
            .then(response => response.blob())
            .then(blob => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `image_0901.jpg`;
                link.click();
            })
            .catch(error => {
                console.error('Error downloading the image:', error);
                imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'ImageDownload_err', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
            });
    };

    const addEventZoom = () => {
        reqFormGATrackREC("Zoom In Clicked",form_param);
        setDisabled(true);
        setIsZoomed(true);
        setCursor("zoom-in");
        setTransform("translate(-50%, -50%) scale(2)");

    };

    const removeEventZoom = () => {
        reqFormGATrackREC("Zoom out Clicked",form_param);
        setDisabled(false);
        setIsZoomed(false);
        setCursor("default");
        setTransform("translate(-50%, -50%) scale(1)");

    };

    const handleImageChange = (newImageUrl) => {
        setCurrentImage(newImageUrl);
    };

    const handleMouseMove = (event) => {
        if (!isZoomed) return;
        const magnifying_area = document.getElementById("t0901_prodimgR");
        if (isset(() => magnifying_area)) {
            const offsetCor = magnifying_area.getBoundingClientRect();
            let clientX = event.clientX - offsetCor.left;
            let clientY = event.clientY - offsetCor.top;
            const mWidth = magnifying_area.offsetWidth;
            const mHeight = magnifying_area.offsetHeight;



            clientX = (clientX / mWidth) * 100;
            clientY = (clientY / mHeight) * 100;
            setTransform(`translate(-${clientX}%, -${clientY}%) scale(2)`);
        }
    };

    useEffect(() => {
        if (reset) {
            const iframe = document.getElementById("videoProd");
            if (iframe && iframe.tagName === "IFRAME") {
                const div = document.createElement("div");
                div.id = "videoProd";
                div.className = "fL bepr bdr1 pdpHgr";
                iframe.replaceWith(div);
            }
            const divinst = document.getElementById("insf-video");
            if (divinst) {
                const div = document.createElement("div");
                div.id = "videoProd";
                div.className = "fL bepr bdr1 pdpHgr";
                divinst.replaceWith(div);
            }
        }
    }, [reset]);



    useEffect(() => {
        // if (reset) {
        const resol = window.innerWidth;
        const container = document.getElementById(`t${id}_mcontR`);
        const addVal = state.multiImg ? resol <= 1515 ? 103 : 113 : 0;
        const imgHeight = container.offsetHeight;
        // const imgHeight= `${window.innerHeight * 0.9}px`;

        const imgadd = imgHeight + addVal;

        setImgHeight(imgHeight);
        setImgHeightadd(imgadd);

        // }
    }, [reset]);
    useEffect(() => {
        const wd = window.innerWidth;
        const ht = window.innerHeight;
        const delta = wd - ht;
        const diffPer = (delta / wd) * 100;
    
        setLowerresol(prevState => {
            if (diffPer < 50) {
                return true; 
            } else {
                return false;
            }
        });
    }, [reset]);
    


    // useEffect(() => {
    //     if (reset) {
    //         setImgHeight(document.getElementById(`t${id}_mcontR`).offsetHeight);
    //     }
    // }, [reset]); 

    useEffect(() => {
        setReset(true);
        setCurrentImage(imageUrl);
    }, [form_param]);

    useEffect(() => {
        if (download == 1) {
            downloadImage();
        }
    }, [download]);


    useEffect(() => {
        if (form_param.ctaType === 'Video' && isset(() => form_param.vidUrl) && form_param.vidUrl != '') {
            setReset(false);
        }
    }, [form_param.ctaType]);

    useEffect(() => {
        if (reset == false) {
            VideosFn({
                data: {
                    tmpId: '0901',
                    vidUrl: form_param.vidUrl,
                    vidKey: form_param.vidKey,
                },
            }, setPiframe, setReset, setLoader);

        }
    }, [reset]);


    return (
        <>

            <div id={`t${id}_leftS`} className={`ber-Lsc enqImg wdnor posunset imgwid ${lowerresol ? 'smRes' : 'lrgRes'}`} style={{ width: `${imgHeightadd}px` }} >
                {loader ?
                    <div id={`t${id}_belodrVid`} className="belodrbg ">
                        <div className="blLoadR"></div>
                    </div> : <div id={`t${id}_belodrVid`} className="belodrbg dispNone">
                        <div className="blLoadR dispNone"></div>
                    </div>}
                <div id={`t${id}_leftsection`}>
                    <div id={`t${id}_prodmediaR`}>
                        <div id={`t${id}_productimgR`} className="imgslide imgwid" style={{ width: `${imgHeightadd}px` }}>
                            {(state.multiImg) ? <SliderMulti form_param={form_param} onImageChange={handleImageChange} blurImg={blurImg} incrementProdCount={incrementProdCount} prodCountRef={prodCountRef} setPiframe={setPiframe} setReset={setReset} reset={reset} setLoader={setLoader} lowerresol={lowerresol}/> : (state.singleimgsld ? <SliderSingle form_param={form_param} /> : <div id="t0901_slide" className="sLidIgsm" data-role="" style={{ width: '83px', flexShrink: 0 }} ></div>)}

                            <PrevNext form_param={form_param} blurImg={blurImg} incrementProdCount={incrementProdCount} prodCountRef={prodCountRef} setLoader={setLoader} toBlur={toBlur} />


                            <div id="t0901iframeVideo" className={`bepr eIfvm ${reset ? 'dispNone' : piframe ? 'cbl_vh' : ''}`} style={{ width: `${imgHeight - 10}px` }}>
                                <div id="videoProd" className="fL bepr bdr1 pdpHgr">
                                </div>
                            </div>

                            <div id={`t0901_prodimgR`} className={`fL bepr bdr1 pdpHgr ${reset ? '' : 'dispNone'}`} style={{ width: `${imgHeight}px` }} onMouseMove={handleMouseMove} onMouseLeave={() => setTransform("translate(-50%, -50%) scale(1)")} >
                                <div id="interact">
                                    <button id="zoomin" disabled={disabled} onClick={() => addEventZoom()}>
                                        <svg width="25" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 24C14.7731 24 17.3265 23.0594 19.3584 21.4799L27.4394 29.5607C28.0251 30.1464 28.9749 30.1464 29.5607 29.5607C30.1464 28.9749 30.1464 28.0251 29.5607 27.4394L21.4799 19.3584C23.0594 17.3265 24 14.7731 24 12C24 5.37259 18.6275 0 12 0C5.37259 0 0 5.37259 0 12C0 18.6275 5.37259 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM10.5 7.50001V10.5H7.50001C6.67159 10.5 6.00001 11.1716 6.00001 12C6.00001 12.8285 6.67159 13.5 7.50001 13.5H10.5V16.5C10.5 17.3285 11.1716 18 12 18C12.8285 18 13.5 17.3285 13.5 16.5V13.5H16.5C17.3285 13.5 18 12.8285 18 12C18 11.1716 17.3285 10.5 16.5 10.5H13.5V7.50001C13.5 6.67159 12.8285 6.00001 12 6.00001C11.1716 6.00001 10.5 6.67159 10.5 7.50001Z" fill="white" />
                                        </svg>
                                    </button>

                                    <button id="zoomout" disabled={!disabled} onClick={() => removeEventZoom()}>
                                        <svg width="25" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 24C14.7731 24 17.3265 23.0594 19.3584 21.4799L27.4394 29.5607C28.0251 30.1464 28.9749 30.1464 29.5607 29.5607C30.1464 28.9749 30.1464 28.0251 29.5607 27.4394L21.4799 19.3584C23.0594 17.3265 24 14.7731 24 12C24 5.37259 18.6275 0 12 0C5.37259 0 0 5.37259 0 12C0 18.6275 5.37259 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM18 12C18 11.1716 17.3285 10.5 16.5 10.5H7.50001C6.67159 10.5 6.00001 11.1716 6.00001 12C6.00001 12.8285 6.67159 13.5 7.50001 13.5H16.5C17.3285 13.5 18 12.8285 18 12Z" fill="#B8BBC0" />
                                        </svg>
                                    </button>

                                    <button id="fullscreen" onClick={() => fullscreen()}>
                                        <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0 14.1126C0 13.4637 0.524809 12.9389 1.17366 12.9389C1.82252 12.9389 2.35687 13.4637 2.35687 14.1126V15.9828L5.75382 12.5859C6.21183 12.1183 6.95611 12.1183 7.41412 12.5859C7.87214 13.0439 7.87214 13.7882 7.41412 14.2462L4.01718 17.6431H5.87786C6.52672 17.6431 7.06107 18.1679 7.06107 18.8168C7.06107 19.4656 6.52672 20 5.87786 20H1.17366C0.954198 20 0.753817 19.9332 0.582061 19.8378C0.562977 19.8282 0.543893 19.8092 0.534351 19.7996H0.524809L0.505725 19.7901L0.496183 19.7805C0.467557 19.7614 0.438931 19.7424 0.410305 19.7137C0.362595 19.6756 0.324427 19.6279 0.28626 19.5897C0.257634 19.5611 0.23855 19.5229 0.209924 19.4943L0.200382 19.4752L0.19084 19.4656C0.181298 19.4466 0.171756 19.437 0.162214 19.4179C0.0572519 19.2462 0 19.0363 0 18.8168L0 14.1126ZM17.6431 4.01718L14.2462 7.41412C13.7882 7.87214 13.0439 7.87214 12.5859 7.41412C12.1183 6.95611 12.1183 6.21183 12.5859 5.75382L15.9828 2.35687H14.1126C13.4637 2.35687 12.9389 1.82252 12.9389 1.17366C12.9389 0.524809 13.4637 0 14.1126 0H18.8168C19.0363 0 19.2462 0.0572519 19.4179 0.162214C19.437 0.171756 19.4466 0.181298 19.4656 0.19084L19.4752 0.200382L19.4943 0.209924C19.5229 0.23855 19.5515 0.257634 19.5802 0.28626C19.6183 0.324427 19.6756 0.362595 19.7137 0.410305C19.7424 0.438931 19.7614 0.467557 19.7805 0.496183L19.7901 0.505725L19.7996 0.515267V0.534351C19.8092 0.543893 19.8282 0.562977 19.8378 0.582061C19.9332 0.753817 20 0.954198 20 1.17366V5.87786C20 6.52672 19.4656 7.06107 18.8168 7.06107C18.1679 7.06107 17.6431 6.52672 17.6431 5.87786V4.01718Z" fill="white" />
                                        </svg>
                                    </button>
                                    <button id="download" onClick={() => { !state.Imeshform ? makeBlur() : downloadImage() }}>
                                        <svg width="20" height="15" viewBox="0 0 38 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M35.6233 18.7505C34.5878 18.7505 33.7489 19.5895 33.7489 20.6249V26.2497H3.74879V20.6249C3.74879 19.5895 2.90985 18.7505 1.87439 18.7505C0.838937 18.7505 0 19.5895 0 20.6249V28.1241C0 29.1596 0.838937 30.0001 1.87439 30.0001H35.6233C36.1203 30.0001 36.5981 29.802 36.9496 29.4504C37.3012 29.0989 37.4977 28.6227 37.4977 28.1241V20.6249C37.4977 20.1279 37.3012 19.6501 36.9496 19.2986C36.5981 18.947 36.1203 18.7505 35.6233 18.7505Z" fill="#B8BBC0" /><path d="M16.2608 19.6868L18.2039 21.6299C18.3509 21.7993 18.5475 21.9175 18.7664 21.9719C18.963 21.8952 19.1435 21.7785 19.2938 21.6299L21.2369 19.6356L24.6453 16.2272H24.6469C25.0065 15.8852 25.211 15.4107 25.211 14.9153C25.211 14.4184 25.0064 13.9437 24.6469 13.6018C24.3049 13.2423 23.8303 13.0377 23.3334 13.0377C22.838 13.0377 22.3634 13.2423 22.0215 13.6018L20.6233 15V1.87599C20.6233 0.840532 19.7843 0 18.7489 0C17.7134 0 16.8745 0.840532 16.8745 1.87599V15L15.545 13.6705C14.8035 13.0377 13.7025 13.0777 13.009 13.7616C12.3171 14.4455 12.2628 15.5465 12.886 16.296L16.2608 19.6868Z" fill="#B8BBC0" />
                                        </svg>
                                    </button>
                                </div>

                                <img src={currentImage} id={`t${id}_zoomimage`} style={{ transform: transform, cursor: cursor, transition: 'transform 0.3s ease', maxWidth: `${imgHeight}px` }} />

                                <div id={`t${id}_blurimg`} className="blrImg beabult" data-role="" style={{ backgroundImage: `url('${currentImage}')` }}></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );


}
export default ImgLeft_sec;
