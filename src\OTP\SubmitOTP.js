import React , { useState, useEffect }from 'react';
import {useGlobalState } from '../context/store';
import OtpHandleUI from './OtpHandleUI';
import IntGenApi from "../main/IntGenApi";
// import callPostreq from '../callpostreq';
import "../Login/form.css";
import "./User_otp.css";
import { Eventtracking } from '../common/formCommfun';
const SubmitOTP = ({ form_param,errordisp, setErrordisp, otpEntered,setShouldFocus,otpreq,getOTPclicked }) => {
  const { state, dispatch } = useGlobalState();
  const [validated, setValidated] = useState(false);
  const handleSubmit = async (e) => {
    e.preventDefault();
    let otpval=otpEntered;
    if (otpval.trim() === "") {
      setErrordisp("empty");
      setShouldFocus(false);
      setTimeout(() => setShouldFocus(true), 0);
      return;
    } else if(otpval.trim().length != 4){
        setErrordisp("incorrect");
        setShouldFocus(false);
        setTimeout(() => setShouldFocus(true), 0);
        return;
    }
    else if(otpval.trim().length == 4){
        const ver = await OtpHandleUI('',2,otpval.trim(),setErrordisp,'',setShouldFocus,'',setValidated,form_param);
        if(ver == "V"){
          let temp = state.UserData;
          temp.uv = ver;
          dispatch({ type: 'UserData', payload: { UserData: temp } });
        }else{
          return;
        }
    }
    dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
    dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep+1 } });
      
  };
  const handleKeyPress = (event) => {
    if (event.keyCode === 13) {
      handleSubmit(event);
    }
  };
  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
        document.removeEventListener('keydown', handleKeyPress);
    };
  }, [otpEntered]);

  useEffect(() => {
    window.userdata = state.UserData;
}, [state.UserData]);
  useEffect(() => {
    if (validated) {
    //   if(state.postreq==0){
    //     let qid= callPostreq(form_param);   
    //     if(qid!=''){
    //      dispatch({ type: 'postreq', payload: { postreq: qid } }); 
    //     }  
    //  } 
     if(state.frscr==2 && !state.searchshown){
        dispatch({ type: "frscr", payload: { frscr: 1 } });
      }
      Eventtracking("SS" + window.screencount + "-FillOTP-Filled" , state.prevtrack , form_param , false);
      dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack+"-FillOTP" } });
      window.screencount++;
      dispatch({ type: 'OTPcon', payload: { OTPcon: false } });
      IntGenApi(form_param, "loginscreen")
      if(form_param.formType =="BL"){
        dispatch({ type: 'IsqformBL', payload: { IsqformBL: true } });
      }else{
        dispatch({ type: 'Isqform', payload: { Isqform: true } });
      }
      
    }
  }, [validated, dispatch]);
  let sbmmtcls = 'befstgo2 hovsub';
  
  var clsfrm = "";
  let mt='25px';
  let wd='200px';
  if(form_param.formType == 'BL'){
    sbmmtcls += ' blotpbtn';
    mt='15px';
    wd='140px';
  }
  if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
    clsfrm = "form-group-img";
    sbmmtcls = "submit-button-img otpsub";
}

  return (
    <div className="form-group">
      <div className={clsfrm} id="t0901_submitdiv">
        <input
          value={!otpreq ? "Send OTP" : "Submit"}
          className={sbmmtcls}
          id="t0901_submit"
          type="submit"
          style={{marginTop: mt ,width : wd}}
          onClick={!otpreq ? getOTPclicked : handleSubmit}
        />
      </div>
    </div>
  );
};

export default SubmitOTP;
