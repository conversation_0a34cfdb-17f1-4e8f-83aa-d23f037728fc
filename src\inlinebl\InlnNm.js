import React, { useEffect, useState } from "react";

function InlnNm({ country_iso, usrnm, setusrnm, err_msg, id, usrnmpr, handleoutsideclick,sticky }) {
    const [err, seterr] = useState("");

    useEffect(() => {
        if (err_msg == "Please enter you name") {
            seterr("ered");
        }else{
            seterr("");
        }
    }, [err_msg]);

    const handleInputChange = (event) => {
        const { value } = event.target;
        setusrnm(value);
        seterr("");
    };

    const handleKeyPress = (event) => {
        const charCode = event.which || event.keyCode;
        const charStr = String.fromCharCode(charCode);

        // Check if the character is an alphabet (both uppercase and lowercase)
        const alphabetRegex = /^[A-Za-z\s]*$/;
        if (!alphabetRegex.test(charStr)) {
            event.preventDefault(); // Prevent the character from being entered
            seterr("ered");
        }
    };

    const handlenameclick = () => {
        handleoutsideclick();
    };

    return (
        <>
            <div className="idsf pfstrt mb20 eqfcsed">
                {!sticky && <label className="fs15 cl11">Name
                    {country_iso !='IN' && <span className="redc">*</span>}
                </label>}
                <div className="pflx1 pr eqfcsed">
                    <input
                        type="text"
                        onChange={handleInputChange}
                        onKeyPress={handleKeyPress}
                        disabled={usrnmpr}
                        className={`name-input-inlinebl  ${err}`}
                        onClick={handlenameclick}
                        value={usrnm}
                        placeholder="Enter your Name"
                        maxLength={100}
                    />
                    {err && <div className="errmsg">Please enter a valid name</div>}
                </div>
            </div>
        </>
    );
}

export default InlnNm;
