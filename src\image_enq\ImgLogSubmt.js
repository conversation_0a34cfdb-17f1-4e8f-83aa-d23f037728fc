import React from 'react';
function ImgLogSubmt({id, handlelgnSub, country_iso,form_param}){
    let sub_cls = country_iso =="IN"?"IN":"txt-cnt";
    let sbmmtcls = 'befstgo2 hovsub befwt';
    var clsfrm = "";
    if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
        clsfrm = "form-group-img";
        sbmmtcls = country_iso =="IN" ? "submit-button-img subIn" : "submit-button-img";
    }
    return(
        <>
        <div id="t0901submit_wrapper" className="txt-cnt">
            <div id="t0901_fBtn" className="bepr eqClearfx" >
                {/* <div className="backdiv dispNone" id="t0901_backdiv">
                    <input value="" className="ber-backbtn" id="t0901_be-backbtn" type="submit" />
                    <div id="t0901_backarr"></div>
                </div> */}
                <div className={clsfrm} id="t0901_submitdiv">
                    <input value="Contact Supplier" className={sbmmtcls} id="t0901_submit" type="submit" onClick={handlelgnSub} />
                </div>
            </div>
        </div>
            
        </>

        
    )
}
export default ImgLogSubmt;