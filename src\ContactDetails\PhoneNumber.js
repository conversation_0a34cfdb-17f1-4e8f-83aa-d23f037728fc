import React, { useState, useEffect } from 'react';
import { useGlobalState } from '../context/store';
const PhoneNumber = ({ fn, em, mb, iso }) => {
    const [infoHtml, setInfoHtml] = useState('');
    const { state, dispatch } = useGlobalState();
    useEffect(() => {
        const generateInfoHtml = () => {
            let formattedMb = mb; // Use a local variable to avoid direct mutation

            if (iso === 'IN') {
                formattedMb = `+91-${mb}`;
            }

            let newInfoHtml = '<div>';
            newInfoHtml += '<b>Your Contact Information :</b><br>';
            if(fn!=''){
                newInfoHtml += `${fn}<br>`;
            }
            if(formattedMb!='')
            newInfoHtml += `${formattedMb}`;

            if (em !== '' && mb !== '') {
                newInfoHtml += '<span> | </span>';
            }

            newInfoHtml += em;
            newInfoHtml += '</div>';

            return newInfoHtml;
        };

        const updatedInfoHtml = generateInfoHtml();
        setInfoHtml(updatedInfoHtml);
    }, [fn, em, mb, iso]); // Run effect when any of these props change

    return (
        <>
        {
            !state.openImgPopup ? <div className='InfoHTML' dangerouslySetInnerHTML={{ __html: infoHtml }} /> : null
        }
        </>
        
    )
};

export default PhoneNumber;
