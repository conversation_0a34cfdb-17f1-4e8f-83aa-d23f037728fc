import React, { useEffect } from "react";

const SearchAdFor = ({ form_param }) => {
    useEffect(() => {
        const loadAdSenseScript = () => {
            // Check if ads.js is already loaded
            if (!document.querySelector("script[src='https://www.google.com/adsense/search/ads.js']")) {
                const adsScript = document.createElement("script");
                adsScript.src = "https://www.google.com/adsense/search/ads.js";
                adsScript.async = true;
                adsScript.onload = () => initializeAds(); // Initialize ads after script loads
                document.head.appendChild(adsScript);
            } else {
                // If script is already loaded, initialize ads immediately
                initializeAds();
            }
        };

        const initializeAds = () => {
            // Retry mechanism to check if _googCsa is available
            const checkAndLoadAds = setInterval(() => {
                if (window._googCsa) {
                    loadAds();
                    clearInterval(checkAndLoadAds); // Stop the interval after loading ads
                }
            }, 100); // Retry every 100ms
        };

        const loadAds = () => {
            const pageOptions = {
                pubId: "partner-pub-0673059417528889",
                query: form_param.prodName || form_param.mcatName,
                styleId: "4836254773",
                adsafe: "high",
                number: 1,
            };

            const adblock1 = {
                container: "low_sold_AD_aftergen",
            };

            window._googCsa("ads", pageOptions, adblock1);
        };

        loadAdSenseScript();
    }, [form_param]);

    return (
        <div id="low_sold_AD_aftergen" style={{ margin: "5px", padding: "10px 5px" , width : "350px" ,  maxHeight: "500px"}}>
            {/* Ad unit container */}
        </div>
    );
};

export default SearchAdFor;
