import { readCookieREC, getparamValREC } from './common/formCommfun';
export default async function callPincodeAPI (form_param,city){
    let webAddressLocation = location.hostname;
    let baseUrl = "https://apps.imimg.com/index.php";
    if(webAddressLocation.match(/^dev/)){
        baseUrl = "https://dev-apps.imimg.com/index.php";
    }
    const queryParams = new URLSearchParams({
        r:'Newreqform/UsrPincode',
        modid: form_param.modId,
        pincode: city ? city : '',
        _: new Date().getTime()
    });
    const url = `${baseUrl}?${queryParams.toString()}`;
    try {
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        let res = await response.json();
        // console.log(res);
        if(typeof(res)!="undefined" && res && res.CODE == 200 && res.DATA){
            if(res.DATA.city && res.DATA.city.city_id && res.DATA.city.city_name){
                return { 
                    city_name:res.DATA.city.city_name, 
                    city_id:res.DATA.city.city_id
                }
            }
        }
        return 'nocity';
    } catch (error) {
        console.error('Failed to call API:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'miniDtl','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
         return 'nocity';
    }
};