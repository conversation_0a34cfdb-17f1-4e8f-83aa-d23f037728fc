import { cslModidR, getActivityTimeREC, getparamValREC, isset, readCookieREC} from '../common/formCommfun';

export default async function CSLApi(form_param, request_url, activity_id) {
    let url = document.location.href;
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const glid = getparamValREC(imesh, 'glid');
    const display_title = activity_id =='4271'? 'Click on Image (Recommendation Widget)' : activity_id =='4269'? 'Get Best Price (Recommendation Widget)' : form_param.ctaName;
    let data = {
      glusr_id: glid,
      fk_display_title: display_title,
      domain_name: location.hostname,
      modid: cslModidR(form_param.modId),
      referer: location.hostname,
      catalog_owner_glusr_id: form_param.rcvGlid,
      product_disp_id: form_param.pDispId,
      mcat_ids: form_param.mcatId,
      group_id: '',
      subcat_id: form_param.catId,
      seller_city_id: '',
      activity_id: activity_id,
      request_url: request_url,
      user_agent: navigator.userAgent,
      url_weight: '1',
      url: url,
      http_status: '200',
      activity_time: getActivityTimeREC(),
  };

  Object.keys(data).forEach(function (key) {
    if (data[key] && typeof data[key] === 'number') {
      data[key] = String(data[key]);  // Convert integer to string
    }
  });
  const mode = url.match(/^(https|http):\/\/dev/);
  const call_Url = mode == null ? 'https://track.indiamart.com/CSL' : 'https://dev-track.indiamart.com/CSL';
   
      try {
          
          const response = await fetch(call_Url, {
            method: 'POST',
            mode: 'cors',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: JSON.stringify(data),
          });
    
        if (!response.ok) {
          throw new Error('Failed to call API');
        }
    
        const responseData = await response.json();
        return responseData;
      } catch (error) {
        console.error('Failed to call API:', error);
      }

  
  
}
