import React from 'react';
function VerBadge({type}){
    var verified = {
        1: { name: "TrustSEAL Verified", class: "equTs" },
        2: { name: "Verified Supplier", class: "equVs" },
        3: { name: "Verified Plus Supplier", class: "equVPs" }
      };
      return (
        type && type !== '0' ? (
            <div className="idsf id_aic emr10">
                <i className={`imFsp oef0 ${verified[type].class}`}></i>{verified[type].name}
            </div>
        ) : null
    )
}
export default VerBadge;