import { readCookieREC, getparamValREC } from './common/formCommfun';
export default async function callMiniDetAPI (form_param){
    let webAddressLocation = location.hostname;
    let baseUrl = "https://apps.imimg.com/index.php";
    if(webAddressLocation.match(/^dev/)){
        baseUrl = "https://apps.imimg.com/index.php";
    }
    const queryParams = new URLSearchParams({
        r:'Newreqform/MiniDetails',
        s_glusrid: getparamValREC(readCookieREC('ImeshVisitor'), 'glid'),
        modid: form_param.modId,
        s_Ak: readCookieREC('im_iss') ? getparamValREC(readCookieREC('im_iss'), 't') : "",
        inputkeys:"IS_COMPANYNAME_AVAILABLE,IS_GST_AVAILABLE,IS_URL_AVAILABLE,CITY_ID",
        sessionKey:  getparamValREC(readCookieREC('ImeshVisitor'), 'sessionKey'),
        _: new Date().getTime()
    });
    const url = `${baseUrl}?${queryParams.toString()}`;
    try {
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        let res = await response.json();
        if(typeof(res)!="undefined" && typeof(res.Response)!="undefined" && typeof(res.Response.Data)!="undefined" && res.Response.Code == 200){
            var res2={Response : {Data : res.Response.Data }};            
            const glid = getparamValREC(readCookieREC('ImeshVisitor'), 'glid');
            res['md_resp'] = {[glid]:res2};
            sessionStorage.setItem("minidtlsres",JSON.stringify(res['md_resp']));
            
            // Save ECV data if it exists
            if (res.Response && res.Response["ECV"]) {
                sessionStorage.setItem(`ecv-${glid}`, JSON.stringify(res.Response["ECV"]));
            }
        }
        return res;
    } catch (error) {
        console.error('Failed to call API:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'miniDtl','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
};