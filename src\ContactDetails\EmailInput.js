import React, { useEffect, useRef } from 'react';

const EmailInput = ({form_param, value, onChange ,val, Blpopup, autoFocus}) => {
    const handleEmailChange = (e) => {
        // Extract the value from the event object and call the onChange handler
        onChange(e.target.value);
    };


    let emcls='';
    if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
        emcls = "lgwd";
    }
    const inputRef = useRef(null);
    useEffect(() => {
        // Focus the input if autoFocus is true
        if (autoFocus && inputRef.current) {
            inputRef.current.focus();
        }
    }, [autoFocus,form_param]); // Re-run effect if autoFocus changes

    return (
        <div className="form-group">
            <input
                ref={inputRef}
                type="email"
                id="email"
                className={emcls}
                placeholder="Email"
                value={value}
                onChange={handleEmailChange} // Use the custom handler
                required
            />
            {form_param.formType !='BL' && <small className='Supptxt'>Supplier will contact you on this email</small>}
            {!val.emailcheck && <div style={{ color: 'red' }}>Please enter a valid email address</div>}
        </div>
    );
};

export default EmailInput;
