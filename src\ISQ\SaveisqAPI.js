import { readCookieREC, isset, getparamValREC ,reqFormGATrackREC} from '../common/formCommfun';

export default async function SaveisqAPI(form_param, qid,b_res,q_desc,q_id,b_id ) {
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const glid = getparamValREC(imesh, 'glid');
    const ofr_id = qid || 0;
    const upd=form_param.formType.toLowerCase() === "enq" ? "DESKTOP ENQUIRY FORM" : "DESKTOP BL FORM";
    // console.log(qid);
    let iploccookie = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    try {
        const params = {
            "modid": form_param && form_param.modId ? form_param.modId : 'DIR',
            "ofr_id": qid,
            "b_response[]": b_res,
            "q_desc[]": q_desc,
            "UPDIP": getparamValREC(iploccookie, 'gip'),
            "UPDURL": window.document.URL.toString().substring(0, 450),
            "UPDIP_COUNTRY": getparamValREC(iploccookie, 'gcnnm'),
            "UPDATESCREEN": upd,
            "glusr_id": glid,
            "q_id[]": q_id,
            "b_id[]": b_id,
            "mcat_id": form_param.mcatId,
            "glusr_email": "",
            "_": new Date().getTime()
        };

        
        if(form_param.formType.toLowerCase() === "enq"){
            params["enq"]= 1;
            params["funcToCall"]= "Enq";
        }
    
        const queryParams = new URLSearchParams();
        for (const key in params) {      
            if (Array.isArray(params[key])) {
                params[key].forEach(val => queryParams.append(key, val));
            } else {
                queryParams.append(key, params[key]);
            }
        }

        const webAddressLocation = location.host
        let ServerName = webAddressLocation.match(/^(dev)/)? "dev-":(webAddressLocation.match(/^stg/)?"stg-":"");
        ServerName='';
        const response = await fetch(`https://${ServerName}apps.imimg.com/index.php?r=postblenq/saveIsqBlEnq&${queryParams}`);
        // const response = await fetch(`https://dev-apps.imimg.com/index.php?r=postblenq/saveIsqBlEnq&${queryParams}`);
        
        if (!response.ok) {
            reqFormGATrackREC("service:saveIsqBlEnq:failure",form_param);
            throw new Error('Network response was not ok');
        }
        
        // const data = await response.json();
        reqFormGATrackREC("service:saveIsqBlEnq:success",form_param);
        // return data;
    
        
        
    } catch (error) {
        // Handle errors
        reqFormGATrackREC("service:saveIsqBlEnq:failure",form_param);
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'SaveIsq_Failure','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}
