import React from "react";
import Login_Heading from "./Login_Heading";
import { useGlobalState } from '../context/store';
import { isBlRevampd } from "./formCommfun";

function Head_scr({ scr, hash, id, scount, isqindex,otpreq }) {
  const { state, dispatch } = useGlobalState();
  let heading = "", bldcls = "befwt";

  if (scr === "otp") {
    if (hash.formType.toString().toLowerCase() === 'enq') {
      heading = "Verify your Mobile Number";
    } else {
      heading = "Verify your Mobile Number";
    }
  } else if (scr === "RD") {
    heading = "Almost Done!";
    bldcls = "";
    if(state.newEnq){
      heading = "Provide Additional Requirements (if any)";
    }

  } else if (scr === "isq") {
    if (scount === 1) {
      const boldHead = getisqHead(hash.ctaName);
      if (boldHead !== '') {
        if (boldHead === 'get more photos') {
          heading = "Add a few details of your requirement to <strong>get more photos</strong> and details from the seller quickly";
          bldcls = "";
        } else {
          heading = `<span className='befwt'>${boldHead}</span> by adding a few details of your requirement`;
          bldcls = "";
        }
      } else {
        heading = "Adding a few details of your requirement can get you quick response from the supplier";
        bldcls = "";
        if(state.newEnq){
          heading = "Add few details to get  quick response from the supplier";
        }
      }
    } else {
      heading = "Adding a few details of your requirement can get you quick response from the supplier";
      bldcls = "";
      if(state.newEnq){
        if(isqindex == 0)
          heading = "Add few details to get  quick response from the supplier";
        if(isqindex == 1)
          heading = "Let us know these specifics to refine your requirement";
        if(isqindex == 2)
          heading = "Share a Few More Details to Assist the Supplier";
        if(isqindex == 3)
          heading = "Add more specifications";
        if(isqindex >= 4)
          heading = "Almost Done !! ";
      }
    }
  } else if (scr === "nec") {
    heading = "Keep Your Information Up-to-Date !";
    if (hash.formType === "BL") {
      if(isBlRevampd(hash)){
        heading=''
      }
      else if (state.frscr === 2 || !state.inlineShownfields) {
          heading = "We want to know more about you!";
      } else if (state.frscr === 1 && !state.inlineShownfields) {
          heading = "We want to know more about you!";
      } else {
          heading = "";
      }
    }
    else if(state.RDNECcon ){
      heading = "Add your details to get response from the Supplier";
    }
  }else if (scr === "updtnec") {
    heading = "Please Verify Your City !";
  } 
  else if (scr === "morereq") {
    heading = "Please provide a few details to get quick response from the supplier";
    bldcls = "";
    if(hash.formType == "BL" && state.frscr==2){
      heading = "Just one step away to connect with verified sellers";
      bldcls = "befwt";
    }
    if(state.newEnq){
      heading = "Tell Us About Your Business";
    }
  }else if(scr === "isqBL"){
    heading = "Share more details about your requirement";
    // bldcls = "befwt"
  }
  else if(scr === "BlLogin" && !isBlRevampd(hash)){
    heading = "Looking to buy something?";
    bldcls = "befwt marBTM";
  }

  return (
    <>
      {
        hash.ctaType !== "Image" && hash.ctaType !== "Video" ? 
          (scr === "login" ? 
            <Login_Heading hash={hash} id={id} /> : 
           <div id="t0901_hdg" className="ber-hdg-r" data-role="" style={{}}>
  {isBlRevampd(hash) && (scr === "otp") ? (
    <>
      <svg
        width="64"
        height="64"
        viewBox="0 0 64 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M32 0C49.6731 0 64 14.3269 64 32C64 49.6731 49.6731 64 32 64C14.3269 64 0 49.6731 0 32C0 14.3269 14.3269 0 32 0Z"
          fill="#E0F2F1"
        />
        <path
          d="M32 0C49.6731 0 64 14.3269 64 32C64 49.6731 49.6731 64 32 64C14.3269 64 0 49.6731 0 32C0 14.3269 14.3269 0 32 0Z"
          stroke="#E5E7EB"
        />
        <path d="M45.5 50H18.5V14H45.5V50Z" stroke="#E5E7EB" />
        <path d="M45.5 50H18.5V14H45.5V50Z" stroke="#E5E7EB" />
        <path
          d="M19.625 18.5C19.625 16.018 21.643 14 24.125 14H39.875C42.357 14 44.375 16.018 44.375 18.5V45.5C44.375 47.982 42.357 50 39.875 50H24.125C21.643 50 19.625 47.982 19.625 45.5V18.5ZM34.25 45.5C34.25 44.9033 34.0129 44.331 33.591 43.909C33.169 43.4871 32.5967 43.25 32 43.25C31.4033 43.25 30.831 43.4871 30.409 43.909C29.9871 44.331 29.75 44.9033 29.75 45.5C29.75 46.0967 29.9871 46.669 30.409 47.091C30.831 47.5129 31.4033 47.75 32 47.75C32.5967 47.75 33.169 47.5129 33.591 47.091C34.0129 46.669 34.25 46.0967 34.25 45.5ZM39.875 18.5H24.125V41H39.875V18.5Z"
          fill="#00796B"
        />
      </svg>
      <div
        className={bldcls}
        dangerouslySetInnerHTML={{ __html: heading }}
      ></div>
    </>
  ) : (
    <span
      className={bldcls}
      dangerouslySetInnerHTML={{ __html: heading }}
    ></span>
  )}
</div>

          ) 
          : null
      }
    </>
  );
}


export default Head_scr

function getisqHead(ctaN){
    switch (ctaN.toLowerCase()) {
        case "get best quote":
          return "Get Best Quote";
        case "get quote":
          return "Get Quote";
        case "get latest price":
          return "Get Latest Price"
        case "contact seller":
          return "Contact Seller"
        case "contact seller_next":
          return "Contact Seller"
        case "contact seller_pre":
          return "Contact Seller"
        case "comphoto":
          return "Contact Seller"
        case "comvideo":
          return "Contact Seller"  
        case "contact supplier":
          return "Contact Supplier"
        case "ask price":
          return "Ask Price"
        case "ask for price":
          return "Ask for price"
        case  "get more photos":
        case  "get more photos_next":
        case  "get more photos_pre":    
            return "get more photos"
        case  "get free download":    
            return "Get Free Download"
        default:
            return '';    


}
}