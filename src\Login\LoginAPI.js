import { set<PERSON><PERSON>ieRE<PERSON>, updateimesh<PERSON>ombined} from '../common/formCommfun';
import SessionDetail<PERSON><PERSON> from './SessionDeatilApi';
// import GlusrUpdate from './GlusrUpdate';
export default async function getLoginAPI(login_data, login_flag) {
  const webAddressLocation = location.hostname;
  const ServerName = webAddressLocation.match(/^(dev)/) ? "":(webAddressLocation.match(/^stg/)?"stg-":"");
  const url =  "https://"+ServerName+"apps.imimg.com/index.php?r=Newreqform/LoginVerification";
    
  const formData = new URLSearchParams();
  const data = {username:login_data.username,
    iso:login_data.iso,
    identified:login_data.identified,
    modid:login_data.modid,
    token:login_data.token,
    format:login_data.format,
    screen_name:login_data.screen_name,
    IP:login_data.IP,
    USER_IP:login_data.USER_IP,
    ip:login_data.ip,
    USER_IP_COUNTRY:login_data.USER_IP_COUNTRY,
    GEOIP_COUNTRY_ISO:login_data.GEOIP_COUNTRY_ISO,
    IPADDRESS:login_data.IPADDRESS
  }
  if(login_flag != 2){
    data.originalreferer = login_data.originalreferer;
    data.create_user= login_data.create_user;
  }
  formData.append('data', JSON.stringify(data));
  formData.append('flag', 'login');
  
  try {
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        body: formData
      });

    if (!response.ok) {
      throw new Error('Failed to call API');
    }

    let responseData = await response.json();
    
    if(responseData.code == 200){
        // if(login_flag != 2){
          if(responseData.msg){window.LoginmodeReact = responseData.msg.trim() == "Mobile Number found in Primary Mobile".trim() || responseData.msg.trim() == "Email found in Primary Email".trim() ? 4 : responseData.msg.trim() == "New User created via User creation service".trim() ? 3 : 0;}
          if(login_flag != 2)setCookieREC('ImeshVisitor', responseData.DataCookie, 365);
          if(responseData.DataCookie && responseData.DataCookie.sessionKey){
            responseData = await SessionDetailApi(responseData.DataCookie.sessionKey,login_data.modid);
            if (typeof getLoginStringv1 === "function" && login_flag != 2) getLoginStringv1();
            return responseData;
          }
          else{
            responseData.DataCookie = updateimeshCombined(responseData.DataCookie,false);
          }
          if (typeof getLoginStringv1 === "function" && login_flag != 2) getLoginStringv1();
        // }
        return responseData;
    }else {
      return responseData;
    }
  } catch (error) {
    console.error('Failed to call API:', error);
    imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'LoginAPi','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
  }
}