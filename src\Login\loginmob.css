.input-containermob {
    display: flex;
    align-items: center;
    border: 1px solid #ccc;
    border-radius: 4px;
    height: 43px;
    transition: border-color 0.3s ease;
}
.mxwidth{
    max-width: 250px;
}

.country-code {
    color: #666;
    padding: 0 8px;
    border-right: 1px solid #ccc;
    height: 43px;
    align-content: center;
    transition: border-color 0.3s ease;
    width: 45px;
}

.mobile-input {
    border: none !important;
    outline: none !important;
    flex-grow: 1 !important;
    box-shadow: none !important;
    height: 30px !important;
    padding: 8px;
}

.mobile-input::placeholder {
    color: #ccc;
}
.input-containermob:focus-within {
    border-color: #2e3192;
}

.input-containermob:focus-within .country-code{
    border-color: #2e3192;
}
.ered{
    border-color: #f40c10 !important;
}
.errmsg{
    color: #f40c10;
    font-size: 12px;
    margin-top: 2px;
    font-weight: 400;
    margin-bottom: 0;
}