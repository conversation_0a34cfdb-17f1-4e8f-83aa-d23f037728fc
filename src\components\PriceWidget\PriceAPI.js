import { readCookieREC } from '../../common/formCommfun';

// API service for fetching price data from reseller API
export default async function fetchPriceData(form_param) {
  // Check authentication before proceeding
  const authToken = readCookieREC("ImeshVisitor");

  if (!authToken) {
    throw new Error('Authentication required to access price data');
  }

  // Extract required parameters from form_param
  const url_orig = form_param.displayImage || '';
  const subcat_id = form_param.catId || '';
  const rawPrice = form_param.price || '';

  // Extract numeric value from price string (e.g., "₹ 90/Piece" -> "90")
  const price = rawPrice ? rawPrice.toString().replace(/[^\d.]/g, '') : '';

  // Create cache key based on API parameters
  const cacheKey = `priceWidget_${subcat_id}_${price}_${url_orig}`;

  // Check if data exists in session storage
  try {
    const cachedData = sessionStorage.getItem(cacheKey);
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      return parsedData;
    }
  } catch (error) {
    console.warn('Error reading from session storage:', error);
    // Continue with API call if cache read fails
  }

  // Extract unit from input price for comparison (e.g., "₹ 90/Piece" -> "piece")
  const extractUnit = (priceString) => {
    if (!priceString) return '';
    const parts = priceString.toString().split('/');
    // Only return unit if there's actually a "/" in the price
    return parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';
  };

  // Convert lakh prices to numeric values (e.g., "1.02 lakh" -> 102000)
  const convertLakhToNumeric = (priceStr) => {
    if (!priceStr) return priceStr;

    const str = priceStr.toString().toLowerCase();

    // Check if price contains "lakh"
    if (str.includes('lakh')) {
      // Extract the numeric part before "lakh"
      const match = str.match(/(\d+\.?\d*)\s*lakh/);
      if (match) {
        const lakhValue = parseFloat(match[1]);
        const numericValue = lakhValue * 100000; // 1 lakh = 100,000

        // Replace the lakh part with numeric value in the original string
        // Keep the currency symbol and unit if present
        const beforeLakh = priceStr.substring(0, priceStr.toLowerCase().indexOf(match[0]));
        const afterLakh = priceStr.substring(priceStr.toLowerCase().indexOf(match[0]) + match[0].length);

        return beforeLakh + numericValue.toLocaleString('en-IN') + afterLakh;
      }
    }

    return priceStr;
  };

  // Extract numeric values from price strings for discount calculation
  const extractPrice = (priceStr) => {
    if (!priceStr) return 0;

    const str = priceStr.toString().toLowerCase();

    // Handle lakh values first
    if (str.includes('lakh')) {
      const match = str.match(/(\d+\.?\d*)\s*lakh/);
      if (match) {
        const lakhValue = parseFloat(match[1]);
        return lakhValue * 100000; // Convert to numeric
      }
    }

    // Handle regular numeric values
    const numericValue = priceStr.toString().replace(/[^\d.]/g, '');
    return parseFloat(numericValue);
  };

  const inputUnit = extractUnit(rawPrice);

  // Use "piece" as default unit if no unit is present
  const finalInputUnit = inputUnit || 'piece';

  // Validate required parameters
  if (!url_orig || !subcat_id || !price) {
    throw new Error('Missing required parameters: product image, subcategory ID, or price');
  }



  // Build server URL like search API
  const webAddressLocation = location.hostname;
  var serverName = webAddressLocation.match(/^dev/)
    ? "//dev-apps.imimg.com/"
    : webAddressLocation.match(/^stg/)
      ? "//stg-apps.imimg.com/"
      : "//apps.imimg.com/";

  var url = serverName + `index.php?r=Newreqform/ResellerData&url_orig=${encodeURIComponent(url_orig)}&subcat_id=${subcat_id}&price=${price}&modid=FORMS&source_page=PDP&price_filter=all`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors'
    });

    if (!response.ok) {
      // Track API failure
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': `API-Response-Failed-Subcat-${subcat_id}`,
          'eventLabel': `HTTP-${response.status}`,
          'eventValue': 0,
          'non_interaction': 0
        });
      }
      throw new Error('Failed to call API');
    }

    const data = await response.json();

    // Track successful API response
    if (window.imgtm) {
      imgtm.push({
        'event': 'IMEvent-NI',
        'eventCategory': 'Price-Widget',
        'eventAction': `API-Response-Success-Subcat-${subcat_id}`,
        'eventLabel': 'Data-Received',
        'eventValue': 1,
        'non_interaction': 0
      });
    }

    // Transform API response to match widget format
    if (data.status === "200" && data.results && data.results.length > 0) {

      let products = data.results.map(item => ({
        id: item.DISPLAY_ID,
        name: item.ITEM_NAME,
        price: extractPrice(item.PRICE_F) || parseFloat(item.PRICE_SEO) || 0, // Use extractPrice to handle lakh values
        priceDisplay: convertLakhToNumeric(item.PRICE_F), // Convert lakh to numeric in display
        image: item.IMAGE_500X500 || item.IMAGE_250X250 || item.IMAGE_125X125,
        supplier: item.COMPANYNAME,
        location: `${item.CITY_NAME}, ${item.STATE_NAME}`
      }));

      // Count products excluding current product (based on pDispId)
      const currentProductId = form_param.pDispId;

      // Filter products to only include those with the same unit as input price
      // Only apply unit filtering if the input price has a unit
      if (finalInputUnit) {
        products = products.filter(product => {
          if (!product.priceDisplay) return false;

          // Extract unit from PRICE_F (e.g., "₹ 400 / Kg" -> "kg")
          const parts = product.priceDisplay.split('/');
          const productUnit = parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';

          // Compare units (case-insensitive)
          return productUnit === finalInputUnit;
        });
      }
      // If no input unit, show all products without unit filtering



      // Remove current product from comparison (exclude self)
      // if (currentProductId) {
      //   products = products.filter(product => product.id != currentProductId);
      // }



      // Only show widget if more than 1 product is present after filtering
      if (products.length <= 1) {
        
        // Track widget formation failure
        if (window.imgtm) {
          imgtm.push({
            'event': 'IMEvent-NI',
            'eventCategory': 'Price-Widget',
            'eventAction': `Widget-Formation-Failed-Subcat-${subcat_id}`,
            'eventLabel': `Insufficient-Products-${products.length}`,
            'eventValue': 0,
            'non_interaction': 0
          });
        }

        return {
          success: false,
          error: 'Insufficient products for price comparison',
          data: {
            products: []
          }
        };
      }

      const successResponse = {
        success: true,
        data: {
          products: products
        }
      };

      // Store successful response in session storage
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify(successResponse));
      } catch (error) {
        console.warn('Error storing to session storage:', error);
      }
      return successResponse;
    } else {
      // Track no data scenario
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': `API-No-Data-Subcat-${subcat_id}`,
          'eventLabel': `Status-${data.status || 'unknown'}`,
          'eventValue': 0,
          'non_interaction': 0
        });
      }

      return {
        success: false,
        error: 'No price data available',
        data: {
          products: []
        }
      };
    }

  } catch (error) {
    console.error('Price API Error:', error);

    // Track API error
    if (window.imgtm) {
      imgtm.push({
        'event': 'IMEvent-NI',
        'eventCategory': 'Price-Widget',
        'eventAction': 'API-Error',
        'eventLabel': error.message || 'Unknown-Error',
        'eventValue': 0,
        'non_interaction': 0
      });
    }

    return {
      success: false,
      error: error.message,
      data: {
        products: []
      }
    };
  }
}
