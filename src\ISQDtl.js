import React, { useState, useEffect } from 'react';
import ISQAPICall from './ISQ/ISQAPICall';
import SubmitButtonISQ from './ISQ/SubmitButtonISQ';
import Req_detail from './Req_detail';
import Head_scr from './common/heading';
import { useGlobalState } from './context/store';
import IsqQues from './ISQ/IsqQues';
import { isset, Eventtracking } from './common/formCommfun';

function ISQDtl({ form_param, selectedOptions, setSelectedOptions,selectedOptscr,setSelectedOptscr }) {
    const [responseData, setResponseData] = useState(null);
    const [isqQuesIndices, setIsqQuesIndices] = useState([0]);
    const [isqL, setisqL] = useState(1);
    const { state, dispatch } = useGlobalState();
    const [cansubmit, setCansubmit] = useState(1);
    const [firstscreen, setFirstscreen] = useState('');
    const [warning, setWarning] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            try {
                const data = await ISQAPICall(form_param);
                setResponseData(data);
            } catch (error) {
                console.error("Error fetching ISQ data:", error);
                imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'Error fetching ISQ data', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
                // Handle error
                dispatch({ type: 'Isqform', payload: { Isqform: false } });
                dispatch({ type: 'RDform', payload: { RDform: true } });
            }
        };

        fetchData();
        if (form_param.ctaName.includes('_Next') || form_param.ctaName.includes('_Pre')) {
            setisqL(1);
            setIsqQuesIndices([0]);
            window.isSC1 = "";
        }          
    }, [form_param]);


    useEffect(() => {
        // console.log(responseData);
        if (responseData && (responseData.CODE != 200 || !responseData.DATA || responseData.DATA.length === 0)) {
            dispatch({ type: 'Isqform', payload: { Isqform: false } });
            dispatch({ type: 'RDform', payload: { RDform: true } });
        }
    }, [responseData]);

    const savecheckbox = (optionval, q_desc, q_id, optionid) => {
        optionval = isset(() => optionval) ? optionval : '';
        q_desc = isset(() => q_desc) ? q_desc : '';
        q_id = isset(() => q_id) ? q_id : '';
        optionid = isset(() => optionid) ? optionid : '';

        if (optionval !== '') {
            setSelectedOptions(prevOptions => {
                const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id && opt.b_response === optionval);

                let updatedOptions = [...prevOptions];

                if (existingIndex !== -1) {
                    // Remove the existing option
                    updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
                } else {
                    // Add a new option
                    updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
                }

                // console.log(updatedOptions);
                return updatedOptions;
            });
            setSelectedOptscr(prevOptions => {
                const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id && opt.b_response === optionval);

                let updatedOptions = [...prevOptions];

                if (existingIndex !== -1) {
                    // Remove the existing option
                    updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
                } else {
                    // Add a new option
                    updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
                }

                // console.log(updatedOptions);
                return updatedOptions;
            });
        }
    };


    const saveIsqs = (optionval, q_desc, q_id, optionid) => {
        optionval = isset(() => optionval) ? optionval : '';
        q_desc = isset(() => q_desc) ? q_desc : '';
        q_id = isset(() => q_id) ? q_id : '';
        optionid = isset(() => optionid) ? optionid : '';
        setSelectedOptions(prevOptions => {
            const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id);

            let updatedOptions = [...prevOptions];

            if (existingIndex !== -1) {
                updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
            }
            if (optionval !== '') {
                updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
            }

            // console.log(updatedOptions);
            return updatedOptions;
        });
        setSelectedOptscr(prevOptions => {
            const existingIndex = prevOptions.findIndex(opt => opt.q_id === q_id);

            let updatedOptions = [...prevOptions];

            if (existingIndex !== -1) {
                updatedOptions = updatedOptions.filter((_, index) => index !== existingIndex);
            }
            if (optionval !== '') {
                updatedOptions.push({ b_response: optionval, q_desc: q_desc, q_id: q_id, b_id: optionid });
            }

            // console.log(updatedOptions);
            return updatedOptions;
        });
    };

    const saveQt = (quantity, quantityUnit, quantityMasterId, quantityOptionId, unitMasterId, unitOptionId) => {
        quantityMasterId = isset(() => quantityMasterId) ? quantityMasterId : '';
        quantityOptionId = isset(() => quantityOptionId) ? quantityOptionId : '';
        unitMasterId = isset(() => unitMasterId) ? unitMasterId : '';
        unitOptionId = isset(() => unitOptionId) ? unitOptionId : '';
        setSelectedOptions(prevOptions => {
            const updatedOptions = [...prevOptions];


            const existingQuantityIndex = updatedOptions.findIndex(opt => opt.q_id === quantityMasterId);
            if (existingQuantityIndex !== -1) {
                updatedOptions.splice(existingQuantityIndex, 1);
            }
            if (quantity !== '') {
                updatedOptions.push({ b_response: quantity, q_desc: 'Quantity', q_id: quantityMasterId, b_id: quantityOptionId });
            }


            const existingUnitIndex = updatedOptions.findIndex(opt => opt.q_id === unitMasterId);
            if (existingUnitIndex !== -1) {
                updatedOptions.splice(existingUnitIndex, 1);
            }
            if (quantity !== '' && quantityUnit !== '') {
                updatedOptions.push({ b_response: quantityUnit, q_desc: 'Quantity Unit', q_id: unitMasterId, b_id: unitOptionId });
            }

            // console.log(updatedOptions);
            return updatedOptions;
        });
        setSelectedOptscr(prevOptions => {
            const updatedOptions = [...prevOptions];


            const existingQuantityIndex = updatedOptions.findIndex(opt => opt.q_id === quantityMasterId);
            if (existingQuantityIndex !== -1) {
                updatedOptions.splice(existingQuantityIndex, 1);
            }
            if (quantity !== '') {
                updatedOptions.push({ b_response: quantity, q_desc: 'Quantity', q_id: quantityMasterId, b_id: quantityOptionId });
            }

            const existingUnitIndex = updatedOptions.findIndex(opt => opt.q_id === unitMasterId);
            if (existingUnitIndex !== -1) {
                updatedOptions.splice(existingUnitIndex, 1);
            }
            if (quantity !== '' && quantityUnit !== '') {
                updatedOptions.push({ b_response: quantityUnit, q_desc: 'Quantity Unit', q_id: unitMasterId, b_id: unitOptionId });
            }

            // console.log(updatedOptions);
            return updatedOptions;
        });
    };

    let res = responseData;

    //  if(responseData){
    //     res={
    //         "CODE": "200",
    //         "STATUS": "1",
    //         "DATA": [
    //             {
    //                 "IM_SPEC_MASTER_FULL_DESC": "",
    //                 "IM_SPEC_MASTER_ID": "53854",
    //                 "IM_CAT_SPEC_PRIORITY": "1",
    //                 "IM_SPEC_MASTER_DESC": "Food",
    //                 "IM_SPEC_MASTER_TYPE": "2",
    //                 "IM_SPEC_MASTER_STATUS": "1",
    //                 "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                 "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                 "CNT": "3",
    //                 "IM_CAT_SPEC_CATEGORY_ID": "144883",
    //                 "IM_SPEC_AFFIX_TYPE": "",
    //                 "IM_SPEC_DESC_WITH_AFFIX": "",
    //                 "IM_CAT_SPEC_SUP_PRIORITY": "1",
    //                 "IM_CAT_SPECIFICATION_SORTORDER": "1",
    //                 "IM_SPEC_OPTIONS_DESC": "Veg##Non Veg##Both Veg and Non-Veg##Other",
    //                 "IM_SPEC_OPTIONS_ID": "192345##192346##192347",
    //                 "IM_SPEC_OPT_BUYER_SELLER": "####",
    //                 "IM_SPEC_OPTIONS_STATUS": "1##1##1",
    //                 "IM_SPEC_OPT_PRIORITY": "1##2##3"
    //             },
    //             {
    //                 "IM_SPEC_MASTER_FULL_DESC": "",
    //                 "IM_SPEC_MASTER_ID": "53857",
    //                 "IM_CAT_SPEC_PRIORITY": "2",
    //                 "IM_SPEC_MASTER_DESC": "Number Of Persons",
    //                 "IM_SPEC_MASTER_TYPE": "4",
    //                 "IM_SPEC_MASTER_STATUS": "1",
    //                 "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                 "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                 "CNT": "3",
    //                 "IM_CAT_SPEC_CATEGORY_ID": "144883",
    //                 "IM_SPEC_AFFIX_TYPE": "",
    //                 "IM_SPEC_DESC_WITH_AFFIX": "",
    //                 "IM_CAT_SPEC_SUP_PRIORITY": "2",
    //                 "IM_CAT_SPECIFICATION_SORTORDER": "2",
    //                 "IM_SPEC_OPTIONS_DESC": "1##2##Other",
    //                 "IM_SPEC_OPTIONS_ID": "192361##192362##3149087",
    //                 "IM_SPEC_OPT_BUYER_SELLER": "####",
    //                 "IM_SPEC_OPTIONS_STATUS": "1##1##1",
    //                 "IM_SPEC_OPT_PRIORITY": "1##2##99"
    //             },
    //             {
    //                 "IM_SPEC_MASTER_FULL_DESC": "",
    //                 "IM_SPEC_MASTER_ID": "2108487",
    //                 "IM_CAT_SPEC_PRIORITY": "5",
    //                 "IM_SPEC_MASTER_DESC": "Location",
    //                 "IM_SPEC_MASTER_TYPE": "1",
    //                 "IM_SPEC_MASTER_STATUS": "1",
    //                 "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                 "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                 "CNT": "3",
    //                 "IM_CAT_SPEC_CATEGORY_ID": "144883",
    //                 "IM_SPEC_AFFIX_TYPE": "",
    //                 "IM_SPEC_DESC_WITH_AFFIX": "",
    //                 "IM_CAT_SPEC_SUP_PRIORITY": "5",
    //                 "IM_CAT_SPECIFICATION_SORTORDER": "3",
    //                 "IM_SPEC_OPTIONS_DESC": "Text",
    //                 "IM_SPEC_OPTIONS_ID": "11327723",
    //                 "IM_SPEC_OPT_BUYER_SELLER": "",
    //                 "IM_SPEC_OPTIONS_STATUS": "1",
    //                 "IM_SPEC_OPT_PRIORITY": "1"
    //             },
    //                 [
    //                     {
    //                         "IM_SPEC_MASTER_FULL_DESC": "",
    //                         "IM_SPEC_MASTER_ID": "2749567",
    //                         "IM_CAT_SPEC_PRIORITY": "1",
    //                         "IM_SPEC_MASTER_DESC": "Quantity",
    //                         "IM_SPEC_MASTER_TYPE": "1",
    //                         "IM_SPEC_MASTER_STATUS": "1",
    //                         "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                         "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                         "CNT": "4",
    //                         "IM_CAT_SPEC_CATEGORY_ID": "444608",
    //                         "IM_SPEC_AFFIX_TYPE": " ",
    //                         "IM_SPEC_DESC_WITH_AFFIX": "",
    //                         "IM_CAT_SPEC_SUP_PRIORITY": "",
    //                         "IM_CAT_SPECIFICATION_SORTORDER": "1",
    //                         "IM_SPEC_OPTIONS_DESC": "None",
    //                         "IM_SPEC_OPTIONS_ID": "12883141",
    //                         "IM_SPEC_OPT_BUYER_SELLER": "1",
    //                         "IM_SPEC_OPTIONS_STATUS": "1",
    //                         "IM_SPEC_OPT_PRIORITY": "1"
    //                     },
    //                     {
    //                         "IM_SPEC_MASTER_FULL_DESC": "",
    //                         "IM_SPEC_MASTER_ID": "2749568",
    //                         "IM_CAT_SPEC_PRIORITY": "2",
    //                         "IM_SPEC_MASTER_DESC": "Quantity Unit",
    //                         "IM_SPEC_MASTER_TYPE": "3",
    //                         "IM_SPEC_MASTER_STATUS": "1",
    //                         "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                         "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                         "CNT": "4",
    //                         "IM_CAT_SPEC_CATEGORY_ID": "444608",
    //                         "IM_SPEC_AFFIX_TYPE": " ",
    //                         "IM_SPEC_DESC_WITH_AFFIX": "",
    //                         "IM_CAT_SPEC_SUP_PRIORITY": "",
    //                         "IM_CAT_SPECIFICATION_SORTORDER": "2",
    //                         "IM_SPEC_OPTIONS_DESC": "Piece##Uit##Other",
    //                         "IM_SPEC_OPTIONS_ID": "12883142##12883143",
    //                         "IM_SPEC_OPT_BUYER_SELLER": "1##1",
    //                         "IM_SPEC_OPTIONS_STATUS": "1##1",
    //                         "IM_SPEC_OPT_PRIORITY": "1##2"
    //                     }
    //                 ],
    //                 {
    //                     "IM_SPEC_MASTER_FULL_DESC": "",
    //                     "IM_SPEC_MASTER_ID": "3605911",
    //                     "IM_CAT_SPEC_PRIORITY": "3",
    //                     "IM_SPEC_MASTER_DESC": "Number of Layers",
    //                     "IM_SPEC_MASTER_TYPE": "3",
    //                     "IM_SPEC_MASTER_STATUS": "1",
    //                     "IM_CAT_SPEC_CATEGORY_TYPE": "3",
    //                     "IM_SPEC_MASTER_BUYER_SELLER": "1",
    //                     "CNT": "4",
    //                     "IM_CAT_SPEC_CATEGORY_ID": "444608",
    //                     "IM_SPEC_AFFIX_TYPE": " ",
    //                     "IM_SPEC_DESC_WITH_AFFIX": "",
    //                     "IM_CAT_SPEC_SUP_PRIORITY": "",
    //                     "IM_CAT_SPECIFICATION_SORTORDER": "3",
    //                     "IM_SPEC_OPTIONS_DESC": "2 Layers##3 Layers##5 Layers##6 Layers##Other",
    //                     "IM_SPEC_OPTIONS_ID": "15707301##15707302##15707300##15707303##15707304",
    //                     "IM_SPEC_OPT_BUYER_SELLER": "1##1##1##1##1",
    //                     "IM_SPEC_OPTIONS_STATUS": "1##1##1##1##1",
    //                     "IM_SPEC_OPT_PRIORITY": "1##2##3##4##5"
    //                 }  
    //             ],
    //         "MESSAGE": "SUCCESS"
    //     };
    //  }


    // if (isset(() => res) && isset(() => res.DATA) && currentISO() != 'IN') {
    //     res.DATA = res.DATA.slice(0, 1);
    // }
    
    useEffect(() => {
        if (isset(() => responseData) && isset(() => responseData.DATA)) {
            if (state.newEnq) {
                const totalQuestions = res.DATA.length;
                const startIdx = isqQuesIndices[0];
                const endIdx = Math.min(startIdx + 1, totalQuestions);
                
                let trackingStr = `DS${window.screencount}-ISQ`;
                
                for (let i = startIdx; i < endIdx; i++) {
                    const isQuantity = res.DATA[i].length === 2 ? 'quantity' : '';
                    if(i==0 ){
                        isQuantity ? setFirstscreen('quantity') : setFirstscreen('other');
                    }                    
                    trackingStr += `-ISQ${i + 1}` + (isQuantity ? `-${isQuantity}` : '');
                }
                
                Eventtracking(trackingStr, state.prevtrack, form_param, false);
                dispatch({ type: 'currentscreen', payload: { currentscreen: trackingStr } });
            } else {
                const totalQuestions = res.DATA.length;
                const startIdx = isqQuesIndices[0];
                let trackingStr = `DS${window.screencount}-ISQ`;
                let hasRDBox = false;
        
                if (startIdx === 0) {
                    // First screen: either 1 ISQ or 1 ISQ + RD if it's the last screen
                    const isQuantity = res.DATA[0].length === 2 ? 'quantity' : 'other';
                    isQuantity ? setFirstscreen('quantity') : setFirstscreen('other');
                    trackingStr += `-ISQ1-${isQuantity}`;
                    if (totalQuestions === 1) {
                        hasRDBox = true;
                    }
                } else {
                    // Subsequent screens: 2 ISQs per screen, last screen includes RD
                    const endIdx = Math.min(startIdx + 2, totalQuestions);
                    for (let i = startIdx; i < endIdx; i++) {
                        const isQuantity = res.DATA[i].length === 2 ? 'quantity' : '';
                        trackingStr += `-ISQ${i + 1}` + (isQuantity ? `-${isQuantity}` : '');
                    }
                    if (endIdx === totalQuestions) {
                        hasRDBox = true;
                    }
                }
        
                // Append -ISQRD only if RDBox is present on this screen
                if (hasRDBox) {
                    trackingStr = trackingStr.replace(`DS${window.screencount}-ISQ`, `DS${window.screencount}-ISQRD`);
                    trackingStr += "-RDBox";
                }
        
                Eventtracking(trackingStr, state.prevtrack, form_param, false);
                dispatch({ type: 'currentscreen', payload: { currentscreen: trackingStr } });
            }
        }
    }, [responseData, isqQuesIndices, state.newEnq, isqL]);

    const handleIndexChange = (val) => {
        if ((isset(() => res) && isset(() => res.DATA) && res.DATA.length > 1)) {
            if (state.newEnq) {
                if (val) {
                    setIsqQuesIndices([val]);
                    window.isSC1 = 0;
                    setisqL(val + 1);
                }
            } else {
                if (val == 1) {
                    setIsqQuesIndices([1, 2]);
                    window.isSC1 = 0;
                    setisqL(2);
                }
                else {
                    let a=2*val
                    setIsqQuesIndices([a-1, a]);
                    window.isSC1 = 0;
                    setisqL(val+1);

                }
            }
        }
        else {
            dispatch({ type: 'Isqform', payload: { Isqform: false } });
            if (state.openPopup) { dispatch({ type: 'RDform', payload: { RDform: true } }); }
            else { dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: true } }); }
        }
    };

    const lastisqscr=(isql,res)=>{
        if(res.DATA.length <= ((2*isql)-1)){
            return true;
        }
        return false;
    }
    const moreisqscr=(isql,res)=>{
        if(isql==1){
            return true;
        }
        if(res.DATA.length > ((2*isql)-1)){
            return true;
        }
        return false;
    }

    return (
        <div className="form-container isqSpl">
            {isset(() => res) && isset(() => res.DATA) ? (
                <div className="isqDv">
                    {state.newEnq ? (
                        res.DATA.filter((_, index) => isqQuesIndices.includes(index)).length > 0 ? (
                            <>
                                {/* Get the first index from the filtered result and pass it to Head_scr */}
                                {res.DATA.map((item, index) =>
                                    isqQuesIndices.includes(index) && (
                                        <React.Fragment key={index}>
                                            {index < res.DATA.length ? (
                                                <>
                                                    {!state.priceWidgetApiResponse && <Head_scr scr={"isq"} hash={form_param} scount={window.isSC1} isqindex={index} />}
                                                    <IsqQues
                                                        data={res.DATA.filter((_, idx) => isqQuesIndices.includes(idx))}
                                                        savecheckbox={savecheckbox}
                                                        saveIsqs={saveIsqs}
                                                        saveQt={saveQt}
                                                        setCansubmit={setCansubmit}
                                                        responseData={responseData}
                                                        form_param={form_param}
                                                        warning={warning} 
                                                        setWarning={setWarning}
                                                    />
                                                    <SubmitButtonISQ
                                                        form_param={form_param}
                                                        isqsLeft={isqQuesIndices[0] + 1}
                                                        handleIndexChange={handleIndexChange}
                                                        selectedOptions={selectedOptions}
                                                        setSelectedOptions={setSelectedOptions}
                                                        selectedOptscr={selectedOptscr} 
                                                        setSelectedOptscr={setSelectedOptscr}
                                                        cansubmit={cansubmit}
                                                        setCansubmit={setCansubmit}
                                                        remainingisq={res.DATA.length - (index + 1)}
                                                        firstscreen={firstscreen}
                                                        resdata = {res.DATA.length}
                                                        setWarning={setWarning}
                                                    />
                                                </>
                                            ) : (
                                                <>
                                                    <Head_scr scr={"RD"} hash={form_param} />
                                                    <Req_detail
                                                        form_param={form_param}
                                                        onIsq={1}
                                                        selectedOptions={selectedOptions}
                                                        setSelectedOptions={setSelectedOptions}
                                                        selectedOptscr={selectedOptscr} 
                                                        setSelectedOptscr={setSelectedOptscr}
                                                        cansubmit={cansubmit}
                                                    /></>
                                            )}
                                        </React.Fragment>
                                    )
                                )}
                            </>
                        ) : (
                            <>
                                <Head_scr scr={"RD"} hash={form_param} />
                                <Req_detail
                                    form_param={form_param}
                                    onIsq={1}
                                    selectedOptions={selectedOptions}
                                    setSelectedOptions={setSelectedOptions}
                                    selectedOptscr={selectedOptscr} 
                                    setSelectedOptscr={setSelectedOptscr}
                                    cansubmit={cansubmit}
                                />
                            </>
                        )
                    ) : (
                        <>
                            {(lastisqscr(isqL,res)) ? (
                                <Head_scr scr={"RD"} hash={form_param} />
                            ) : (
                                <Head_scr scr={"isq"} hash={form_param} scount={window.isSC1} />
                            )}
                            <IsqQues
                                data={res.DATA.filter((_, index) => isqQuesIndices.includes(index))}
                                savecheckbox={savecheckbox}
                                saveIsqs={saveIsqs}
                                saveQt={saveQt}
                                setCansubmit={setCansubmit}
                                responseData={responseData}
                                form_param={form_param}
                                warning={warning} 
                                setWarning={setWarning}
                            />
                            {(lastisqscr(isqL,res)) ? (
                                <Req_detail
                                    form_param={form_param}
                                    onIsq={1}
                                    isqL={isqL}
                                    resdata={res.DATA.length}
                                    selectedOptions={selectedOptions}
                                    selectedOptscr={selectedOptscr} 
                                    setSelectedOptscr={setSelectedOptscr}
                                    cansubmit={cansubmit}
                                />
                            ) : (moreisqscr(isqL,res)) ? (
                                <SubmitButtonISQ
                                    form_param={form_param}
                                    isqsLeft={isqL}
                                    handleIndexChange={handleIndexChange}
                                    selectedOptions={selectedOptions}
                                    setSelectedOptions={setSelectedOptions}
                                    selectedOptscr={selectedOptscr} 
                                    setSelectedOptscr={setSelectedOptscr}
                                    cansubmit={cansubmit}
                                    setCansubmit={setCansubmit}
                                    firstscreen={firstscreen}
                                    resdata = {res.DATA.length}
                                    setWarning={setWarning}
                                />
                            ) : (
                                <Req_detail
                                    form_param={form_param}
                                    onIsq={1}
                                    selectedOptions={selectedOptions}
                                    setSelectedOptions={setSelectedOptions}
                                    selectedOptscr={selectedOptscr} 
                                    setSelectedOptscr={setSelectedOptscr}
                                    cansubmit={cansubmit}
                                />
                            )}
                        </>
                    )}
                </div>
            ) : (
                <div></div>
            )}
        </div>
    );
}

export default ISQDtl;
