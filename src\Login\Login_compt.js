import React, { useState } from "react";
import <PERSON>ginMainComp from './LoginMainComp';
import Head_scr from "../common/heading";
import { shouldRenderPriceWidget } from "../common/formCommfun";
function Login_compt({id, form_param, close_fun}){
    const [err_msg, seterr_msg] = useState("");
    return(
        <div id={`t0901screen1`} className="bedvh">
        {!shouldRenderPriceWidget(form_param) && <Head_scr scr={"login"} hash={form_param} id={id}/>}
            <div id={`t${id}clslog`} className="enqLogIn bemlsecR" >
                <LoginMainComp id={id} form_param={form_param} err_msg={err_msg} seterr_msg={seterr_msg} close_fun={close_fun}/>
            </div>
        </div>
    )
    
}
export default Login_compt;