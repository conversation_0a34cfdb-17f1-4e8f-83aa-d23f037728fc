
import React, { useRef, useEffect, useState } from "react";
import PhEmError from "./PhEmError";
import CountrySuggester from "./CountrtDrpdwn";
import { isInlineBl } from "../common/formCommfun";
function Email_fld({ id, seterr_msg, setname_err, err_msg, name_err, form_param, country_iso, setcountry_iso, setSelectedCountry, selectedCountry}) {
  const [email_value, setemail_value] = useState("");
  const [name_value, setname_value ] = useState("");
  const inputRef = useRef(null);
  // const wrapperRef = useRef(null);
  useEffect(() => {
    // Focus on the input field when the component renders
    if (id == "0901" && inputRef.current) {
      inputRef.current.focus();
    }
  }, [form_param]);
  const handleClick = () => {
    seterr_msg("");
    setname_err("");
  };
  const handleChange = (event) => {
    setemail_value(event.target.value);
  };
  const handlenameChange = (event) => {
    setname_value(event.target.value);
  };
  let lblcls = 'ber-lbl';
  if(form_param.formType == 'BL'){
    lblcls = 'prodsrchtitl';
  }

  return (
    <>
      <div id="Login_wrapper" className="wdth100">
        {(id == "0901" && (form_param.ctaName == "Get Latest Price" || form_param.ctaName == "Get Free Download" || form_param.formType == 'BL' )) ? <label id={`t0901_label-l`} className={`${lblcls} beml-50`}>Email ID<span className="redc">*</span></label> : null}
        <div className="pr">
          {err_msg !== "" && err_msg != "Please enter your requirement" && (id == "0401" || isInlineBl(form_param)) ? <PhEmError form_param={form_param} id={id} err_msg={err_msg} /> : ""}
          <div id="t0901_dliso" className="eqCntry emailbox"></div>
          <input id={`t${id}_login_field_email`} type="text" name="login_field" value={email_value} className={`ber-input benords beW3  inPlace pl65 grCBl`} placeholder="Enter your Email" request="0" onClick={handleClick} onChange={handleChange} ref={inputRef} />
        </div>
        {err_msg !== ""&& err_msg != "Please enter your requirement" && id == "0901" ? <PhEmError form_param={form_param} id={id} err_msg={err_msg} /> : ""}
        <CountrySuggester setcountry_iso={setcountry_iso} country_iso={country_iso} setSelectedCountry={setSelectedCountry} selectedCountry={selectedCountry} form_param={form_param}/>
        {!isInlineBl(form_param) && form_param.isEcom !== 1 && 
        <><label id={`t0901_label-l`} className={`${lblcls} beml-50`}>Name<span className="redc">*</span></label>
        <div className="pr">
          <input id={`t${id}_q_first_nm1`} type="text" name="q_first_nm" value={name_value} className="loginName inpt_errorbx inPlace " placeholder="Name" onClick={handleClick} onChange={handlenameChange} />
          {name_err !== "" && err_msg == "" ? <div id={`t${id}_error_first_name1`} className="ber-erbx beerrp" data-role="" ><div id={`t${id}_fname_errmsg1`} data-role="content" >Please Enter Your Name</div>{id == "0401" && <a className="ber-erarw" data-role="arrow"></a>}</div> : ""}
        </div></>}
      </div>


    </>

  )
}
export default Email_fld;