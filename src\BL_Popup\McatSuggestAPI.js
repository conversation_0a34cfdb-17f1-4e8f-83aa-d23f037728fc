const McatSuggestAPI = (inputValue) =>{

    var mcatMatch=inputValue;
    const fetchData = async () => {
        if( mcatMatch!=="" && mcatMatch!==null){
            var submit_url =
            "//apps.imimg.com/models/mcatid-suggestion.php?search_param=" +
            encodeURIComponent(mcatMatch);
            const response = await fetch( submit_url , {
               method: "GET",
               mode: 'cors',
               dataType: "json",
            });
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            const resdata = await response.json();
            if(resdata){
                // console.log(resdata);
            }
        }
    }
    const res = fetchData();
    return res;

}
export default McatSuggestAPI;