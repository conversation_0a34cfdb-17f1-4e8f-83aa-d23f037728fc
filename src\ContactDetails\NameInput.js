import React, { useEffect, useRef, useState } from 'react';

const NameInput = ({ form_param,value, onChange,val , autoFocus}) => {
    const [placeholdeer, setPlaceholdeer] = useState('City*');
    const [errcls, setErrcls] = useState('');
    const [isReadOnly, setIsReadOnly] = useState(false);
    // useEffect(() => {
    //     // Set the input field as read-only only if the initial value is not empty
    //     if (value !== "") {
    //         setIsReadOnly(true);
    //     }
    // }, []);

    const inputRef = useRef(null);
    useEffect(() => {
        // Focus the input if autoFocus is true
        if (autoFocus && inputRef.current) {
            inputRef.current.focus();
        }
    }, [autoFocus,form_param]); // Re-run effect if autoFocus changes
    const handleNameChange = (e) => {
        // Extract the value from the event object and call the onChange handler
        onChange(e.target.value);
    };
    
    let emcls='';
    if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
        emcls = "lgwd";
    }
    const handleKeyPress = (event) => {
        const charCode = event.which || event.keyCode;
        const charStr = String.fromCharCode(charCode);
        // Check if the character is an alphabet (both uppercase and lowercase)
        const alphabetRegex = /^[A-Za-z\s]*$/;
        if (!alphabetRegex.test(charStr)) {
            event.preventDefault(); // Prevent the character from being entered
        }
    };
    useEffect(() => {
        if(value && value.trim().length!=0){
            setPlaceholdeer('Name*');
            setErrcls('');
        }else{
            if(val.namecheck){
                setPlaceholdeer('Name*');
                setErrcls('');
            }else{
                setPlaceholdeer('Please Enter Your Name*');
                setErrcls('input-error');
                onChange("");
            }
        }
    }, [val.namecheck,value]);
    return (
        <div className="form-group">
            <input
                ref={inputRef}
                type="text"
                id="name"
                placeholder={placeholdeer}
                className={`${errcls} ${emcls}`}
                value={value}
                maxLength={100}
                onKeyPress={handleKeyPress}
                onChange={handleNameChange}
                required
                // readOnly={isReadOnly}
            />
        </div>
    );
};

export default NameInput;
