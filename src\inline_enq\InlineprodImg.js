import React from "react";
function InlineprodImg({ id, form_param }) {
  return (
    <div className="frmimg belft">
      <div id={`t${id}_pdpimg`}>
        <img
          id="obj_fit"
          src={form_param.displayImage}
          alt={form_param.prodName}
          height={60}
          width={60}
          loading="lazy"
        />
      </div>
      <div
        className="fs11 verT lh14 vhd ht5"
        title={form_param.prodName}
        id={`t${id}_pdpname`}
      >
        {" "}
        {form_param.prodName}{" "}
      </div>
    </div>
  );
}
export default InlineprodImg;
