import React, { useState, useEffect } from 'react';
import { readCookieREC ,isset, getparamValREC} from '../../common/formCommfun';
import './LocalSellerCard.css';
import { updateCityForms } from './updateCityForms';
import { useGlobalState } from '../../context/store';

const FormCityUpdate = ({ form_param}) => {
    const { state } = useGlobalState();
      const [isChecked, setIsChecked] = useState(true); 
    const imesh_cookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    let glusrCt = getparamValREC(imesh_cookie, 'ctid') ? getparamValREC(imesh_cookie, 'ctid') : '';
    let glid=getparamValREC(readCookieREC("ImeshVisitor"), "glid")


    useEffect(()=>{
        sessionStorage.setItem(`updatepopShown`,"yes");
    },[])
  

    const handleYesClick = async () => {
        // console.log('yesss');
        // updateCityForms(glbCt)
        sessionStorage.removeItem(`ecv-${glid}`)
        sessionStorage.removeItem(`minidtlsres`)
    };

    
    const handleCheckboxChange = (e) => {
        setIsChecked(e.target.checked);
    };

    return (
        <div className="local-seller-card">
            {/* <input
                type="checkbox"
                className="updtChk"
                id= "fromtochk"
                checked={isChecked}
                onChange={handleCheckboxChange}
            /> */}
            <div className="citytext" id="fromtochk">
                {/* Update my City from <span className="cityupdt">{state.cityUpdateInfo?.from_city}</span> to <span className="cityupdt">{state.cityUpdateInfo?.to_city}</span>      */}
                Your city information will be updated to the selected city.
            </div>
        </div>
    );
};

export default FormCityUpdate;
