// Iploc.js
import { getparamValREC, isset, readCookieREC, setCookieREC } from '../common/formCommfun';

const IpLoc = async (form_param) => {
  // Read the iploc cookie if it exists
  const tempIpLoc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
  const isGoogleBot = navigator.userAgent.match(/googlebot|mediapartners/);

  // If the cookie is not present or is invalid, and it's not a Google bot, fetch the IP
  if (
    (tempIpLoc === '' ||
      (tempIpLoc !== '' && getparamValREC(tempIpLoc, 'gip') === '')) &&
    !isGoogleBot
  ) {
    return await getIp(form_param);
  } else {
    return 'cookie already present';
  }
};

const getIp = async (form_param) => {
  const appSName = '//geoip.imimg.com/';

  if (form_param.modId !== undefined && form_param.modId !== null && form_param.modId !== '') {
    const data = new FormData();
    data.append('modid', form_param.modId);
    data.append('token', 'imobile@15061981');
    try {
      const response = await fetch(appSName + 'api/location', {
        method: 'POST',
        body: data,
      });

      const result = await response.json();
      if (result && result.Response) {
        const { Code, Status, Data } = result.Response;

        if (Code === 200 && Status === 'Success' && Data) {
          const resp = {
            geoip_cityname: Data.geoip_cityname || '',
            geoip_cityid: Data.geoip_cityid || '',
            geoip_countryiso: Data.geoip_countryiso || '',
            geoip_countryname: Data.geoip_countryname || '',
            geoip_accuracy: Data.geoip_accuracy || '',
            geoip_ipaddress: Data.geoip_ipaddress || '',
            geoip_statename: Data.geoip_statename || '',
          };
          setIpLoccookie(resp);
          return 'cookie is set';
        }
      }
      failSafe();
    } catch (error) {
      failSafe();
    }
  }
};

const setIpLoccookie = (resp) => {
  const tempIpLoc = `gcniso=${resp.geoip_countryiso}|gcnnm=${resp.geoip_countryname}|gctnm=${resp.geoip_cityname}|gctid=${resp.geoip_cityid}|gacrcy=${resp.geoip_accuracy}|gip=${resp.geoip_ipaddress}|gstnm=${resp.geoip_statename}`;

  setCookieREC('iploc', tempIpLoc, 3, true);
};

const failSafe = () => {
  const tempIpLoc =
    'gcniso=IN|gcnnm=India|gctnm=New Delhi|gctid=70469|gacrcy=20|gip=**************|gstnm=National Capital Territory of Delhi';
  setCookieREC('iploc', tempIpLoc, 0.125, true);
};

export default IpLoc;
