
import React from 'react';
import { LoginNewUiPDP, shouldRenderPriceWidget } from '../common/formCommfun';
function LogSubmt({form_param, id, handlelgnSub, country_iso , err_msg}){
    let sub_cls = country_iso =="IN"?"":"txt-cnt";
    let subincls = 'befstgo2 hovsub';
    let subval = "Submit";
    if(form_param.formType !="BL"){
        sub_cls = sub_cls+" mt10";
    }
    if(LoginNewUiPDP(form_param) && country_iso == "IN"){
        subincls += " enqpopgoin";
        subval = "Continue";
        sub_cls += " centred";
    }
    if(form_param.formType =="BL" && id =="0901"){
        sub_cls = sub_cls+" blpopgo";
        if(country_iso != "IN"){
            sub_cls = sub_cls+" blpopgofrn";
        }else{
            if(err_msg !="" && err_msg != "Please enter your requirement"){
                sub_cls = sub_cls+" blpopgoerr";
            }
        }
        subincls = 'blpopgoin hovsub'
        subval = "Go";
    }
    return(
        <div className={`${sub_cls}`}id={`t${id}_submitdiv`}>
            {shouldRenderPriceWidget(form_param)
                  ?<button value={subval} className={`pricewidgetloginscr ${subincls}`} id={`t${id}_submit`} type="submit" onClick={handlelgnSub}>{subval}
                <svg class="svg-inline--fa fa-arrow-right" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="arrow-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="#fff" d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path></svg>
            </button> :
            <input value={subval} className={subincls} id={`t${id}_submit`} type="submit" onClick={handlelgnSub} />}
            {LoginNewUiPDP(form_param) && country_iso === "IN" && (
              <div className="safetxt submtext">
                {shouldRenderPriceWidget(form_param)
                  ? "We don't call, only genuine sellers will contact"
                  : "Almost done! Just verify your mobile"}
              </div>
            )}
        </div>
    )
}
export default LogSubmt;

