import React, { useState, useEffect } from "react";
import './imgEnq.css';
import './slider.css';
import {isPDfForm, isset , reqFormGATrackREC } from '../common/formCommfun';
import { useGlobalState } from "../context/store";
import getstartindex from './getstartindex';

const SliderMulti = ({ form_param, onImageChange, blurImg, incrementProdCount, prodCountRef, setPiframe, setReset,reset,setLoader,changeProd_copy , setPdfselected,lowerresol}) => {

    const [activeIndex, setActiveIndex] = useState(0);
    const [startIndex, setStartIndex] = useState(0);
    const { state, dispatch } = useGlobalState();
    let visibleImages = null;
    let imagesToShow = null;
    let resol = window.innerWidth;
      let maximg = resol >= 1920 ? 6 : resol >= 1500 ? 5 : lowerresol ? 3 : 4;

    if (isset(() => form_param.multipleImageVideo) && form_param.multipleImageVideo.length > 0) {
        imagesToShow = form_param.multipleImageVideo;
    } else {
        if (isset(() => window.pdpMultires) && window.pdpMultires.length > 0) {
            imagesToShow = window.pdpMultires;
        }
    }

    useEffect(() => {
        if(isset(()=>imagesToShow) && imagesToShow.length>1 ){
            let defaultIndex = getstartindex(form_param,imagesToShow);
        setActiveIndex(defaultIndex);
        setStartIndex(Math.max(0, Math.min(imagesToShow.length - maximg, defaultIndex - 2)));
        if (isset(() => imagesToShow) && imagesToShow.length > 0) {
            onImageChange(imagesToShow[defaultIndex].displayImage);
        }
        }        
    }, [imagesToShow]);

    const sliderFn=(image)=>{
        if(image.type != 'Video' && image.type != 'pdf'){
            onImageChange(image.displayImage);
            setReset(true);
            setPdfselected && setPdfselected(false);
            incrementProdCount(1); 
            if( isset(()=>$("#videoProd")[0].src) &&
            $("#videoProd")[0].src !== ""){
                $("#videoProd")[0].src='';
            }
        }
        else if(image.type == 'Video'){
            setReset(false);
            setPdfselected && setPdfselected(false);
        }
        else if(image.type == 'pdf'){
            setPdfselected && setPdfselected(true);
            setReset(true);
        }
        if (prodCountRef.current > 2) {
            blurImg(true); 
            incrementProdCount(0);
        }
        
    }
    
    const handleImageClick = (index, image,type) => {
        var trackingText= type ? "slider_clicked_" + type : "slider_clicked";
        reqFormGATrackREC(trackingText,form_param);
        setActiveIndex(index);
        setStartIndex(Math.max(0, Math.min(imagesToShow.length - maximg, index - 2)));
        sliderFn(image);
    }    

    const showPreviousImages = (key) => {
        setActiveIndex((prevActiveIndex) => {
            if (prevActiveIndex > 0) {
                key ? reqFormGATrackREC("arrow_up_key",form_param) : reqFormGATrackREC("arrow_up",form_param);
                setLoader(false);
                const newActiveIndex = prevActiveIndex - 1;
                setStartIndex(Math.max(0, Math.min(imagesToShow.length - maximg, newActiveIndex - 2)));
                sliderFn(imagesToShow[newActiveIndex]);
                return newActiveIndex; // Return the updated index
            }else{
                if(isset(()=>changeProd_copy))
                changeProd_copy({ data: {todo: 'prev' } })
            }
            return prevActiveIndex; // Return the previous index if no change
        });
    };
    
    const showNextImages = (key) => {
        setActiveIndex((prevActiveIndex) => {
            if (prevActiveIndex < imagesToShow.length - 1) {
                key ? reqFormGATrackREC("arrow_down_key",form_param) : reqFormGATrackREC("arrow_down",form_param);
                setLoader(false);
                const newActiveIndex = prevActiveIndex + 1;
                setStartIndex(Math.max(0, Math.min(imagesToShow.length - maximg, newActiveIndex - 2)));
                sliderFn(imagesToShow[newActiveIndex]);
                return newActiveIndex; // Return the updated index
            }
            else{
                if(isset(()=>changeProd_copy))
                changeProd_copy({ data: { todo: 'next' } })
            }
            return prevActiveIndex; // Return the previous index if no change
        });
    };

    useEffect(() => {
        if(!isPDfForm(form_param)){
            const handleKeyPress = (event) => {
                if (event.key === 'ArrowLeft') {
                    showPreviousImages(1)
                  }
                  else if(event.key === 'ArrowRight' ){
                    showNextImages(1)
                  }
            }; 
            
            document.addEventListener('keydown', handleKeyPress);
            return () => {
                document.removeEventListener('keydown', handleKeyPress);
            };
        }       
    }, [form_param,imagesToShow]);

    
    if (isset(() => imagesToShow) && imagesToShow.length > 0) {
        visibleImages = imagesToShow.slice(startIndex, startIndex + maximg);
    }

    

    return (
        <>
            {isset(() => imagesToShow) && imagesToShow.length > 0 ? (
                <div id="t0901_slide" className="sLidIgsm" data-role="" >
                    {imagesToShow.length > 1 && (
                        <>
                            {activeIndex > 0 && (
                                <span id="t0901_beup" className="eqUp" onClick={()=>{showPreviousImages(0)}}></span>
                            )}
                            {activeIndex < imagesToShow.length - 1 && (
                                <span id="t0901_bedown" className="eqDwn" onClick={() => {showNextImages(0)}}></span>
                            )}
                        </>
                    )}
                    <div id="t0901_slideout" className="slideCss" data-role="">
                        <span id="t0901_sliderImg" className="sliderImg">
                            {visibleImages.map((image, index) => (
                                <span
                                    key={index + startIndex}
                                    id={`t0901_slider${index + startIndex}`}
                                    className={`eqitem ${index + startIndex === activeIndex ? 'active' : ''}`}
                                    onClick={() => handleImageClick(index + startIndex, image,image.type)}
                                >
                                    {image.type != 'pdf' && <>{image.type === 'Video' && (
                                        <span className="yTub"><i className="instfbIc"></i></span>
                                    )}
                                    <img id="t0901_dispimage" className="igTh" src={image.displayImage} alt={`Slide ${index + startIndex}`} /></>}
                                    {image.type === 'pdf' && <i className="Pdf_thum"> </i>}
                                </span>
                            ))}
                        </span>
                    </div>
                </div>
            ) :  <div id="t0901_slide" className="sLidIgsm" data-role="" style={{ width: '83px', flexShrink: 0 }} ></div> }
        </>
    );
}

export default SliderMulti;
