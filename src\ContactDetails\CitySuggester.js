import React from 'react';
import './NEC.css';
import { readCookieREC, getparamValREC, isset, updatePopupShow } from '../common/formCommfun';
import { useGlobalState } from '../context/store';

// React functional component for rendering city suggestions
export function CitySuggester({ onSelectCity ,form_param,isCityUpdate}) {
    const { state } = useGlobalState();
    const ipLoc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    let cityArr = {};
    cityArr[getparamValREC(ipLoc, 'lg_ct')] = getparamValREC(ipLoc, 'lg_ctid');
    cityArr[getparamValREC(ipLoc, 'gctnm')] = getparamValREC(ipLoc, 'gctid');

    if (isCityUpdate?.update && updatePopupShow(form_param)) {
        cityArr = {};
        cityArr[isCityUpdate?.to_city] = '';
        cityArr[isCityUpdate?.from_city] = '';
    }

    const handleCitySelect = (city,cityid) => {
        onSelectCity(city,cityid); // Call the onSelectCity function with the selected city
    };

    const renderCitySuggestions = () => {
        const suggestions = [];

        let isFirst = true; // Flag to track if it's the first suggestion

        for (const ct in cityArr) {
            if (
                ct &&
                ct.toLowerCase() != 'all india' &&
                ct.toLowerCase() != 'undefined' &&
                ct.toLowerCase() != '' &&
                ct.toLowerCase() != 'null'
            ) {
                if (!(isCityUpdate?.update && updatePopupShow(form_param)) && !isFirst) {
                    suggestions.push(<span key={`sep-${ct}`} className='suggSep'> | </span>);
                }


                {
                    (isCityUpdate?.update && updatePopupShow(form_param)) ?  suggestions.push(
                    <div className='sugstrip boxSug'
                        key={ct} 
                        onClick={() => handleCitySelect(ct,cityArr[ct])}
                    >
                        <div className='cityWrap'>{ct}</div>
                        {/* {(isCityUpdate?.update && updatePopupShow(form_param)) && <div className='cityTyp'>{cityArr[ct]}</div>} */}
                    </div>
                    
                )  : 

                suggestions.push(
                    <span className='sugstrip'
                        key={ct} // Use a unique key for each suggestion
                        onClick={() => handleCitySelect(ct,cityArr[ct])}
                    >
                        {ct}
                    </span>
                );


                }


               

                isFirst = false; // Update flag after processing the first suggestion
            }
        }

        // Render the suggestions container only if there are valid suggestions
        let imgsugg = "";
        if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
            imgsugg = " imgsugg";
        }
        if(isCityUpdate?.update && updatePopupShow(form_param)){
            imgsugg = " updtNw";
        }
        if (suggestions.length > 0) {
            return (
                <div className={'citySuggstrip' + imgsugg}>
                    <span className='sgstx'>Suggestions: </span>
                    {(isCityUpdate?.update && updatePopupShow(form_param)) ?  <div className='sugBlock'>
                        {suggestions}
                    </div> : suggestions }
                   
                    
                </div>
            );
        }

        return null; // Render nothing if there are no suggestions
    };

    return renderCitySuggestions();
}
