import React, {useEffect, useRef, useState} from 'react';
import { useGlobalState } from '../context/store';
import { Eventtracking, getparamValREC, isset, readCookieREC} from '../common/formCommfun';
import InEnqSubBtn from './InEnqSubBtn';
import InlineEnqLogin from './InlineENQlogin';
import InlnENQnm from './InlnENQnm';
import './inline.css';
import GDPRCountry, { isGDPRCountry } from '../Login/GDPRCountry';
// import { validateEmailId, validatePhoneNumber } from '../Login/VldNmEm';
// import LoginApiCall from '../Login/LoginApiCall';
function InlineEnq({id, form_param , close_Form}){
    const { state, dispatch } = useGlobalState();
    // const ismshexist = readCookieREC('ImeshVisitor');
    const [imeshExist, setImeshExist] = useState(readCookieREC('ImeshVisitor'));
    const [fn, setFn] = useState("");
    const [usrnm, setusrnm] = useState('');
    const [usrnmpr, setusrnmpr] = useState('');
    const [country_iso, setcountry_iso] = useState(window.countryiso);
    const [GDPRiso, setGDPRiso] = useState(isGDPRCountry(window.countryiso));
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [isGDPRcheck, setisGDPRcheck] = useState(false);
    const [GDPRerrmsg, setGDPRerrmsg] = useState("");
    const [mob_value, setmob_value] = useState("");
    const [email_val, setemail_val] = useState("");
    const [err_msg, seterr_msg] = useState("");
    const targetRef = useRef(null);
    const imeshExistRef = useRef(imeshExist);
    const observerRef = useRef(null);
    // const mobValueRef = useRef(mob_value);
    // const emailValueRef = useRef(email_val);
    // const countryIsoRef = useRef(country_iso);
    // const lastProcessedValuesRef = useRef({ mob_value: "", email_val: "", country_iso: "" });

    // Update the refs whenever the state changes
    // useEffect(() => {
    //     mobValueRef.current = mob_value;
    //     emailValueRef.current = email_val;
    //     countryIsoRef.current = country_iso;
    // }, [mob_value, email_val, country_iso]);
    
    // async function handleoutsideclick() {
    //     const ismshexist = readCookieREC("ImeshVisitor");
    //     if (!ismshexist) {
    //         const currentValues = {
    //             mob_value: mobValueRef.current,
    //             email_val: emailValueRef.current,
    //             country_iso: countryIsoRef.current,
    //         };
    //         const { mob_value, email_val, country_iso } = lastProcessedValuesRef.current;
    //         if ((countryIsoRef.current == "IN" && currentValues.mob_value !== mob_value) || (countryIsoRef.current != "IN" && currentValues.email_val !== email_val)) {
    //             let valid_check = "mob";
    //             let fld_val = mobValueRef.current;;
    //             if (countryIsoRef.current == "IN") {
    //                 valid_check = validatePhoneNumber(fld_val);
    //             } else {
    //                 valid_check = validateEmailId(emailValueRef.current);
    //                 fld_val = emailValueRef.current;
    //             }
    //             if (valid_check == "") {
    //                 const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    //                 const s_ip = getparamValREC(iploc, 'gip');
    //                 const loginres = await LoginApiCall(form_param, fld_val, 2 , s_ip, countryIsoRef.current);
    //                 if (isset(() => loginres) && loginres.DataCookie) {
    //                     setusrnm(loginres.DataCookie.fn);
    //                     if(loginres.DataCookie.fn){
    //                         setusrnmpr(true);
    //                         seterr_msg("");
    //                         return loginres.DataCookie.fn;
    //                     }else{
    //                         setusrnmpr(false);
    //                         return false;
    //                     }
    //                 }
    //                 return false;
    //             }
    //             lastProcessedValuesRef.current = currentValues;
    //             return false;
    //         }
    //         return false;
    //     }
    // }
    useEffect(() => {
      // Function to handle the observer callback
      const handleObserver = (entries) => {
        if (entries[0].isIntersecting) {
          const currentImeshVisitor = readCookieREC('ImeshVisitor');
          const loginval = currentImeshVisitor ? "-CTA" : "-UserLogin";
          Eventtracking("DS1" + loginval, state.prevtrack, form_param, true);
          if (currentImeshVisitor && !imeshExistRef.current) {
            setImeshExist(currentImeshVisitor);
          }
        }
      };
  
      // Initialize the observer
      observerRef.current = new IntersectionObserver(handleObserver, { threshold: 0.1 });
  
      if (targetRef.current) {
        observerRef.current.observe(targetRef.current);
      }
  
      // Clean up the observer when the component unmounts or when imeshExist is set
      return () => {
        if (observerRef.current && targetRef.current) {
          observerRef.current.disconnect();
        }
      };
    }, []);

    useEffect(() => {
      imeshExistRef.current = imeshExist;
      if (imeshExist && observerRef.current && targetRef.current) {
        observerRef.current.disconnect();
      }
      if (imeshExist == null) {
          dispatch({ type: "Imeshform", payload: { Imeshform: false } });
      } else {
          dispatch({ type: "Imeshform", payload: { Imeshform: true } });
      }
      const visitorFn = getparamValREC(imeshExist, "fn") || state.UserData && state.UserData.fn ? state.UserData.fn : "";
      setFn(visitorFn);
  }, [imeshExist]);
    return(
        <div className="contBLin" ref={targetRef}>
            <div className="ber-pdg">
              <img id="obj_fit" src={form_param.displayImage} alt={form_param.prodName} loading="lazy"/>
              <div className='ml12'>
                <span className='inlinemsghead'>Send enquiry for <span className='inlinemsgprodnm'>{form_param.prodName|| form_param.prodDispName || "This Product"}</span></span>

                {!imeshExist && <InlineEnqLogin id={id} form_param={form_param} err_msg={err_msg} seterr_msg={seterr_msg} country_iso={country_iso} setcountry_iso={setcountry_iso} selectedCountry={selectedCountry} setSelectedCountry={setSelectedCountry} mob_value={mob_value} setmob_value={setmob_value} email_val={email_val} setemail_val={setemail_val} setGDPRiso={setGDPRiso} setusrnmpr={setusrnmpr} setusrnm={setusrnm} usrnmpr={usrnmpr}/>}

                {fn == "" &&  country_iso !='IN' && <InlnENQnm country_iso={country_iso} usrnm={usrnm} setusrnm={setusrnm} err_msg={err_msg} id={id} usrnmpr={usrnmpr}/>}

                {!imeshExist && GDPRiso ? <GDPRCountry id={id} setisGDPRcheck={setisGDPRcheck} setGDPRerrmsg={setGDPRerrmsg} /> : ""}
                {!imeshExist && GDPRerrmsg != "" && <div className="GDPRerr">{GDPRerrmsg}</div>}
                <InEnqSubBtn form_param={form_param} seterr_msg={seterr_msg} usrnm={usrnm} isGDPRcheck={isGDPRcheck} country_iso={country_iso} GDPRiso={GDPRiso} setGDPRerrmsg={setGDPRerrmsg} mob_value={mob_value} email_val={email_val} setusrnm={setusrnm} id={id} setemail_val={setemail_val} close_Form={close_Form} setImeshExist={setImeshExist}/>
              </div>
            </div>
        </div>
    )
}

export default InlineEnq;
