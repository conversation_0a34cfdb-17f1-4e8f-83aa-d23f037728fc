import React ,{memo} from 'react';

function Form_hdrctblMemo({id, close_fun}) {
    return(
        <React.Fragment>
                <div id={`t${id}_chatBL`} className="cbl_wrp cbl_br8 cbl_df cbl_fdc ber-frwrap ber-frwrap " style={{zIndex:"999"}}>

                    <div id={`t${id}_blchatheader`} className="cbl_hed cbl_df cbl_p025 cbl_aic cbl_br8 cbl_bg1"><div id={`t${id}_prodimgR`} className="ber-prdimg cbl_img cbl_df cbl_aic cbl_jcc cbl_ofh"></div><div className="cbl_fs16 cbl_flx1" id={`t${id}_productname`}></div><div className="cbl_pr cbl_clsgrp"><div id={`t${id}_cls`} className="cbl_closbtn cbl_cp cbl_pa" onClick={close_fun}></div><div id={`t${id}flagdiv`}><dl id={`t${id}flag`} className="dropdown"></dl></div>
                        <svg className="cbl_cross"><use xlinkHref={`#t${id}cblcross`}>
                        </use></svg>
                    </div>
                    </div>

                    <div id={`t${id}_blchatbody`} className="cbl_windw cbl_df cbl_ofh"><div className="cbl_scrl" id={`t${id}_chatScroll`}><span></span></div><div className="cbl_windw_iner cbl_df cbl_fdc" id={`t${id}_scroll`}><div className="cbl_ques cbl_hi dn" id={`t${id}hi`} isstatic="yes"><div className="txt_area cbl_bg1" id={`t${id}_hiid`}>Hi</div></div><div id={`t${id}_new_chatbl`} className="cbl_ques_grp"></div></div></div>

                    <div id={`t${id}_tCond`} className="cbl_tnc cbl_pa dispNone"><input type="checkbox" className="bemt2" id={`t${id}_tCondCheckBox`} /><span className="bevtCss bedblk"><label className={`t${id}_test1`}>I agree to the </label><a href="https://www.indiamart.com/terms-of-use.html" target="_blank" className="betrmt">terms</a><label className={`t${id}_test1`}> and </label><a href="https://www.indiamart.com/privacy-policy.html" target="_blank" className="betrmt">privacy policy</a></span></div>
                    <div id={`t${id}_fBtn`} className="bepr"><div className="backdiv dispNone" id={`t${id}_backdiv`}><input value="Back" className="ber-backbtn" id={`t${id}_be-backbtn`} type="submit" /></div></div>

                    <div id={`t${id}_blchatfooter`} className="cbl_inpt cbl_pr cbl_df cbl_aic"><div className="cbl_whtbx cbl_pa cbl_bg1 cbl_br10"></div><div id={`t${id}cbl_bluline`} className="cbl_bluline cbl_pa cbl_br10"></div> <div className="cbl_dtls cbl_typhere dn" id={`t${id}_typehere`}><input readOnly name="type_field" className="cbl_name" placeholder="Type Here..." /></div> <div id={`t${id}_newblchatReply`} className="cbl_dtls"></div><div className="cbl_error cbl_pa" data-role=""><span id={`t${id}verify_error`}></span></div><div className="cbl_vrfy cbl_pa dn" data-role=""><span id={`t${id}cbl_msg`}>Verifying...</span></div><div className="cbl_sbmt_btn cbl_zi3 cbl_pa cbl_df cbl_aic cbl_jcc"> <button id={`t${id}_submit`} className="cbl_sbmtbt cbl_pa cbl_cp"></button> <svg className="cbl_sbmt"> <use xlinkHref={`#t${id}sendbtn`} /></svg> </div></div>
                </div>

                <div className={`t${id}blk_scrn blk_scrn dn`}></div>
                <div id={`t${id}_bl_form_wrapper_cta`} className="chat-blCta dispNone">
                    <span id={`t${id}message_indicator`} className="chat-ntify">1</span>
                    <i className="chat-CinBg chat-blCin"></i>
                    <p className="chat-rqtx" style={{ margin: '0' }}><span>Have a requirement?</span><br /><span className="getbestprice_span"> Chat with us </span></p>
                </div>

                <svg style={{ display: 'none' }} xmlns="http://www.w3.org/2000/svg">
                    <symbol viewBox="0 0 18 18" id={`t${id}cblcheck`}>
                        <path d="M0,9a9,9,0,1,1,9,9A9.01,9.01,0,0,1,0,9ZM1,9A8,8,0,1,0,9,1,8.01,8.01,0,0,0,1,9Zm7.377,2.888a.6.6,0,0,1-.366-.134L5.4,9.642a.6.6,0,0,1,.76-.939l2.2,1.784L11.828,6.06a.6.6,0,1,1,.951.743l-3.8,4.865a.605.605,0,0,1-.6.22Z"></path>
                    </symbol>
                    <symbol id={`t${id}cblcross`} viewBox="0 0 21.213 21.213">
                        <path d="M-632.745-856.141l-9.9,9.9-.707-.706,9.9-9.9-9.9-9.9.707-.707,9.9,9.9,9.9-9.9.707.707-9.9,9.9,9.9,9.9-.707.706Z" transform="translate(643.352 867.454)"></path>
                    </symbol>
                    <symbol viewBox="0 0 35 30" id={`t${id}sendbtn`}>
                        <path d="M0,68.25l35-15-35-15V49.917L25,53.25,0,56.583Z" transform="translate(0 -38.25)"></path>
                    </symbol>
                    <symbol viewBox="0 0 10 7" id={`t${id}dwnarow`}>
                        <path d="M5,0l5,7H0Z" transform="translate(10 7) rotate(180)"></path>
                    </symbol>
                </svg>
            </React.Fragment>
    );
    
}


const Hdrctbl_form = memo(Form_hdrctblMemo);
export default Hdrctbl_form;