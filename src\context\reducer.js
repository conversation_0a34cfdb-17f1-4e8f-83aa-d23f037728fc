const reducer = (state, action) => {
  switch (action.type) {
    case "UserData":
      return {
        ...state,
        UserData: action.payload.UserData,
      }
    case "prevtrack":
      return {
        ...state,
        prevtrack: action.payload.prevtrack,
      }
    case "currentscreen":
      return {
        ...state,
        currentscreen: action.payload.currentscreen,
      }
    case "frscr":
      return {
        ...state,
        frscr: action.payload.frscr,
      }
    // case "inlinRD":
    //   return {
    //     ...state,
    //     inlinRD: action.payload.inlinRD,
    //   }
    case "searchshown":
      return {
        ...state,
        searchshown: action.payload.searchshown,
      }
    case "Imeshform":
      return {
        ...state,
        Imeshform: action.payload.Imeshform,
      }
    case "NECcon":
      return {
        ...state,
        NECcon: action.payload.NECcon,
      }
    case "newEnq":
      return {
        ...state,
        newEnq: action.payload.newEnq,
      }
    case "progressCnt":
      return {
        ...state,
        progressCnt: action.payload.progressCnt,
      }
    case "progBar":
      return {
        ...state,
        progBar: action.payload.progBar,
      }
    case "RDNECcon":
      return {
        ...state,
        RDNECcon: action.payload.RDNECcon,
      }
    case "OTPcon":
      return {
        ...state,
        OTPcon: action.payload.OTPcon,
      }

    case "Isqform":
      return {
        ...state,
        Isqform: action.payload.Isqform,
      }
    case "IsqformBL":
      return {
        ...state,
        IsqformBL: action.payload.IsqformBL,
      }
    case "RDform":
      return {
        ...state,
        RDform: action.payload.RDform,
      }
    case "RDformBL":
      return {
        ...state,
        RDformBL: action.payload.RDformBL,
      }
    case "MrEnrForm":
      return {
        ...state,
        MrEnrForm: action.payload.MrEnrForm,
      }
    case "md_resp":
      return {
        ...state,
        md_resp: action.payload.md_resp,
      }
    case "thankyou":
      return {
        ...state,
        thankyou: action.payload.thankyou,
      }
    case "postreq":
      return {
        ...state,
        postreq: action.payload.postreq,
      }
    // case "postreqenqlocal":
    //   return {
    //     ...state,
    //     postreqenqlocal: action.payload.postreqenqlocal,
    //   }
    // case "localenqparam":
    //   return {
    //     ...state,
    //     localenqparam: action.payload.localenqparam,
    //   }
    case "openPopup":
      return {
        ...state,
        openPopup: action.payload.openPopup,
      }
    case "Inlnformparam":
      return {
        ...state,
        Inlnformparam: action.payload.Inlnformparam,
      }
    case "ImagedormParam":
      return {
        ...state,
        ImagedormParam: action.payload.ImagedormParam,
      }
    case "BLParam":
      return {
        ...state,
        BLParam: action.payload.BLParam,
      }
    case "openImgPopup":
      return {
        ...state,
        openImgPopup: action.payload.openImgPopup,
      }
    case "BLPopup":
      return {
        ...state,
        BLPopup: action.payload.BLPopup,
      }
    case "openBLPopup":
      return {
        ...state,
        openBLPopup: action.payload.openBLPopup,
      }
    case "multiImg":
      return {
        ...state,
        multiImg: action.payload.multiImg,
      }

    case "capsHit":
      return {
        ...state,
        capsHit: action.payload.capsHit,
      }

    case "userDetails":
      return {
        ...state,
        userDetails: action.payload.userDetails,
      }

    case "formsParamUpdate":
      return {
        ...state,
        PrevNext: action.payload.PrevNext,
      }

    case "enquirenow":
      return {
        ...state,
        enquirenow: action.payload.enquirenow,
      }

    case "singleimgsld":
      return {
        ...state,
        singleimgsld: action.payload.singleimgsld,
      }

    case "openinlnBLPopup":
      return {
        ...state,
        openinlnBLPopup: action.payload.openinlnBLPopup,
      }

    case "nextprev":
      return {
        ...state,
        nextprev: action.payload.nextprev,
      }
    case "progressstep":
      return {
        ...state,
        progressstep: action.payload.progressstep,
      }

    case "priceWidgetApiResponse":
      return {
        ...state,
        priceWidgetApiResponse: action.payload.priceWidgetApiResponse,
      }

    case "isqScreenReached":
      return {
        ...state,
        isqScreenReached: action.payload.isqScreenReached,
      }
    // case "srchapiresult":
    //   return {
    //     ...state,
    //     srchapiresult: action.payload.srchapiresult,
    //   }
    // case "secisqscreen":
    //   return {
    //     ...state,
    //     secisqscreen: action.payload.secisqscreen,
    //   }
    case "country_iso":
      return {
        ...state,
        country_iso: action.payload.country_iso,
      }        
    default:
      throw new Error();
  }

};

export { reducer };
