export default async function detectmycity(formData, url) {
    try {
        const response = await fetch(url, {
          method: 'POST',
          mode: 'cors', // Specify 'cors' mode to allow cross-origin requests
          body: formData // Pass form data as the request body
        });
  
      if (!response.ok) {
        throw new Error('Failed to call API');
      }
  
      const responseData = await response.json();
      return responseData;
      
    //   setApiResponse(responseData);
    } catch (error) {
      console.error('Failed to call API:', error);
    }
  }