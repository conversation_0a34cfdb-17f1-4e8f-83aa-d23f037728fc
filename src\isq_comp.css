.ber-dvtxt {
    color:#111;
    display: inline-block;
    position: relative;
    font-weight: 400;
    line-height: 15px;
    margin-bottom: 0;
    text-align: left;
    font-size: 15px;
}

.chkBox{
    background: #fff;
    height: 34px;
    line-height: 34px;
    border: 1px solid #c1c1c1;
    width: auto;
}
.chkBox{
    display: inline-block;
    box-sizing: border-box;
    position: relative;
    vertical-align: top;
    margin: 8px 13px 0px 0;
}
.chkBox {
    border-radius: 20px !important;
}
.eqVam {
    vertical-align: middle;
}
.chkBox input[type="radio"]{
    display: none;
}
.bepr {
    position: relative;
}
.chkBox label{
    display: block;
}
.bechkin {
    position: absolute;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 10px;
    border: 1px solid #a0a0a0;
    top: 8px;
}

.bechkin {
    background: #fff;
}

.beradio-sl {
    width: 6px;
    height: 6px;
    background: #2f3292;
    border-radius: 50%;
    margin: 0 auto;
    margin-top: 4px;
}
.dispNone {
    display: none!important;
}
.beisq3{
    font-size: 15px;
}
.ber-frmpop .beisq3{
    line-height: 33px;
    width: auto;
    color: #333;
    padding: 0px 10px 0px 30px !important;
}
.chkBox:hover{
    border-color: #029f93;
}
.chkBox:hover .bechkin{
    border-color: #029f93;
}
.chkBox:hover .beisq3 {
    color: #029f93;
}
.checkSl {
    background-color: #fff !important;
    border-color: #2e3192;
}
.checkSl .bechkin{
    border-color: #2e3192;
    background-color: #fff;
}
.checkSl span {
    color: #2e3192 !important;
}
.checkSl .beradio-sl {
    width: 8px;
    height: 8px;
    margin: 0 auto;
    margin-top: 3px;
    transform: unset;
    border: none;
    background-color: #2e3192;
    border-radius: 20px;
}


.reqbox .slbox.ber-txt:focus,.ber-slbox.ber-txtarea.rn:focus {
  border:1px solid #029f93 !important;
}




