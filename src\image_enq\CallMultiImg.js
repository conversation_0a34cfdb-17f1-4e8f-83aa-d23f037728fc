
import { isset } from '../common/formCommfun';
import MultiImgAPI from './MultiImageAPI';


export default async function CallMultiImg(form_param) {
    window.pdpMultires = '';

    if(form_param.itemid!=1){

        const responseData = await MultiImgAPI(form_param);

        if (
            responseData &&
            responseData.Response &&
            responseData.Response.Data &&
            Array.isArray(responseData.Response.Data) &&
            responseData.Response.Data.length >= 1 
        ) {
            const dataArray = responseData.Response.Data.map(item => {
                let displayImage = "";
                if (item.pc_item_image_1000x1000) {
                    displayImage = item.pc_item_image_1000x1000;
                } else if (item.pc_item_image_500x500) {
                    displayImage = item.pc_item_image_500x500;
                } else if (item.pc_item_image_250x250) {
                    displayImage = item.pc_item_image_250x250;
                } else {
                    displayImage = item.pc_item_image_125x125;
                }
    
                return {
                    displayImage: displayImage,
                    type: "image",
                    vidUrl: "",
                    zoomImage: displayImage,
                };
            });
            let imageUrl = isset(() => form_param.displayImage) && form_param.displayImage.includes("1000x1000") ?
                form_param.displayImage :
                isset(() => form_param.origImage) && form_param.origImage!='' ?
                    form_param.origImage :
                    isset(() => form_param.displayImage) ?
                        form_param.displayImage : "";
            dataArray.unshift({
                displayImage: imageUrl,
                type: "image",
                vidUrl: "",
                zoomImage: form_param.zoomImage,
            });
            window.pdpMultires = dataArray;
            return dataArray;
                
    
        }

    }

   
}
