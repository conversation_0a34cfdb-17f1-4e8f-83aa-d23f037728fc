import React, { useState, useEffect } from "react";
import {reqFormGATrackREC,currentISO,isset} from '../common/formCommfun';
import { useGlobalState } from "../context/store";
function Thankyoureg({ form_param }) {
    const { state } = useGlobalState();
    const [plaData, setPlaData] = useState([]);

    useEffect(() => {
        if((state.UserData && state.UserData.iso ? state.UserData.iso : currentISO())=='IN'){
            plawidget(form_param);
        }
       
    }, [form_param]);

    const retrieveData = (id) => {
        try {
          return JSON.parse(sessionStorage.getItem("plaWidget-" + id )) || null;
        } catch (err) {
          console.error("Failed to retrieve sessionStorage item:", err);
          return null;
        }
      };
    

    async function plawidget(form_param) {

        const widgetData = retrieveData(form_param.mcatId);
        if(!widgetData && isset(()=>form_param.mcatId) && form_param.mcatId!=-1){
            try {
                const parmObj = {
                    modid: form_param.modId,
                    mcatid: form_param.mcatId
                };
    
                const queryParams = new URLSearchParams(parmObj);
                const webAddressLocation = location.host;
                const ServerName = webAddressLocation.match(/^(dev|localhost)/) ? "dev-" : (webAddressLocation.match(/^stg/) ? "stg-" : "");
                const response = await fetch(`https://apps.imimg.com/index.php?r=Newreqform/WidgetDataNew&${queryParams}`);
    
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
    
                const data = await response.json();
                if (data && data.STATUS === 200 && data["RECOMMMENDATON_CATEGORY_BASED"] && data["RECOMMMENDATON_CATEGORY_BASED"].length > 0) {
                    sessionStorage.setItem("plaWidget-" + form_param.mcatId, JSON.stringify(data["RECOMMMENDATON_CATEGORY_BASED"]));
                    setPlaData(data["RECOMMMENDATON_CATEGORY_BASED"]);
                }
            } catch (error) {
                console.error('There was a problem with the fetch operation:', error);
                imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Widget_data','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
            }

        }
        else {
            setPlaData(widgetData);
          }
        
    }

    const capsdata = window.CapsData || [];

    // Determine how many items to take from plaData and capsdata
    const plaDataToShow = plaData.slice(0, 4);
    // const capsDataToShow = capsdata.slice(0, Math.max(0, 5 - plaDataToShow.length));
    let combinedData=[];
    if(currentISO()=='IN'){
        combinedData = plaData.concat(capsdata);
    }
    else{
        combinedData = capsdata;
    }
    combinedData= combinedData.slice(0, 4);
    if(plaData.length>= 4 && currentISO()=='IN'){
        reqFormGATrackREC("ThankYouShown_Ecom",form_param);
    }else if(plaData.length= 0 && capsdata.length >=4){
        reqFormGATrackREC("ThankYouShown_Caps",form_param);
    }else{
        reqFormGATrackREC("ThankYouShown_EcomCaps",form_param)
    }

    return (
        <>
        {isset(()=>combinedData) && combinedData.length>0 ? 
        
        ( <div id="plawid" className="idsf pdv20R eptb10">
    <div style={{ width: '100%' }}>
        <div className="eqs16 befwt BL_Fm8">More Products For You</div>
        <div id="pla-strip">
            {combinedData.map((elem, index) => {
                const { IMAGE_250X250, IMAGE_125X125, PRICE_F, PDP_URL, ITEM_NAME, ECOM_CART_URL, ECOM_ITEM_LANDING_URL, GLUSR_ID, COMPANY_URL, COMPANYNAME } = elem;
                const image = isset(()=>IMAGE_250X250) ? (IMAGE_250X250).replace("http://", "https://"):(isset(()=>IMAGE_125X125) ? (IMAGE_125X125).replace("http://", "https://") : "");
                const price = PRICE_F || "";
                const pdpUrl = PDP_URL.split("?");
                const name = isset(()=>ITEM_NAME) && ITEM_NAME.includes('"') ? ITEM_NAME.replace(/"/g, "") : ITEM_NAME;
                const ecomUrl = ECOM_CART_URL || ECOM_ITEM_LANDING_URL;
                const isCapsData = index >= plaDataToShow.length; // Determine if the item is from capsdata

                return (
                    <span key={index}>
                        <a href={`${pdpUrl}?ecom`}  target="_blank" className="cardsPla">
                            <div className="ecmpImgR">
                                <a href={`${pdpUrl}?ecom`} className="idsf pJc id_aic">
                                    <img alt={name} src={image} />
                                </a>
                            </div>
                            <div className="cardTxt">
                                {/* <div className="txtElp txtElp1 befs14 befwt beclr3 bemrg2"> {price}
                                </div> */}
                                {/* <div className="txtElp pdpU bemrg2">
                                    <a href={`${pdpUrl}?ecom`} className="eComtxt befs13 eplh16">{name}</a>
                                </div> */}


                                <div className="posR"><h4 className="namePla">{name}</h4></div>
                                
                                <p className="linecl_pdp">{price}</p>

                                {/* <div className="txtElp txtElp1 bemrg2">
                                    <a href={`${COMPANY_URL}?ecom`} className="befs11 ectxt63">{COMPANYNAME}</a>
                                </div> */}
                                {/* <a href={`${pdpUrl}?ecom`} className="emBynw" target="_blank">
                                    {isCapsData ? 'Contact Supplier' : 'Buy Now'}
                                </a> */}
                            </div>
                        </a>
                    </span>
                );
            })}
        </div>
    </div>
</div> ) : 

         (
             <div></div>
         )

}

        </>
        
    );
}

export default Thankyoureg;
