import React, { useState, useEffect, useRef } from 'react';
import { getparamValREC, LoginNewUiPDP, readCookieREC, shouldRenderPriceWidget } from '../common/formCommfun';
import { useGlobalState } from '../context/store';
import { flagResList } from '../CountryFlagList';

const CountrySuggester = ({country_iso, setcountry_iso,setSelectedCountry,selectedCountry,form_param}) => {
  const { dispatch } = useGlobalState();
  const [query, setQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);  // Reference to the dropdown

  const handleSearch = (event) => {
    setQuery(event.target.value);
  };

  const handleSelect = (country) => {
    setSelectedCountry(country);
    window.country = country.data.cname;
    window.countryiso = country.data.iso;
    setcountry_iso(country.data.iso);
    dispatch({ type: "progressCnt", payload: { progressCnt: country.data.iso } });
    setQuery('');
    setIsDropdownOpen(false);
  };

  const handleDropdownClick = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown if clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownRef]);

  const filteredCountries = flagResList.filter((country) =>
    country.label.toLowerCase().includes(query.toLowerCase())
  );

  return (
    <div className='country_drop_parent_cont'>
      <div>
        <div 
          className='country_drpnclick'
          onClick={handleDropdownClick}
        >
          <i className="oeWicn"></i>
          <p>Your Country is: </p>
          <span className='selected_cont'>{selectedCountry ? selectedCountry.data.cname : getparamValREC(readCookieREC('iploc'), 'gcnnm')?getparamValREC(readCookieREC('iploc'), 'gcnnm') : 'Select a country'}</span>
          <span style={{ marginRight: '10px' }}>▼</span>
        </div>

        {isDropdownOpen && (
          <div ref={dropdownRef} className='country_drpn'>
            <input
              type="text"
              placeholder="Search.."
              value={query}
              onChange={handleSearch}
              className='contsearch'
            />
            <ul style={{ listStyleType: 'none', margin: 0, padding: 0 }}>
              {filteredCountries.map((country) => (
                <li
                  key={country.value}
                  onClick={() => handleSelect(country)}
                  style={{ padding: '10px', cursor: 'pointer' }}
                ><span
                style={{
                  backgroundPosition: `0px -${11 * country.data.icon_order}px`,
                }}
              ></span>
                  {country.label}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {!shouldRenderPriceWidget(form_param) && LoginNewUiPDP(form_param) && country_iso == "IN"  &&  <span className='safetxt'>We don't call, only genuine sellers will contact</span>}
    </div>
  );
};

export default CountrySuggester;
