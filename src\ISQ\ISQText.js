import React, { useState, useEffect } from "react";
import { isset,smartcitySug} from "../common/formCommfun";
import IsqCitySuggester from "./isqCitysuggest";
function ISQText({ item, index, saveIsqs }) {
  const [inputValue, setInputValue] = useState(''); // State to hold the input value

  
  useEffect(() => {
    setInputValue('');
  }, [item,index,item.IM_SPEC_MASTER_DESC]);

  // useEffect(() => {
  //   const mcatid = window.forms_param.mcatId; 
  //   let userFilledData = [];
  //       try{
  //         userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
  //       }
  //       catch(e){
  //         userFilledData=[];
  //       }
  
  //       if (isset(()=>userFilledData) && isset(()=>userFilledData[mcatid])) {
  //     const filledData = userFilledData[mcatid];
  
  //     Object.keys(filledData).forEach((key) => {
  //       const value = filledData[key];
  //       if (key === item.IM_SPEC_MASTER_DESC) {       
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, item.IM_SPEC_OPTIONS_ID);
  //         setInputValue(value);         
  //       }
  //     });
  //   } 
  //   else if (window.forms_param && window.forms_param.plsqArr) {
  //     const params = window.forms_param.plsqArr.split('#');
  //     params.forEach(param => {
  //       const [encodedKey, encodedValue] = param.split(':');
  //       const key = decodeURIComponent(encodedKey);
  //       const value = decodeURIComponent(encodedValue);
  //       if (key === item.IM_SPEC_MASTER_DESC) {       
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, item.IM_SPEC_OPTIONS_ID);
  //         setInputValue(value);
         
  //       }
  //     });
  //   }
  // }, []);


  const handleInputChange = (event) => {
    const { value } = event.target;
    setInputValue(value);
    saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, item.IM_SPEC_OPTIONS_ID);
  };

  const handleCityonSugg = (e,v) => {
    const value = v.item && v.item.value;
    setInputValue(value);
    saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, item.IM_SPEC_OPTIONS_ID);
};

const shouldShowSuggester = smartcitySug(item.IM_SPEC_MASTER_DESC);
  return (
    <div className="widt100">
      {item.IM_SPEC_MASTER_TYPE === "1" ? (
        <div className="row-be txtRw" key={item.IM_SPEC_MASTER_ID}>
          <div className="bepr beclr">
            <div className="lbtool">
              <div className="beclr"></div>
              <div className="ber-dvtxt bewauto bepr tllb">
                <label id={`t0901ques${index}`} quesid={item.IM_SPEC_MASTER_ID} >
                  {item.IM_SPEC_MASTER_DESC}
                </label>
              </div>
              <div className="beclr"></div>
            </div>
          </div>
          <input
            type="text"
            className="ber-input beisq-tbx bg-white inpt_errorbx txtblng"
            id={`textIsq_opt_${item.IM_SPEC_MASTER_ID}`}
            name={`t0901textIsq_name${item.IM_SPEC_MASTER_ID}`}
            optionid={item.IM_SPEC_OPTIONS_ID}
            autoComplete="off"
            value={inputValue}
            onChange={handleInputChange}
            role="textbox"
            placeholder=""
          />
          {shouldShowSuggester ? <IsqCitySuggester elementId={`textIsq_opt_${item.IM_SPEC_MASTER_ID}`} onSelect={handleCityonSugg} /> : '' }
        </div>
      ) : (
        <div></div>
      )}
    </div>
  );
}

export default ISQText;
