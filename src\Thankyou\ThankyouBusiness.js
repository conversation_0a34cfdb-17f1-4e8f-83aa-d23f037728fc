import React from 'react';
import { reqFormGATrackREC,newTYform } from '../common/formCommfun';
const ThankyouBusiness = (form_param) => {
    return (
        <>
        {newTYform() ? <div className="idsf eptb10 ejcsv bemt5 bemb5 bgcol"><div id="manage_seller" className="txt16 thbtmnR"><span className="sellBll">Have a business? Bring it online with IndiaMART
            <a href="https://seller.indiamart.com/" target="_blank" className="tsnBtnR bemt15" id="t0901sellink" onClick={() => { reqFormGATrackREC("Seller Click", form_param) }}><i></i>Register for FREE</a></span>     
            </div></div> 
            : 
            <div className="idsf eptb10 ejcsv bemt5 bemb5"><div id="manage_seller" className="txt16 thbtmn ew50"><span className="befwt sellBl">Have a business? Bring it online with IndiaMART</span><a href="https://seller.indiamart.com/" target="_blank" className="tsnBtn idsf id_aic bemt15 sellBtn" id="t0901sellink" onClick={() =>{reqFormGATrackREC("Seller Click",form_param)}}><i></i>Register for FREE</a></div></div>
        
        }
        </>
        
        
    );
};

export default ThankyouBusiness;