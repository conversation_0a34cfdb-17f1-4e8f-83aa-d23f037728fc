import React, { useEffect, useRef, useState } from "react";
import "./stickyBL.css";
import InlnBlSub from "./InlnBlSub";
import InlnNm from "./InlnNm";
import { readCookieREC, getparamValREC, isset, Eventtracking } from "../common/formCommfun";
import LoginBLComponent from "../BL_Popup/Inlinebllogin";
import GDPRCountry, { isGDPRCountry } from "../Login/GDPRCountry";
import { validateEmailId, validatePhoneNumber } from "../Login/VldNmEm";
import LoginApiCall from "../Login/LoginApiCall";
import { useGlobalState } from "../context/store";

function StickyBL({ id, form_param }) {
    const [fn, setFn] = useState("");
    const [usrnm, setusrnm] = useState('');
    const [usrnmpr, setusrnmpr] = useState('');
    const [country_iso, setcountry_iso] = useState(window.countryiso);
    const [GDPRiso, setGDPRiso] = useState(isGDPRCountry(window.countryiso));
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [isGDPRcheck, setisGDPRcheck] = useState(false);
    const [GDPRerrmsg, setGDPRerrmsg] = useState("");
    const [mob_value, setmob_value] = useState("");
    const [email_val, setemail_val] = useState("");
    const [imeshExist, setImeshExist] = useState(readCookieREC('ImeshVisitor'));
    const targetRef = useRef(null);
    const imeshExistRef = useRef(imeshExist);
    const observerRef = useRef(null);
    const [err_msg, seterr_msg] = useState("");
    const { state, dispatch } = useGlobalState();

    // Create refs for the states
    const mobValueRef = useRef(mob_value);
    const emailValueRef = useRef(email_val);
    const countryIsoRef = useRef(country_iso);
    const lastProcessedValuesRef = useRef({ mob_value: "", email_val: "", country_iso: "" });
    const containerRef = useRef(null);
    // Update the refs whenever the state changes
    useEffect(() => {
        mobValueRef.current = mob_value;
        emailValueRef.current = email_val;
        countryIsoRef.current = country_iso;
    }, [mob_value, email_val, country_iso]);
    async function handleoutsideclick() {
        const ismshexist = readCookieREC("ImeshVisitor");
        if (!ismshexist) {
            const currentValues = {
                mob_value: mobValueRef.current,
                email_val: emailValueRef.current,
                country_iso: countryIsoRef.current,
            };
            const { mob_value, email_val, country_iso } = lastProcessedValuesRef.current;
            if ((countryIsoRef.current == "IN" && currentValues.mob_value !== mob_value) || (countryIsoRef.current != "IN" && currentValues.email_val !== email_val)) {
                let valid_check = "mob";
                let fld_val = mobValueRef.current;;
                if (countryIsoRef.current == "IN") {
                    valid_check = validatePhoneNumber(fld_val);
                } else {
                    valid_check = validateEmailId(emailValueRef.current);
                    fld_val = emailValueRef.current;
                }
                if (valid_check == "") {
                    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
                    const s_ip = getparamValREC(iploc, 'gip');
                    const loginres = await LoginApiCall(form_param, fld_val, 2, s_ip, countryIsoRef.current);
                    if (isset(() => loginres) && loginres.DataCookie) {
                        if (loginres.DataCookie.fn) {
                            setusrnmpr(true);
                            seterr_msg("");
                            return loginres.DataCookie.fn;
                        } else {
                            setusrnmpr(false);
                            return false;
                        }
                    }
                    return false;
                }
                lastProcessedValuesRef.current = currentValues;
                return false;
            }
            return false;
        }
    }
    useEffect(() => {
        // Intersection Observer Logic
        const handleIntersect = (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const currentImeshVisitor = readCookieREC('ImeshVisitor');
                    const loginval = currentImeshVisitor ? "-ProductImage" : "-UserLogin";
                    Eventtracking("DS1" + loginval, state.prevtrack, form_param, true);
                    if (currentImeshVisitor && !imeshExistRef.current) {
                        setImeshExist(currentImeshVisitor);
                    }
                }
            });
        };

        const observer = new IntersectionObserver(handleIntersect, {
            root: null, // Observe within the viewport
            threshold: 0.1, // Trigger when 10% of the component is visible
        });

        if (containerRef.current) {
            observer.observe(containerRef.current);
        }

        return () => {
            if (containerRef.current) {
                observer.unobserve(containerRef.current);
            }
        };
    }, []);
    useEffect(() => {
        imeshExistRef.current = imeshExist;
        if (imeshExist && observerRef.current && targetRef.current) {
            observerRef.current.disconnect();
        }
        if (imeshExist == null) {
            dispatch({ type: "Imeshform", payload: { Imeshform: false } });
        } else {
            dispatch({ type: "Imeshform", payload: { Imeshform: true } });
        }
        const visitorFn = getparamValREC(imeshExist, "fn") || (state.UserData?.fn ? state.UserData.fn : "");
        setFn(visitorFn);
    }, [imeshExist, state.UserData?.fn]);
    return (
        <div id="t0102_stickyBL" className="sticky-bl stickybl mr20 ibgc bdr5 inBxSh pr" ref={containerRef}>
            <div id="t0102_contactdiv" className="idsf iJSpb pfstrt sticky-bl__header">
                <div id="t0102_hdg1">
                    {country_iso === "IN" ? (
                        <>
                            Get Best Sellers for <b>{form_param.prodName || "This Product"}</b>
                        </>
                    ) : (
                        "Save Time! Get verified sellers exporting to your country"
                    )}
                </div>

            </div>
            <div className="sticky-bl__content">
                {imeshExist ? (
                    form_param?.displayImage ? (
                        <div className="t0102_sticky_imgwrap sticky-bl__image">
                            <img src={form_param.displayImage} alt={form_param.prodDispName || form_param.prodName || 'Product'} />
                        </div>
                    ) : null
                ) : null}

                {!imeshExist ?
                    <LoginBLComponent id={id} form_param={form_param} err_msg={err_msg} seterr_msg={seterr_msg} country_iso={country_iso} setcountry_iso={setcountry_iso} selectedCountry={selectedCountry} setSelectedCountry={setSelectedCountry} mob_value={mob_value} setmob_value={setmob_value} email_val={email_val} setemail_val={setemail_val} setGDPRiso={setGDPRiso} setusrnmpr={setusrnmpr} setusrnm={setusrnm} usrnmpr={usrnmpr} sticky={true}/> : ""}

                {fn == "" && country_iso != 'IN' ? <InlnNm country_iso={country_iso} usrnm={usrnm} setusrnm={setusrnm} err_msg={err_msg} id={id} usrnmpr={usrnmpr} handleoutsideclick={handleoutsideclick} sticky={true}/> : ""}

                {!imeshExist && GDPRiso ? <GDPRCountry id={id} setisGDPRcheck={setisGDPRcheck} setGDPRerrmsg={setGDPRerrmsg} /> : ""}
                {!imeshExist && GDPRerrmsg != "" && <div className="GDPRerr">{GDPRerrmsg}</div>}

                <InlnBlSub form_param={form_param} seterr_msg={seterr_msg} usrnm={usrnm} isGDPRcheck={isGDPRcheck} country_iso={country_iso} GDPRiso={GDPRiso} setGDPRerrmsg={setGDPRerrmsg} mob_value={mob_value} email_val={email_val} setusrnm={setusrnm} handleoutsideclick={handleoutsideclick} setImeshExist={setImeshExist} blsearchval={form_param.prodName} sticky={true}/>
            </div>

        </div>
    )
}
export default StickyBL;

