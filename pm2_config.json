{"name": "IM-Forms-React", "cwd": "/home3/indiamart/public_html/dev-forms_react/", "script": "npm run dev", "args": "", "interpreter": "node", "interpreter_args": "", "instances": 2, "exec_mode": "cluster", "watch": true, "ignore_watch": "", "max_memory_restart": "2G", "source_map_support": true, "log_date_format": "YYYYMMDDHHmmZ", "log_type": "json", "error_file": "/home3/indiamart/public_html]/forms_node_error.log", "out_file": "/home3/indiamart/public_html]/forms_node_output.log", "combine_logs": true, "merge_logs": true, "min_uptime": "5s", "max_restarts": 10, "restart_delay": 2000, "autorestart": true, "vizion": true, "force": false, "env": {"SERVER_ENV": "dev", "NODE_PORT": "3012"}}