
.imgslide{
    display: flex; height: 85vh;
  }
  
  
  
    .eqtstR .sliderImg {
      display: table;
      overflow: hidden;
      width: 83px;
      height:auto;
    }
  
  
    .eqitem {
      border-radius: 6px;
      overflow: hidden;
      display: inline-block;
      position: relative;
      border: 1px solid #eaeaea;
      box-sizing: border-box;
        width: 80px;
        height: 80px;
        margin-bottom: 21px;
    
    }

    .slideCss {
      width: 100px;
      overflow: hidden;
      margin-left: -10px;
  }
  
    .eqitem.active {
      border-color: #2e3192;
      border-width: 1px;
    }
  
    .eqtstR .eqitem.active {
      border-color: #2e3192;
      border-width: 1px;
    }
  
    .eqtstR .eqitem {
      width: 83px;
      height: 83px;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 12px;
      display: inline-block;
      position: relative;
      border: 1px solid #eaeaea;
      box-sizing: border-box;
    }
  
    .igTh {
      max-width: 79px;
      max-height: 79px;
    }
  
    .igTh {
      max-width: 99px;
      max-height: 99px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  
    .eqtstR .igTh {
      max-width: 83px;
      max-height: 83px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .sLidIgsm {
      position: relative;
      float: left;
        width: 90px;
        padding: 30px 10px 0 0;
    }
    .eq_img .sLidIgsm {
      padding-top: 36px;
    }
    .eqtstR .sLidIgsm {
      padding: 30px 0 0;
      margin: 0 15px;
      width: 83px;
      align-self: center;
    }
  
    
  .eqUpR,
  .eqDwnR {
    position: absolute;
    width: 10px;
    height: 10px;
    z-index: 1;
    background: transparent;
    border-top: 2px solid #000;
    border-left: 2px solid #000;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    left: 44px;
  }
  .eqUpR {
    transform: rotate(45deg);
    top: 8px;
  }
  .eqDwnR {
    transform: rotate(-132deg);
    bottom: -35px;
  }
  
  .eqUpR {
    top: 4px;
  }
  .eq_img .eqDwnR {
    bottom: -33px;
  }
  .eqUpR,
  .eqDwnR {
    left: 34px;
  }


  .eqtstR .eqUpR,
.eqtstR .eqDwnR {
  left: 37px;
}
.eqtstR .eqDwnR {
    bottom: -8px;
  }
  
  .yTub {
    background: rgba(0, 0, 0, 0.4);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
  }
  .instfbIc{
    background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDY0IDY0Ij4NCiAgICA8Y2lyY2xlIGN4PSIzMiIgY3k9IjMyIiByPSIzMCIgZmlsbD0idHJhbnNwYXJlbnQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iNCIgLz4NCiAgICANCiAgICA8cG9seWdvbiBwb2ludHM9IjI1LDE2IDI1LDQ4IDQ3LDMyIiBmaWxsPSJ3aGl0ZSIgLz4NCiAgPC9zdmc+")
      no-repeat center center !important;
    width: 80px;
    height: 80px;
    display: inline-block;
    margin: 0px auto;}   
