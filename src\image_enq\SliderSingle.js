import React, { useState, useEffect } from "react";
import './imgEnq.css';
import './slider.css';

const SliderSingle = ({ form_param}) => {

    

    return (
        <>
        <div id="t0901_slide" className="sLidIgsm" data-role="" >
            <div id="t0901_slideout" className="slideCss" data-role="">
                <span id="t0901_sliderImg" className="sliderImg">
                    <span id="t0901_slider0" className="eqitem active">
                        <img id="t0901_dispimage" className="igTh" src={form_param.displayImage} alt={form_param.displayImage} />
                    </span>
                </span>
            </div>
        </div>
        </>
    );
}

export default SliderSingle;
