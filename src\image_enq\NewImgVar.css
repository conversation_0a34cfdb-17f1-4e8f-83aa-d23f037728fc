.fullImgfr {
  padding: 0 !important;
  position: fixed;
  top: 0;
  left: 0;
  transform: unset !important;

}

.fullImgfr .ber-Lsc {
  background-color: black !important;
  padding: 0 !important;
  margin: 0 !important;
  height: 100vh !important;
  width: unset !important;
  margin: auto !important;
}

.fullImgfr .imgslide {
  width: unset !important;
  height: unset !important;
}

.fullImgfr .imgscroll.ber-mcont {
  height: 100vh !important;
  width: 100vw !important;
  background-color: black !important;
  max-height: 100vh !important;
}

.fullImgfr .ber-Rsc {
  position: absolute;
  top: 0;
  border-radius: 15px;
  background: white;
  bottom: 0;
  height: fit-content;
  margin: auto;
  padding: 0;
  z-index: 4;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.7), 0 1px 1px 0 rgba(0, 0, 0, .9);
  min-height: 450px !important;
}

.hidRight {
  right: -100%;
  transition: right 0.4s ease-in-out;
}

.slideOut {
  right: 55px;
}
.pdfformright{
  right: 85px;
}

.fullImgfr .pdpHgr,
.fullImgfr #t0901iframeVideo {
  height: 100vh !important;
  width: calc(100vw - 250px);
  border: none;
}

@media screen and (max-width: 1280px) and (min-width: 990px) {


  .fullImgfr .eqtstR.ber-mcont .pdpHgr {
    height: 100vh !important;
    width: calc(100vw - 250px);
    border: none;
  }
  

  .fullImgfr.fbImage:not(:has(> .blrpopR)) .eqtstR.ber-mcont .pdpHgr {
    width: calc(100vw - 25vw);
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 115px;
  }

}

.fullImgfr .pdpHgr img {
  /* height: 100vh!important;
    width:100vh; */
  max-width: 100%;
  max-height: 100% !important;


}

.fullImgfr .eqUp,
.fullImgfr .eqDwn {
  border-top: 2px solid white;
  border-left: 2px solid white;
}



.fullImgfr .eqitem {
  height: 100px;
  width: 100px;
  border-radius: 0px;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.fullImgfr .sLidIgsm {
  margin-right: 20px !important;
  width: 100px !important;
}

.fullImgfr .eqtstR .eqitem.active {
  border: 1px solid white !important;
}

.fullImgfr .eqtstR .igTh {
  max-height: 100%;
  max-width: 100%;
  position: unset;
  transform: unset;
}

.fullImgfr .instfbIc {
  width: 100px;
  height: 100px;
}

.soldDf {
  box-shadow: none;
  padding: 5px 0 0 0;
  margin: 0;
  display: flex;
}

.soldDf .eqsold.newCLsld,
.soldDf .sClr {
  color: #777 !important;
}

.fullImgfr .ber-cls-rec {
  position: fixed !important;
  left: 10px !important;
  top: 10px !important;
  right: 0px !important;

}

.fullImgfr #interact {
  position: fixed !important;
  right: 10px;
}


.fullImgfr button#t0901_nextbtnR {
  right: 2px;
}
.fullImgfr .country_drpn {
  width: 250px!important;
}

.fullImgfr button#t0901_prebtnR {
  left: 2px;
}

#upperDv {
  background-color: #007a6e !important;
  height: 35px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  color: white;
  font-size: 16px;
  padding: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

#colps {
  position: absolute;
  top: 8px;
  left: 11px;
  cursor: pointer;
  width: 21px !important;
  height: 21px !important;
  background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjEyMi44NzhweCIgaGVpZ2h0PSIxMjIuODhweCIgdmlld0JveD0iMCAwIDEyMi44NzggMTIyLjg4IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAxMjIuODc4IDEyMi44OCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+PHBhdGggZD0iTTEuNDI2LDguMzEzYy0xLjkwMS0xLjkwMS0xLjkwMS00Ljk4NCwwLTYuODg2YzEuOTAxLTEuOTAyLDQuOTg0LTEuOTAyLDYuODg2LDBsNTMuMTI3LDUzLjEyN2w1My4xMjctNTMuMTI3IGMxLjkwMS0xLjkwMiw0Ljk4NC0xLjkwMiw2Ljg4NywwYzEuOTAxLDEuOTAxLDEuOTAxLDQuOTg1LDAsNi44ODZMNjguMzI0LDYxLjQzOWw1My4xMjgsNTMuMTI4YzEuOTAxLDEuOTAxLDEuOTAxLDQuOTg0LDAsNi44ODYgYy0xLjkwMiwxLjkwMi00Ljk4NSwxLjkwMi02Ljg4NywwTDYxLjQzOCw2OC4zMjZMOC4zMTIsMTIxLjQ1M2MtMS45MDEsMS45MDItNC45ODQsMS45MDItNi44ODYsMCBjLTEuOTAxLTEuOTAxLTEuOTAxLTQuOTg0LDAtNi44ODZsNTMuMTI3LTUzLjEyOEwxLjQyNiw4LjMxM0wxLjQyNiw4LjMxM3oiIGZpbGw9IiNmZmZmZmYiLz48L2c+PC9zdmc+") no-repeat center center !important;
  background-color: none !important;
  background-size: 70% !important;
}

.fullImgfr .submit-button-img {
  width: 100% !important;
  margin-top: 30px !important;
  margin-bottom: 15px !important;
}

.fullImgfr #t0901screen1 .submit-button-img.subIn {
  margin-bottom: 100px !important;
}


.fullImgfr .blrpopR .submit-button-img {
  width: 57% !important;
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.fullImgfr .blrpopR #t0901screen1 .submit-button-img {
  margin-bottom: 0px !important;
}

.fullImgfr .blrpopR #t0901screen1 #t0901_gwrap{
  margin-top: 0px !important;
}


.fullImgfr .eprod {
  line-height: 25px;
  font-size: 18px;
  font-weight: 400;
}

.fullImgfr .eqpr {
  font-size: 17px;
  font-weight: 400;
}

.fullImgfr div#t0901_rightsection {
  max-height: calc(85vh - 30px);
  overflow-y: auto;
  overflow-x: hidden;
}

.fullImgfr .eqflot,
.fullImgfr .ber-txtarea,
.fullImgfr .lgcont,
.fullImgfr #textIsq_opt,
.fullImgfr .input-container {
  width: 100%;
  max-width: 100%;
}

.hrLine {
  border-bottom: 1px solid #ccc;
  width: 85%;
  margin: 10px 30px 15px 30px;
}

.fullImgfr #ttxtbx_option2,
.fullImgfr #ttxtbx_option1 {

  border-radius: 0px;
  background-color: white;
  color: black;
  font-size: 14px;
  border-left: 1px solid #c9c6c6;
}

.fullImgfr #ttxtbx_option2 {
  border-radius: 0px 7px 7px 0px;
  width: 55%;
}

.fullImgfr #ttxtbx_option1 {
  border-radius: 7px 0px 0px 7px;
  width: 45%;
}

.br77{
  border-radius: 7px !important;
}

.cNmSP {
  font-weight: 400;
  font-size: 16px;
}

.cNmSP,
.sendE {

  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 215px;
  height: 22px;
  word-wrap: break-word;
}

.sendE {
  display: block;
}

.btmCTA {
  height: 75px;
  padding: 2px 0px 0px 18px;
  color: white;
  cursor: pointer;
  position: absolute;
  right: 0;
  bottom: 0;
  margin: 13px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px 50px 0 50px;
  border: 2px solid #05867B;
  transition: all 0.3s ease-in-out;
  gap: 15px;
  z-index: 4;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.7), 0 1px 1px 0 rgba(0, 0, 0, .9);
}

.btmCTA:hover {
  border: 2px solid #05867B;
  box-shadow: 0 2px 4px #05867B;
}

.otSP {
  color: #05867B;
  text-align: left;
  max-width: 0;
  opacity: 0;
  overflow: hidden;
  /* transition: all 1s ease; */
}

.slideIn .otSP {
  max-width: 300px;
  opacity: 1;
  transition: all 1s ease;
  margin-right: 20px;
}

.fullImgfr .blrpopR .hrLine,
.fullImgfr .blrpopR #t0901_rightproddetails,
.fullImgfr .blrpopR #upperDv {
  display: none;
}

.fullImgfr .blrpopR .ber-Rsc {
  box-shadow: none;
  background: transparent;
}





.fullImgfr .blrpopR #t0901_questionouterwrapper {
  left: -125px;
  transform: translateY(-50%);
  width: 450px !important;
}

.blrpopR #fullscreen,
.blrpopR #download,
.blrpopR #zoomin,
.blrpopR #zoomout {
  pointer-events: none;
  opacity: 0.5;
}

.fullImgfr .bemlsecR {
  background-color: white;
}

.webKflow {
  -webkit-box-orient: vertical;
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
}



.fullImgfr.fbImage:not(:has(> .blrpopR)) .ber-Lsc{
  margin: 0 !important;
}

.fbImage:not(:has(> .blrpopR)) .eqtstR .pdpHgr:before {
   height: 100vh;
   background:rgba(0, 0, 0, 0.3);
}
@media screen and (max-width: 1280px) and (min-width: 990px) {
  .fbImage:not(:has(> .blrpopR)) .eqtstR.ber-mcont .pdpHgr,
  .fbImage:not(:has(> .blrpopR)) .eqtstR .imgslide,
  .fbImage:not(:has(> .blrpopR)) .eqtstR .pdpHgr:before,
  .fbImage:not(:has(> .blrpopR)) .eqtstR .ber-Lsc.enqImg,
  .fbImage:not(:has(> .blrpopR)) .eqtstR .imgslide {
    height: 100vh !important;
    background:rgba(0, 0, 0, 0.3);
  }
}
.fbImage:not(:has(> .blrpopR)) #upperDv{
  border-top-left-radius:unset;
  border-top-right-radius:unset;
  height: 42px;
  font-size: 17px;
}

.fullImgfr.fbImage:not(:has(> .blrpopR)) .ber-Rsc {
  right: 0;
  border-radius: unset;
  height: unset;
  width: calc(100vw - 75%)!important;
}

.fullImgfr.fbImage:not(:has(> .blrpopR)) div#t0901_rightsection {
  max-height: 90vh;
  padding-left: 4px;
}

.fbImage:not(:has(> .blrpopR)) #colps{
  display: none;
}
.fullImgfr.fbImage:not(:has(> .blrpopR)) button#t0901_prebtnR {
  left: 2px;
}
.fullImgfr.fbImage:not(:has(> .blrpopR)) button#t0901_nextbtnR {
  right: calc(100vw - 75% + 2px);
}
.fullImgfr.fbImage:not(:has(> .blrpopR)) button#t0901_nextbtnR,
.fullImgfr.fbImage:not(:has(> .blrpopR)) button#t0901_prebtnR {
  z-index: 999;
  top: calc(100% - 40px)
}
.fullImgfr.fbImage:not(:has(> .blrpopR)) .sLidIgsm {
  display: flex;
  flex-direction: column;
  z-index: 999;
  align-items: center;
  width: 45% !important;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.fbImage:not(:has(> .blrpopR)) .slideCss{
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: center;
}

.fbImage:not(:has(> .blrpopR)) .sliderImg{
  display: flex;
  flex-direction: row;
  width: unset;
}

.fullImgfr.fbImage:not(:has(> .blrpopR)) .pdpHgr,
.fullImgfr.fbImage:not(:has(> .blrpopR)) #t0901iframeVideo {
  width: calc(100vw - 25vw);
}
.fbImage:not(:has(> .blrpopR)) #t0901_zoomimage{
  top: 0;
  /* transform: translateX(-50%)!important; */
  max-height: calc(100vh - 115px)!important;
  position:unset;

}
.fbImage:not(:has(> .blrpopR)) #t0901_prodmediaR{
  position: relative;
}
.fbImage:not(:has(> .blrpopR)) .eqDwn{
  transform: rotate(132deg);
  top:75px;
  left:95%;
}
.fbImage:not(:has(> .blrpopR)) .eqUp{
  transform: rotate(-45deg);
  top:75px;
  left:0;

}
.fbImage:not(:has(> .blrpopR)).fullImgfr #interact{
  right: calc(100vw - 75vw);
}
.fbImage:not(:has(> .blrpopR)) #interact button{
  width:40px;
  height: 40px;
}
.fbImage:not(:has(> .blrpopR)) .eqtstR .pdpHgr .blrImg {
  filter: blur(28px);
  -webkit-filter: blur(28px);
  position: absolute;
  left: 0;
  top: 0;
}
.fbImage:not(:has(> .blrpopR)) .closeScrl {
  /* width: 25px !important;
    height: 25px !important; */
    background-color: rgb(31 31 31 / 70%) !important;
    background-size: 40% !important;
    border-radius: 50%;
    left: unset !important;
    top: 5px !important;
    right: 5px !important;
  }

.fullImgfr.fbImage:not(:has(> .blrpopR)) .eqitem,
.fullImgfr.fbImage:not(:has(> .blrpopR)) .instfbIc {
  height: 83px;
  width: 83px;
}

.fullImgfr.fbImage:not(:has(> .blrpopR)) .pdpHgr{
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 115px;

}
 