import React, { useEffect } from "react";
import '../Login/loginmob.css'
import { isGDPRCountry } from "../Login/GDPRCountry";
import CountrySuggester from "../Login/CountrtDrpdwn";
import PhEmError from "../Login/PhEmError";
function LoginBLComponent({id, form_param, err_msg, seterr_msg, country_iso, setcountry_iso, selectedCountry, setSelectedCountry, mob_value, setmob_value, email_val, setemail_val, setGDPRiso , setusrnmpr , setusrnm, usrnmpr ,sticky}) {
    useEffect(() => {
        seterr_msg("");
        window.countryiso = country_iso;
        setGDPRiso(isGDPRCountry(country_iso));
    }, [country_iso])

    let err = err_msg && err_msg != "Please enter your requirement" && err_msg != "Please enter you name" ? "ered" : "";
    let numem = country_iso == "IN" ? "number" : "email";
    const handlemobChange = (event) => {
        setmob_value(event.target.value);
        if(usrnmpr){
            setusrnm("");
        }
        setusrnmpr(false);
    };
    const handleemChange = (event) => {
        setemail_val(event.target.value);
        setusrnmpr(false);
    };
    const handleClick = () => {
        if (err_msg !== "") {seterr_msg("");}
    };
    const isNumberKey = event => {
        const charCode = event.which || event.keyCode;
        return charCode >= 48 && charCode <= 57;
    };
    const handleKeyPress = (event) => {
        if (!isNumberKey(event)) {
            event.preventDefault();
        }
    };
    return (
        <>
            <div className={`enqLogIn bemlsecR3 idsf mt0`} >
                {!sticky && <label >{country_iso == "IN" ? "Mobile Number" : "Email ID"}<span className="redc">*</span></label>}

                <div className={`width100 pr`}>
                    {err_msg && err_msg != "Please enter your requirement" && err_msg != "Please enter you name" && <PhEmError form_param={form_param} id={id} err_msg={err_msg} />}
                    {country_iso == "IN" ?
                        <div className={`input-containermob ${err}`}>
                            <span className={`country-code ${err}`}>+91</span>
                            <input type="text" id={`${id}_mobile-inline-bl`} name="mobile" className="mobile-input" placeholder="Enter your mobile" maxLength="10" onClick={handleClick} onChange={handlemobChange} onKeyPress={e => handleKeyPress(e)} value={mob_value} />
                        </div>
                        :
                        <div className={`input-containermob ${err}`}>
                            <span className={`country-code emailbox ${err}`}></span>
                            <input type="text" id={`${id}_email-inline-bl`} name="email" className="mobile-input" placeholder="Enter your Email" maxLength="100" onClick={handleClick} onChange={handleemChange} value={email_val} />
                        </div>}
                        <div className="be-msghlp" >Supplier will contact you on this {numem}</div>
                    <CountrySuggester country_iso = {country_iso}setcountry_iso={setcountry_iso} setSelectedCountry={setSelectedCountry} selectedCountry={selectedCountry} form_param={form_param}/>
                </div>
            </div>
        </>
    );

}
export default LoginBLComponent;




