import React, { useState } from 'react';
import './progressbar.css';
import { currentISO ,isset,readCookieREC} from '../common/formCommfun';
import { useGlobalState } from "../context/store";

const ProgressBar = ({ progress }) => {
    const { state, dispatch } = useGlobalState();
    const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
    var curr= imesh=='' ? (state.progressCnt || currentISO()) :  state.UserData.iso;
    // var curr= state.progressCnt || currentISO() ;
    

    const labels = curr=='IN' ? [
        'Select Product',
        'Log In',
        'User Info',
        'Get Verified',
        'Specify Details'
    ] : [
        'Select Product',
        'Log In',
        'User Info',
        'Specify Details'
    ] ;

    return (
        <>  <div className="progress-bar-container">
            <div className="progress-bar-enqforms">
            {(curr === 'IN' ? [1, 2, 3, 4, 5] : [1, 2, 3, 4]).map((step, index) => {
                    // Calculate progress percentage (e.g., 30%, 60%, etc.)
                   
                    var val = curr=='IN' ? 4 : 3;
                    const progressPercentage = state.progressstep > val? Math.floor((state.progressstep - val) * 100)  : 0;
                    
                    
                    return (
                        <React.Fragment key={index}>
                            <div className="progcircle-container wd20">
                            <div
                                className={`progcircle ${index < state.progressstep ? 'completed' : ''} ${state.progressstep> val && index >= val ? 'perrep' : ''}`}
                                style={index >= val ? { '--progress': progressPercentage } : {}}
                                {...(index >= val ? { 'data-progress': progressPercentage } : {})}
                            >
                                    {index < state.progressstep ? (
                                        <svg width="12" height="11" viewBox="0 0 16 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M1 5.4L5.4 9.8L14.2 1" stroke="white" strokeWidth="1.76" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    ) : <div className="proginner-circle"></div>}
                                </div>
                                <div className={`labelPro labelcolorPro`}>{labels[index]}</div>
                            </div>
                        </React.Fragment>
                    );
                })}
            </div>
        </div> </>
        
       
    );
};

export default ProgressBar;
