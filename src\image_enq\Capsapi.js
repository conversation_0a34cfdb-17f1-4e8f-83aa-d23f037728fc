
export default async function CapsAPI(formpar) {

    const disppId=formpar.pDispId;
    const webAddressLocation = location.hostname;
    var serverName = webAddressLocation.match(/^dev/)
    ? "//apps.imimg.com/"
    : webAddressLocation.match(/^stg/)
      ? "//stg-apps.imimg.com/"
      : "//apps.imimg.com/";    
    var url = serverName + `index.php?r=Newreqform/RelatedNewCaps&modid=${formpar.modId}&displayId=${formpar.pDispId}`;
    // console.log(url);
   
        
    try {
        const response = await fetch(url, {
          method: 'GET',  
          mode: 'cors', 
          cache: 'no-store'
        });
  
      if (!response.ok) {
        throw new Error('Failed to call API');
      }
      // console.log(response)

      // const responseData = await response.json();
      const text = await response.text();
    const responseData = text ? JSON.parse(text) : {};

    // console.log(responseData);
    return responseData;

    //   setApiResponse(responseData);
    } catch (error) {
      console.error('Failed to call API:', error);
      imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'caps_Failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
  }