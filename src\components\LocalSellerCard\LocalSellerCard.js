import React, { useState, useEffect } from 'react';
import { Eventtracking, readCookieREC } from '../../common/formCommfun';
import './LocalSellerCard.css';
import SaveisqAPI from '../../ISQ/SaveisqAPI';
import callPostreq from '../../callpostreq';
import { useGlobalState } from '../../context/store';

const LocalSellerCard = ({ form_param, searchAPIdata, selectedOptions }) => {
    const { state, dispatch } = useGlobalState();
    // Remove showConfirmation and unmounting logic
    const [yesSelected, setYesSelected] = useState(false);
    let enableLinks = false;
    // if (isEvenDigit) {
    //     enableLinks = true;
    // }
    if (!searchAPIdata || searchAPIdata.length === 0) {
        return null;
    }

    // Get the first product from the API response
    const firstProduct = searchAPIdata[0];
    const {
        desktop_title_url, title, catalog_url, companyname, city, supplier_rating,mcatid,mcatname,displayid,catid,original_title,glusrid
    } = firstProduct.fields;
    const localenqparam = {
        modId: form_param.modId,
        formType : form_param.formType,
        pdpTemplate: form_param.pdpTemplate,
        ctaName: form_param.ctaName,
        ctaType: form_param.ctaType,
        section : form_param.section,
        position : form_param.position,
        mcatName : mcatname[0],
        sllrRtng : supplier_rating,
        prodName : original_title,
        afflId : form_param.afflId,
        pDispId : displayid,
        mcatId : mcatid[0],
        catId : catid[0],
        prodDispName : title,
        rcvGlid : glusrid,
        modrefType : form_param.modrefType,
        prodServ : form_param.prodServ,
        fromcard : true,
    }
    let pdpUrl = desktop_title_url && desktop_title_url.split("?");
    pdpUrl = pdpUrl ? pdpUrl[0] : "";
    useEffect(() => {
        Eventtracking("DS" + window.screencount + "-LocalSellerCard", state.prevtrack,localenqparam , true);
    }, []);
    const handleYesClick = async () => {
        setYesSelected(true);
        dispatch({ type: 'localenqparam', payload: { localenqparam: localenqparam } });
        let qid = await callPostreq(localenqparam);
        if (qid !== '') {
            dispatch({ type: 'postreqenqlocal', payload: { postreqenqlocal: qid } });
        }
        if (selectedOptions.length > 0) {
            const b_response = selectedOptions.map(opt => opt.b_response);
            const q_desc = selectedOptions.map(opt => opt.q_desc);
            const q_id = selectedOptions.map(opt => opt.q_id);
            const b_id = selectedOptions.map(opt => opt.b_id);

            SaveisqAPI(localenqparam, qid, b_response, q_desc, q_id, b_id);
        }

        Eventtracking("SS" + window.screencount + "-LocalSeller_YesClicked", state.prevtrack, localenqparam, false);
    };

    const handleNoClick = () => {
        Eventtracking("SS" + window.screencount + "-LocalSeller_NoClicked", state.prevtrack, localenqparam, false);
    };

    return (
        <div className={`local-seller-card`}>
            {/* Header Section */}
            <div className="text">
                Send Enquiry to Local <span className="city-name">{city.toUpperCase()}</span> Seller - 
                {enableLinks ? (
                    <a href={`${catalog_url}?ecom`} target="_blank" onClick={() => Eventtracking("LocalSeller_SellerName", state.prevtrack, localenqparam, false)} className="seller-name">{companyname}</a>
                ) : (
                    <span className="seller-name-no-link"> {companyname}</span>
                )}
            </div>

            {/* Action Radio Buttons */}
            <div className="action-section">
                <div className="action-buttons">
                    <label className="radio-option">
                        <input
                            type="radio"
                            name="localSellerChoice"
                            value="yes"
                            onChange={handleYesClick}
                            className="radio-input"
                            checked={yesSelected}
                            readOnly
                        />
                        <span className={`radio-custom yes-radio${yesSelected ? ' ticked' : ''}`}>{yesSelected && <span className="tick-mark-radio">&#10004;</span>}</span>
                        <span className="radio-label">Yes</span>
                    </label>
                    <label className={`radio-option${yesSelected ? ' no-disabled' : ''}`}>
                        <input
                            type="radio"
                            name="localSellerChoice"
                            value="no"
                            onChange={handleNoClick}
                            className="radio-input"
                            disabled={yesSelected}
                        />
                        <span className="radio-custom no-radio"></span>
                        <span className="radio-label">No</span>
                    </label>
                </div>
            </div>
        </div>
    );
};

export default LocalSellerCard;
