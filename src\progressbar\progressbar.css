.progress-bar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 20px;
  }
  .progress-bar-enqforms {
    display: flex;
    width: 100%;
    position: relative;
  }
  .progcircle-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
    position: relative;
    text-align: center;
  }
  /* .progcircle[data-progress="30"] {
    --progress: 30; 
  } */

  .progcircle {
    width: 20px;
    height: 20px;
    border: 3px solid #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    background-color: #a19e9e;
    color: #333;
    z-index: 1;
    /* transition: background-color 0.3s ease; */
  }
  .progcircle.perrep {
    /* This is the base gradient, where green represents the progress */
    background: conic-gradient(#007A6E calc(var(--progress, 0) * 1%), #ccc 0);
  }

  .progcircle.completed {
    background-color: #007A6E;
    color: #fff;
    border-color: #007A6E;
  }
  
  .progcircle-container:last-child::before{
    display: none
   }

  .progcircle .progtick {
    font-size: 14px;
    color: white;
  }
  
  .labelPro {
    margin-top: 8px;
    font-size: 11px;
    text-align: center;
  }
  .progcircle-container::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    width: 100%;
    height: 3px;
    background-color: #ccc;
    z-index: 0;
}
  .mt33{
    margin-top: 33px;
  }

  .wd20{
    width: 20%;
  }

  .labelcolorPro{
    font-weight: bold;
    color: #757575;
  }
  .proginner-circle {
    width: 15px;           /* Size of inner circle */
    height: 15px;
    background-color: white; /* Inner circle color */
    border-radius: 50%;
}

.enqsnt {
    color: #007A6E;
  }
  