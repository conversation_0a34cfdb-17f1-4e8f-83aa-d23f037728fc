import React, { useEffect, memo } from 'react';

const ImageAnchorAd = () => {
  useEffect(() => {
    if (!window.googletag) {
      console.log('GPT library not loaded');
      return;
    }

    let anchorSlot;

    try {
      window.googletag.cmd.push(() => {
        anchorSlot = window.googletag.defineOutOfPageSlot(
          "/3047175/Desktop_AnchorAd_ImageForm",
          window.googletag.enums.OutOfPageFormat.BOTTOM_ANCHOR
        );

        if (anchorSlot) {
          anchorSlot.addService(window.googletag.pubads());
          window.googletag.enableServices();
          window,googletag.display(anchorSlot);
        }
      });
    } catch (error) {
      console.log('Anchor ad initialization error:', error);
    }

    return () => {
      if (window.googletag && anchorSlot) {
        window.googletag.cmd.push(() => {
          window.googletag.destroySlots([anchorSlot]);
        });
      }
    };
  }, []);

  return null; // No need for any div here
};

export default memo(ImageAnchorAd);
