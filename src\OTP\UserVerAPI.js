import React, { useEffect, useState } from 'react'
import {getparamValREC,isset,readCookieREC,ReturnCorrectVal,checkblockedUser, setCookieREC} from '../common/formCommfun';
export default async function UserVerAPI(resend,type,otpentered) {

  const imesh_cookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
  const imissCookie = isset(() => readCookieREC('imiss')) ? readCookieREC('im_iss') : '';
  const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
  const webAddressLocation = location.hostname;
  const data={
    s_mobile: getparamValREC(imesh_cookie,"mb1") || "",
    modid:  window.forms_param.modId,
    s_country_code: '+91',
    s_country_iso: 'IN',
    s_glusrid: getparamValREC(imesh_cookie,"glid"),
    flag: window.forms_param.formType.toString().toLowerCase(),
    OTPResend: resend, 
    verify_screen: 'default',
  };
  
  if (type === 1) { 
    /* for OTP sending*/ data["todoflag"] = "OTPGen";
  } else if (type === 2) {
    /* for OTP Validation */ data["todoflag"] = "OTPVer";
    data["verify_process"] = "ONLINE";
    data["auth_key"] = otpentered;
    // data["s_authkey"] = authkey;
  } 
  var appsServerName = webAddressLocation.match(/^dev/)
  ? "//apps.imimg.com/"
  : webAddressLocation.match(/^stg/)
    ? "//stg-apps.imimg.com/"
    : "//apps.imimg.com/";
  var verUrl = appsServerName + "index.php?r=Newreqform/LoginVerification";
   
  
  var _case = checkblockedUser() && imissCookie== "" ? 2 : 1;
  // var _case = 1;
  var ajaxData = {
    token: "imobile@15061981",
    mobile_num: data["s_mobile"],
    attribute_id: "121",
    // mobile_num: '7985227444',
    modid: data["modid"],
    // user_mobile_country_code:  ReturnCorrectVal(data["s_country_code"], "91"),
    flag: ReturnCorrectVal(data["todoflag"], "OTPGen"),
    user_ip: ReturnCorrectVal(getparamValREC(iploc, 'gip'),""),
    user_country:  data["s_country_iso"],         
    glusrid: ReturnCorrectVal(data["s_glusrid"], ""),
  };  

  if (data["flag"] == "bl") {
    ajaxData["process"] = "OTP_PBRForm_Desktop";
    ajaxData["user_updatedusing"] = "PBRForm";
    if (type==2) {
      ajaxData["verify_screen"] = "DESKTOP PBR FORM";
    }
  } else {
    ajaxData["process"] = "OTP_CentralisedEnqForm_Desktop";
    ajaxData["user_updatedusing"] = "DESKTOP ENQUIRY FORM";
    if (type==2) {
      ajaxData["verify_screen"] = "DESKTOP ENQUIRY FORM";
    }
  }
  ajaxData["OTPResend"] = data["OTPResend"];
  if(type==2){
    ajaxData["verify_process"] = "ONLINE";
    ajaxData["auth_key"] = otpentered;
  }
  
  const formData = new URLSearchParams();
  formData.append('data', JSON.stringify(ajaxData));
  formData.append('flag', 'otp');
      try {
          const response = await fetch(verUrl, {
            method: 'POST',
            mode: 'cors', // Specify 'cors' mode to allow cross-origin requests
            body: formData // Pass form data as the request body
          });
    
        if (!response.ok) {
          throw new Error('Failed to call API');
        }
    
        const responseData = await response.json();
        if (responseData?.Response?.Code == 200 && responseData?.Response?.LOGIN_DATA?.DataCookie) {
          setCookieREC('ImeshVisitor', responseData.Response.LOGIN_DATA.DataCookie, 365);
        }      
        return responseData;
      } catch (error) {
        console.error('Failed to call API:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'OtpAPI','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
      }

  
  
}
