import React from 'react';
import {isset, readCookieREC, getparamValREC} from '../common/formCommfun';
export async function callSaveEnrichAPI(enrichDesc, offer_id, query_destination, form_param) {
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
        glid = getparamValREC(imesh, 'glid');
        const baseUrl = "https://apps.imimg.com/index.php";
        const queryParams = new URLSearchParams({
            r:'Newreqform/saveEnrichment',
            offer_id: offer_id,
            s_glusrid: glid,
            modid: form_param && form_param.modId ? form_param.modId : 'DIR',
            _: new Date().getTime()
        });
        if(enrichDesc !=''){
            queryParams.append('enrichDesc', enrichDesc);
        }
        if(form_param.formType == 'BL'){
            queryParams.append('blEnqFlag', "BL");
            queryParams.append('updatevalue', "updatevalue");
        }else{
            queryParams.append('blEnqFlag', "Enq");
            queryParams.append('r_glusrid', form_param.rcvGlid);
            queryParams.append('q_dest', query_destination);
        }
        const url = `${baseUrl}?${queryParams.toString()}`;

        try {
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Failed to call API:', error);
            imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'saveEnrichment_Failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
        }
    };