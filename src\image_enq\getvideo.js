import { isset,loadInstaScriptREC,loadScriptREC } from '../common/formCommfun';

export default async function getVideo(VideObj,setPiframe, setReset,setLoader) {
    // console.log('called');
let videoKey= isset(()=>VideObj.data["vidKey"]) ? VideObj.data["vidKey"] : "";
if(videoKey == "" || videoKey == "1" || videoKey == "2"){
    if (
      YT.loading === 1 &&
      YT.loaded === 1 &&
      !(
        isset(()=>$("#videoProd")[0].src) &&
        $("#videoProd")[0].src !== ""
      )
    ) {
      window.IframeApiloaded = 2;
      let new_player = new YT.Player("videoProd", {
        width: "500",
        height: "500",
        playerVars: {
          rel: 0,
          autoplay: 1,
        },
        videoId: VideObj.data.vidUrl,
        events: {
          onReady: function (event) {
            setLoader(false);
            setPiframe(false);
          },
          onStateChange: function (event) {
            // $("#t0901_imglodr").addClass("dispNone");

            if (
              isset(()=>new_player) &&
              typeof new_player.getPlayerState === "function" &&
              new_player.getPlayerState() === 0
            ) {
              new_player.seekTo(0);
              new_player.stopVideo();
            }
          },
        },
      });
      setPiframe(true);
      setReset(false);
    }
  } 
  else if(videoKey == "3"){
    //fb
    if (!( isset(()=>$("#videoProd")[0].src) && $("#videoProd")[0].src !== "" )){

      let divProd = document.getElementById("videoProd");
      let clAdd=divProd.className;
      if(!$("#t0901_mcontR").hasClass("eqRes")){
        clAdd+=' pdpHgyt';
      }
      let iframeElement = getFbIframe(clAdd, "videoProd");
      divProd.replaceWith(iframeElement);     
      const iframe = document.getElementById("videoProd");
      iframe.addEventListener("load", function() { 
            setLoader(false); 
            setPiframe(false);        
          });
      let encodedURL = encodeURI(VideObj.data.vidUrl);
      iframe.src=  'https://www.facebook.com/plugins/video.php?href=' + encodedURL + '&width=500&show_text=false&height=500&appId'  ;
      setPiframe(true);
      setReset(false);
    }
           
  }
  else if(videoKey == "5" || videoKey == "6"){
    //insta
    if (!( isset(()=>$("blockquote#videoProd")) && $("blockquote#videoProd").length>0)){  
      let videoId=0;
      let divProd = $("#videoProd"); 
      let instaProdVid = '';    
      let pattern = /\/(p|reel)\/([\w-]+)\//;
      let matches = VideObj.data.vidUrl.match(pattern);
    
      if (matches) {
        videoId = matches[2];
      }
    
      let insCl= $("#t0901iframeVideo").hasClass("eIfvm") && !$("#t0901_mcontR").hasClass("eqRes") ? "newImgIns" : "";
      let afterid = '/?utm_source=ig_embed&amp;utm_campaign=loading';
      let instaVersion = (videoKey === "5") ? '12' : '14';
      let instaBlockquote = '<blockquote class="instagram-media stopV ifrIns" id="videoProd" data-instgrm-permalink="https://www.instagram.com/'; 
      instaBlockquote += (videoKey === "5") ? 'p/' : 'reel/';
      instaBlockquote += videoId + afterid + ' data-instgrm-version="' + instaVersion + '"' + ' style={{ background: "#FFF", border: 0, borderRadius: "3px", boxShadow: "0 0 1px 0 rgba(0,0,0,0.5), 0 1px 10px 0 rgba(0,0,0,0.15)", margin: "-55px 0 -167.5px", minWidth: "540px", padding: 0, width: "calc(100% - 2px)", WebkitWidth: "calc(100% - 2px)" }}></blockquote>';
      instaProdVid += '<div id="insf-video" className="' + insCl + '"><div id="inside-insf-video">' + instaBlockquote + '</div></div>';    
    

      divProd.replaceWith(instaProdVid);
       let attempts = 0;        

      let insInt = setInterval(function() {
        attempts++;
        let checkIn=$(".instagram-media");
        if (checkIn.is("iframe")) {
          const iss = document.getElementsByClassName("instagram-media");  
          let tempI=iss[0].src;
          iss[0].src='';
          iss[0].addEventListener("load", function() {
              setLoader(false); 
              setPiframe(false);                       
            });
          iss[0].src=tempI;       
          clearInterval(insInt);                        
        }
          if (attempts >= 30) {
          clearInterval(insInt);
           setLoader(false);
           setPiframe(false);
           setReset(true);
        }
      }, 100);
      setPiframe(true);
      setReset(false);

    }
  }

}


const getFbIframe = (clsad,ifrId) => {
  let ifrEle = document.createElement("iframe");
  ifrEle.id = ifrId;
  ifrEle.width = 500;
  ifrEle.className = 'stopV '+clsad;
  ifrEle.height = 500;
  ifrEle.style.overflow = "hidden";
  ifrEle.style.scroll = "no";
  ifrEle.frameBorder = "0";
  ifrEle.allowFullscreen = true;
  ifrEle.allow = "autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share";

  return ifrEle;
}