/* Price Widget Styles */
.price-widget {
  /* border: 1px solid #dee2e6;
  border-radius: 8px; */
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  transition: all 0.3s ease;
  max-width: 540px;
  width: 320px;
  height: auto;
}

.price-widget-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.price-widget-header {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  color: #000000;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 20px;
  position: relative;
  z-index: 10;
}

.price-widget-content {
  background: #f0fdfa;
  padding: 12px 16px;
  position: relative;
  height: 95px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 8px 8px rgba(0, 0, 0, 0.08);
  overflow: visible;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  opacity: 1;
  transition: opacity 0.8s ease-in-out;
}

.loading-content.fade-out {
  opacity: 0;
}

.widget-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  opacity: 0;
  transform: scale(0.95);
  animation: fadeInWidget 0.6s ease-out forwards;
}

@keyframes fadeInWidget {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}



.price-widget-content.blurred {
  filter: blur(3px);
  pointer-events: none;
  position: relative;
}

/* Lowest Price Product Display */
.lowest-price-product {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 400px;
  box-sizing: border-box;
}

.product-price-container {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.current-price-label {
  font-size: 10px;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.main-product-price {
  text-align: center;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
}

.price-value-red {
  font-size: 14px;
  font-weight: 700;
  color: #dc3545;
}

.price-value-blue {
  font-size: 14px;
  font-weight: 700;
  color: #007bff;
}



.product-details {
  /* flex: 1; */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
}

.product-name {
  font-size: 10px;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  /* margin-bottom: 4px; */
}



.product-price {
  text-align: center;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
}

.product-price .price-value {
  font-size: 14px;
  font-weight: 700;
  color: #1b5e20;
  white-space: nowrap;
}

.widget-price-unit {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
}
.widget-price-unit-header {
  font-size: 16px;
  font-weight: 500;
  color: #6c757d;
}

.price-comparison {
  font-size: 14px;
  font-weight: 600;
  color: #388e3c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.percentage-less {
  background: #e8f5e9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
}

.down-arrow {
  font-size: 10px;
  color: #388e3c;
}

/* Price Comparison Bar */
.price-comparison-bar {
  margin-top: 25px;
  width: 100%;
  max-width: 100%;
  overflow: visible;
  position: relative;
  /* padding: 12px 16px; */
}

.price-bar-container {
  position: relative;
  height: 8px;
  width: 350px;
  max-width: calc(100% - 20px);
  background: linear-gradient(to right, rgb(0, 106, 25) 0%, #007bff 50%, #c70014 100%);
  border-radius: 4px;
  margin: 8px auto;
  overflow: visible;
}

/* Price Marker Dot */
.price-marker-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  position: absolute;
  top: -3px;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.price-marker-dot:hover {
  transform: translateX(-50%) scale(1.2);
}

.current-product-dot {
  border-color: #fff;
}

/* Tooltip styles */
.price-tooltip {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  padding: 1px 3px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.2s ease, padding 0.2s ease, font-size 0.2s ease;
  z-index: 10;
  pointer-events: none;
  max-width: 280px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  overflow: hidden;
}

.price-tooltip.always-visible {
  opacity: 1;
  visibility: visible;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-width: 6px;
  border-top-style: solid;
  border-top-color: inherit;
  width: 0;
  height: 0;
  pointer-events: none;
}

.price-marker-dot:hover .price-tooltip:not(.always-visible) {
  opacity: 1;
  visibility: visible;
}

.price-marker-dot:hover .price-tooltip {
  transform: translateX(-50%) scale(1.1);
  padding: 3px 6px;
  font-size: 11px;
}

/* Edge-aware tooltip alignment to keep text inside widget */
.price-tooltip.align-left {
  left: 0;
  right: auto;
  transform: none;
  text-align: left;
  max-width: calc(100% - 10px);
}

.price-tooltip.align-right {
  left: auto;
  right: 0;
  transform: none;
  text-align: right;
  max-width: calc(100% - 10px);
}

.price-tooltip.align-center {
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  max-width: 280px;
}

/* Ensure tooltip content fits within widget boundaries */
.price-bar-container .price-tooltip {
  max-width: calc(100% - 20px);
  min-width: 120px;
}

.lowest-product-dot {
  background: rgb(0, 106, 25);
  border-color: #fff;
}

.highest-product-dot {
  background: #c70014;
  border-color: #fff;
}

.price-marker-label {
  font-weight: 600;
  margin-top: 8px;
  text-align: center;
  line-height: 1.2;
  font-size: 12px;
}

.current-product-label {
  color: #007bff;
}

.lowest-product-label {
  color: #28a745;
}

.highest-product-label {
  color: #dc3545;
}



/* Negotiation Tip */
.negotiation-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #c3e6c3;
  border-radius: 6px;
  font-size: 11px;
  color: #2d5a2d;
}

.tip-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tip-text {
  font-weight: 500;
  line-height: 1.3;
}



.market-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-stable {
  color: #28a745;
}

.trend-rising {
  color: #dc3545;
}

.trend-falling {
  color: #ffc107;
}

.confidence-score {
  font-weight: 500;
}

/* Login Prompt Styles - No overlay needed, direct content */

.login-prompt {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  gap: 8px;
  justify-content: center;
}

.locked-icon {
  font-size: 16px;
  color: #ff6b35;
  background: #ffedd5;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
svg:not(:host).svg-inline--fa, svg:not(:root).svg-inline--fa {
    overflow: visible;
    box-sizing: content-box;
}
.svg-inline--fa {
    display: var(--fa-display, inline-block);
    height: 1em;
    overflow: visible;
    vertical-align: -.125em;
}

.prompt-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.main-message {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  margin: 0;
}

.disclaimer {
  font-size: 12px;
  color: #4b5563;
  line-height: 1.2;
  font-weight: 400;
  font-style: italic;
  margin: 0;
}

/* Loading State with Shining Text */
.price-widget-loading {
  background: #f0fdfa;
  padding: 12px 16px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  z-index: 2;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  will-change: opacity;
  opacity: 1;
}

.price-widget-loading.fade-out {
  animation: fadeOutLoading 0.5s ease-in-out forwards;
}

@keyframes fadeOutLoading {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(0) scale(1);
    visibility: hidden;
  }
}

.loading-lock {
  width: 32px;
  height: 32px;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
}

.loading-lock::before {
  content: '🔒';
  font-size: 16px;
}

@keyframes textShine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.loading-text {
  font-size: 14px;
  margin: 0;
  font-weight: 600;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  white-space: nowrap;
  background: linear-gradient(
    90deg,
    #6c757d 0%,
    #ffffff 50%,
    #6c757d 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShine 2s ease-in-out infinite;
}

/* Error State */
.price-widget-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #dc3545;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .price-widget {
    margin-bottom: 10px;
    max-width: 400px;
    width: 300px;
  }

  .price-widget-header {
    padding: 10px 14px;
    font-size: 17px;
  }

  .price-widget-container {
    height: 100px;
  }

  .price-widget-content {
    height: 90px;
  }

  .price-widget-loading {
    padding: 10px 14px;
    height: 100px;
  }

  .price-bar-container {
    width: 300px;
    max-width: calc(100% - 10px);
  }

  .price-tooltip {
    font-size: 12px;
    max-width: 250px;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
    line-height: 1.2;
  }

  .price-markers {
    width: 300px;
  }

  .current-price-label {
    font-size: 8px;
  }

  .price-value-red {
    font-size: 16px;
  }

  .product-name {
    font-size: 8px;
  }

  .product-price .price-value {
    font-size: 16px;
  }

  .widget-price-unit {
    font-size: 12px;
  }

  .locked-icon {
    font-size: 14px;
    width: 32px;
    height: 32px;
  }



  .negotiation-tip {
    margin-top: 8px;
    padding: 6px 10px;
    font-size: 10px;
    gap: 6px;
  }

  .tip-icon {
    font-size: 12px;
  }

  .main-message {
    font-size: 18px;
  }

  .disclaimer {
    font-size: 11px;
  }

  .login-overlay {
    padding: 8px;
  }
}

/* Integration with existing form styles */
.ber-frmpop .price-widget {
  margin: 0 0 16px 0;
}

.ber-Rsc .price-widget {
  width: 100%;
  box-sizing: border-box;
}
