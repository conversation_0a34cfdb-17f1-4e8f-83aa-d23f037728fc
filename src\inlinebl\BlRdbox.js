import React from "react";
function Blrdbox({setWarning,warning,setRequirementDetails,requirementDetails}){
    const handleRequirementDetailsChange = (e) => {
        setRequirementDetails(e.target.value);
    };


return(
    <>
        <div id="t0102_reqbox"  className="idsf pfstrt mb20 inTxAr"><label className="fs15 cl11" id="t0102_textarea">Briefly describe your requirement</label><div id="" className="pflx1" data-role="" ><div id="" className="pr" data-role="" >
            <textarea id="t0102_reqBoxTemplates" 
             
            className={` ${warning ? 'highErr' : ''}`}
            placeholder="Additional details about your requirement..."
            value={requirementDetails}
            onChange={handleRequirementDetailsChange}
            onClick={()=>{setWarning('')}}>
            </textarea>
            {warning && <div className="errorRD">{warning}</div>}
            </div></div></div>
    </>
)
}
export default Blrdbox;