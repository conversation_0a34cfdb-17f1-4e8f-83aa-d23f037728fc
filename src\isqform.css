.ber-dvtxt {
    color:#111;
    display: inline-block;
    position: relative;
    font-weight: 400;
    line-height: 15px;
    margin-bottom: 0;
    text-align: left;
    font-size: 15px;
}

.chkBox{
    background: #fff;
    height: 34px;
    line-height: 34px;
    border: 1px solid #c1c1c1;
    width: auto;
}
.chkBox{
    display: inline-block;
    box-sizing: border-box;
    position: relative;
    vertical-align: top;
    margin: 8px 13px 0px 0;
}
.chkBox {
    border-radius: 20px !important;
}
.grb {
    appearance: none; /* Remove default arrow */
    -webkit-appearance: none; /* Remove default arrow for Safari */
    -moz-appearance: none; /* Remove default arrow for Firefox */
    outline: none;
    line-height: normal;
    background: url("data:image/gif;base64,R0lGODlhDwAUAIABAH9/f////yH5BAEAAAEALAAAAAAPABQAAAIXjI+py+0Po5wH2HsXzmw//lHiSJZmUAAAOw==") 99% 50% no-repeat #fff !important;
    box-shadow: none;
}
.chkBox.eqchkbx ,.ber-input {
    border-radius: 7px !important;
}.bebr4 {
    border-radius: 4px !important;
}
.eqVam {
    vertical-align: middle;
}

.chkBox input[type="radio"],.chkBox input[type="checkbox"]{
    display: none;
}
.bepr {
    position: relative;
}
.chkBox label{
    display: block;
}
.bechkin {
    position: absolute;
    left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 10px;
    border: 1px solid #a0a0a0;
    top: 8px;
}

.bechkin {
    background: #fff;
}

.beradio-sl {
    width: 8px;
    height: 8px;
    background: #2f3292;
    border-radius: 50%;
    margin: 0 auto;
    margin-top: 3px;
}
.dispNone {
    display: none!important;
}
.beisq3{
    font-size: 15px;
}
.ber-frmpop .beisq3{
    line-height: 33px;
    width: auto;
    color: #333;
    padding: 0px 10px 0px 30px !important;
}
.chkBox:hover,.ber-input:focus{
    border-color: #029f93;
}
.chkBox.selRd{
    border-color: #029f93;
}
.chkBox:hover .bechkin{
    border-color: #029f93;
}
.chkBox:hover .beisq3 {
    color: #029f93;
}
.chkBox.selRd .beisq3{
    color: #029f93!important;
}
.checkSl {
    background-color: #fff !important;
    border-color: #2e3192;
}
.checkSl .bechkin{
    border-color: #2e3192;
    background-color: #fff;
}
.checkSl span {
    color: #2e3192 !important;
}
.checkSl .beradio-sl {
    width: 8px;
    height: 8px;
    margin: 0 auto;
    margin-top: 3px;
    transform: unset;
    border: none;
    background-color: #2e3192;
    border-radius: 20px;
}
/*rd box css */

.bemt15 {
    margin-top: 15px;
}

.oEq_r .ber-txtarea {
    height: 70px;
    width: 80%;
    padding: 5px 9px;
}
.bemlsecR {
    background: #fff;
    margin: 25px 0 10px 0;
}
.oEq_r .eqflot {
    position: relative;
    margin-bottom: 30px;
    margin-top: 10px;
}
/* .oEq_r .eqflot label{
    pointer-events: none;
    transform: translate3d(5px, 9px, 0) scale(1);
    transform-origin: left top;
    transition: 100ms;
    font-size: 12px;
    color: #888;
    z-index: 1;
    position: absolute;
    padding: 5px 10px;
} */

.oEq_r .enqLogIn .ber-input:focus{
    border-color: #029f93!important;
}

.oEq_r .eqflot.eqfcsed label, .e_whm .eqflot.eqfcsed label {
    transform: translate(5px, -12px);
    color: #2e3192;
    background-color: #fff;
}
.beW5 {
    width: 331px;
}

.oEq_r .eqflot .ber-slbox{
    height: 42px;
    border-radius: 3px !important;
    border: solid 1px #d0cdcd;
    padding: 0px 0 0 8px;
    margin: 0px;
    font-size: 15px;
    background-color: #fff;
    color: #000;
}
.oEq_r .eqflot .ber-slbox::placeholder {
    color: #888;
    font-size: 12px;
  }


.beticked{
    display: none;
}
.checkSl .beticked{
    display: block!important;
}

.beticked {
    width: 6px;
    height: 10px;
    background: none;
    margin: 0 auto;
    margin-top: 1px;
    border-right: 3px solid #2f3292;
    border-bottom: 3px solid #2f3292;
    transform: rotate(40deg);
}

.chkBox .beticked {
    width: 6px;
    height: 10px;
    background: none;
    margin: 0 auto;
    margin-top: 1px;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(40deg);
}

.oEq_r .eqchkbx.checkSl .bechkin, .oEq_r .eqchkbx.checkSl:hover .bechkin {
    border-color: #2e3192;
    background-color: #2e3192;
}
.oEq_r .txtblng {
    width: 70%;
}
input:focus-visible {
    outline: none; /* or specify your desired focus style */
    /* Additional styles as needed */
  }

  .row-be {
    margin-top: 15px;
    margin-bottom: 10px;
}
.ber-w50.lgcont {
    display: block;
}

.lbtool .tllb, .lbtool label {
    display: inline-block !important;
    float: none !important;
}
.bewfull {
    width: 100%;
}
.oEq_r .row-be {
    margin-bottom: 20px;
}
.oEq_r .chkBox.oth_bx ,.ber-radiobox.oth_bx {
    border-radius: 7px !important;
}
.other_check{
    border-radius: 20px !important;}
 .chkBox .isqoth,.ber-radiobox .isqoth {
        width: 106px;
        text-align: left;
        line-height: 34px !important;
        height: 34px !important;
        padding: 0px 10px !important;
        border: 0px !important;
        margin-top: 0px !important;
        box-shadow: none;
        background: none;
}
.othersel{
    width: 100%;
    top: 23px;
    left: -1px;
    background: #fff !important;
    border-radius: 7px !important;
    margin-top: -34px !important;
}
  .radio-clk {
    background-color:  #2e3192;
  }
  

  .scrl_layout {
    overflow: hidden !important;
  }
  .scrl_layout body {
    overflow: scroll !important;
    height: 100%;
  }
