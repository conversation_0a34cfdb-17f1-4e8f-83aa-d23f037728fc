import { getparamValREC, readCookieREC } from "../../common/formCommfun";

export async function updateCityForms(cityy) {
  let imiss = readCookieREC("im_iss") || "";
  let decoded = "";

  try {
    decoded = decodeURIComponent(imiss);
  } catch (err) {
    console.error("Failed to decode im_iss cookie:", err);
    decoded = "";
  }

  let match = decoded ? decoded.match(/t=([^&]+)/) : null;

  const AK = match && match[1]?.trim() !== "" ? match[1] : "";

  const params = new URLSearchParams({ 
    CITY: cityy || '',
    USR_ID: getparamValREC(readCookieREC("ImeshVisitor"), "glid"),
    webAddLoc: window?.location?.hostname || "",
    modId: "FORMS",
    AK: AK || ''
  });

  try {
    const response = await fetch(`https://www.indiamart.com/api/ajax-services/profileCityUpdate/?${params.toString()}`, {

      method: "GET",
    });

    const data = await response.json();

    if (data?.code === "200") {
      console.log("City updated");
    }

    if (!response.ok) {
      console.error("Server responded with error:", data);
    }
  } catch (err) {
    console.error("Fetch failed:", err);
  }
}
