import React from 'react';
import { isset, getAddressREC, sessionValREC , reqFormGATrackREC, isInactBL,searchAllindia } from '../common/formCommfun';
import './imgEnq.css';

const CatsImgRecom = ({ form_param }) => {
  let brd_mcat_id = form_param.mcatId;
  let maxN = 4 
  let catsData = isset(() => sessionValREC("cats-" + brd_mcat_id)) ? sessionValREC("cats-" + brd_mcat_id) : [];

  if ((!isset(() => catsData) || catsData.length <= 1) || (searchAllindia(form_param))) {
    return null;
  }

  const text = "Find related categories";
  let pdpabex = isInactBL(form_param) ? "blform=2" : "enqformpdp=1";
  const teststr = form_param.section;

  if (/\|EUI1/.test(teststr)) pdpabex = "enqformpdp=2";
  if (/\|CUI/.test(teststr)) pdpabex = "enqformpdp=3";

  const param = pdpabex;

  return (
    <div id="recommendCatsImg" className="VSP-SECI">
      <div className="vs-heading">
        <h3>{text}</h3>
      </div>
      <div id="catsList" className={`ProBoxULIC ${isInactBL(form_param) ? 'ProBoxInactR' : ''}`}>
        {catsData.slice(0, maxN).map((item, index) => {
          item = isset(() => item[0]) ? item[0] : item;
          
          if (!isset(() => item) || !isset(() => item.catUrl) || !isset(() => item.catImg) || !isset(() => item.catName)) {
            return null;
          }

          item.catUrl = isset(() => item.catUrl) ? (item.catUrl.endsWith(".html") ? `${item.catUrl}?${param}` : `${item.catUrl}&${param}`) : "''";
          item.catUrl = isset(() => item.catUrl) ? (item.catUrl.startsWith('/') ? `https://dir.indiamart.com${item.catUrl}` : item.catUrl) : "''";
          item.catCity = isset(() => item) && isset(() => item.catCity) && item.catCity!== "" ? `in ${item.catCity}` : '';

          return (
            <span key={index}>
              <a target="_blank" href={item.catUrl} className="ProBox-Item img-mcats" id={`recomCat${index + 1}`} onClick={() => {reqFormGATrackREC(`Cat_${index + 1}`,form_param)}}>
                <div className="ProimgC scrlcats">
                  <img src={item.catImg} alt={item.catName} />
                </div>
                <div className="mcard-txt">
                  <div className="pr">
                    <h4 className="mcard-hdg">{item.catName}</h4>
                  </div>
                  <div style={{ paddingTop: '5px' }}>
                    <p className="mcard-cty">{item.catCity}</p>
                    <div style={{ display: 'inline-flex' }} className="cbl_aic mt2 txtl">
                      <p className="disp-inl befs13 hvtxu">Get Quote</p>
                      <div className="mcard-gqarw">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#2e3192">
                          <path d="M13.1714 12.0007L8.22168 7.05093L9.63589 5.63672L15.9999 12.0007L9.63589 18.3646L8.22168 16.9504L13.1714 12.0007Z"></path>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </a>
            </span>
          );
        })}
      </div>
    </div>
  );
};

export default CatsImgRecom;
