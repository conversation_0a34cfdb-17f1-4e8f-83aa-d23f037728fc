
export default async function MultiImgAPI(formpar) {

    const  itemId = formpar.itemid;
    const modid = formpar.modId;
    const glusrId = formpar.rcvGlid;
    
    const webAddressLocation = location.hostname;
    var serverName = webAddressLocation.match(/^dev/)
    ? "//dev-apps.imimg.com/"
    : webAddressLocation.match(/^stg/)
      ? "//stg-apps.imimg.com/"
      : "//apps.imimg.com/";    
    var url = serverName + `index.php?r=MultiImage/MultipleImageData&token=imobile@15061981&modid=${modid}&itemid=${itemId}&glusrid=${glusrId}`;
    // console.log(url);
   
        
    try {
        const response = await fetch(url, {
          method: 'GET',
          mode: 'cors', 
          cache: 'no-store'
        });
  
      if (!response.ok) {
        throw new Error('Failed to call API');
      }
      const responseData = await response.json();
      return responseData;
    //   setApiResponse(responseData);
    } catch (error) {
      console.error('Failed to call API:', error);
      imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'MulitImgAPI','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
  }
