import React, { useEffect, memo } from 'react';

const ImageformAd = () => {
  useEffect(() => {
    if (!window.googletag) {
      console.log('GPT library not loaded');
      return;
    }

    let slot;
    try {
      window.googletag.cmd.push(() => {
        slot = window.googletag.defineSlot(
          '/3047175/Desktop_Forms_Next_pre', 
          [[300, 120], [250, 250], [320, 150], [336, 280], [468, 60], [400, 300], [300, 200], [300, 100], [320, 250], [320, 120], [320, 100], [300, 150], [300, 250], [320, 200], [250, 360]], 
          'div-gpt-ad-1737527928798-0'
        );
        if (slot) {
          slot.addService(window.googletag.pubads());
          window.googletag.pubads().refresh([slot]);
        }
      });
    } catch (error) {
      console.log('Ad initialization error:', error);
    }

    return () => {
      if (window.googletag && slot) {
        window.googletag.cmd.push(() => {
          window.googletag.destroySlots([slot]);
        });
      }
    };
  }, []);

  return (
    <div style={{
      minWidth: '468px', 
      minHeight: '250px', 
      position: 'absolute',
      transform: 'translateY(-50%)',
      top: '50%',
      left: '55px',
      width: 'calc(100% - 570px)',
      display: 'flex',
      justifyContent: 'center',
      zIndex: 3
    }}>
      <div id="div-gpt-ad-1737527928798-0"></div>
    </div>
  );
};

export default memo(ImageformAd);