import React, { useState, useEffect, useRef } from 'react';
import { scriptTag, isset, isPDfForm, safeDecodeURIComponent } from '../common/formCommfun';
import './QuantityBox.css';

const QuantitySelector = ({ resdata, saveQt, setCansubmit, onInline, responseData, form_param ,setWarning , warning}) => {
    let qtOnInline = isset(() => onInline) && onInline == 1;
    const inputRef = useRef(null);
    function isqutArr(IsqArray) {
        var isqnewarr = [];
        for (var i = 0; i < IsqArray.length; i++) {
            if (IsqArray[i].length === 2) {
                var readarr = IsqArray[i][1].IM_SPEC_OPTIONS_DESC !== undefined ? IsqArray[i][1].IM_SPEC_OPTIONS_DESC : "";
                if (readarr !== "") {
                    isqnewarr = readarr.split("##");
                    if (qtOnInline) {
                        isqnewarr = isqnewarr.slice(0, 4)
                    }
                }
            }
        }
        var filteredArray = [];
        for (var j = 0; j < isqnewarr.length; j++) {
            var element = isqnewarr[j];
            if (!/other|others|other\(s\)/i.test(element)) {
                filteredArray.push(element);
            }
        }
        return filteredArray;
    }
    useEffect(() => {
        // Focus the input field when the component renders
        if (inputRef.current) {
            inputRef.current.focus();
        }
    }, [form_param]);

    const [unitValues, setUnitValues] = useState(isqutArr(resdata));
    const [selectedUnit, setSelectedUnit] = useState(unitValues[0]); // Default selected unit
    // const [otherUnit, setOtherUnit] = useState(''); // Default selected unit
    const [inputValue, setInputValue] = useState(''); // State to hold the input value
    // const [warning, setWarning] = useState(''); // State to manage warning message
    const [utwarning, setUtWarning] = useState('');


    let masterId = '';
    let unitId = '';
    let opidq = '';
    let unitFlag = '0';

    if (isset(() => resdata[0][0]) && isset(() => resdata[0][1])) {
        masterId = resdata[0][0].IM_SPEC_MASTER_ID;
        unitId = resdata[0][1].IM_SPEC_MASTER_ID; // Assuming masterId is same for all options
        opidq = resdata[0][0].IM_SPEC_OPTIONS_ID;
        unitFlag = resdata[0][1].GLCAT_MCAT_NO_UNIT ? resdata[0][1].GLCAT_MCAT_NO_UNIT : '0';
    }
    useEffect(() => {
        const newUnitValues = isqutArr(resdata);
        setUnitValues(newUnitValues);
        setSelectedUnit(newUnitValues[0]); // Reset to the first unit when resdata changes
    }, [responseData]);

    useEffect(() => {
        setWarning("");
        setUtWarning("");
        if (form_param) {
            if (form_param.ctaName && form_param.ctaName.includes("Next")) {
                setInputValue('');
            }
            if (form_param.plsqArr) {
                const params = form_param.plsqArr.split('#');
                params.forEach(param => {
                    const [encodedKey, encodedValue] = param.split(':');
                    const key = safeDecodeURIComponent(encodedKey);
                    const value = safeDecodeURIComponent(encodedValue);

                    if (key === "Quantity") {
                        setInputValue(value);
                    } else if (key === "Quantity Unit" && unitValues.some(unit => unit.toLowerCase() === value.toLowerCase())) {
                        const normalizedUnitValues = unitValues.map(unit => unit.toLowerCase());
                        const optionIndex = normalizedUnitValues.indexOf(value.toLowerCase());
                        setSelectedUnit(unitValues[optionIndex]);
                    }
                });
            }
        }
    }, [form_param]);



    useEffect(() => {
        if (warning || utwarning) {
            setCansubmit(0);
        }
        else {
            setCansubmit(1);
        }
    }, [warning, utwarning]);


    const handleInputChange = (event) => {
        const { value } = event.target;
        setInputValue(value);

        const isValid = /^[0-9]*$/.test(value); // Allows empty string
        const allZeros = /^0+$/.test(value); // Checks if the input consists only of zeros
        setWarning(allZeros ? "Quantity should not be '0'" : (!isValid ? 'Please enter only numeric characters.' : ""));
        value == "" && selectedUnit == "" ? setUtWarning('') : (value != "" && selectedUnit == "" ? setUtWarning('Please enter Quantity Unit') : '');
    };

    const handleUnitChange = (unit) => {
        setSelectedUnit(unit);
        setUtWarning('');
        // setOtherUnit('');
    };

    useEffect(() => {
        const unitOptionId = isset(() => resdata[0][1]) && isset(() => resdata[0][1].IM_SPEC_OPTIONS_ID) ? resdata[0][1].IM_SPEC_OPTIONS_ID.split("##")[unitValues.indexOf(selectedUnit)] : '';
        saveQt(inputValue, selectedUnit, masterId, opidq, unitId, unitOptionId);
    }, [inputValue, selectedUnit]);


    // const handleInputCheck = (event) => {
    //     const { value } = event.target;
    //     setSelectedUnit(value);
    //     setOtherUnit(value);
    //     const isValid = scriptTag(value);
    //     setUtWarning((isValid ? 'Enter a valid Quantity Unit.' : (inputValue != "" && value == "" ? "Please enter Quantity Unit" : "")));
    // };

    // const otherselected = (event) => {
    //     setSelectedUnit('');
    // };

    let inlCss = '';
    let inlUt = ''
    if (qtOnInline) {
        inlCss = 'inlQT'
        inlUt = 'inlUnit'
    }


    return (
        <>
            {
                <div className={`${inlCss}`}>
                    {/* Display warning message */}
                    {/* {!qtOnInline ? warning ? <div style={{ color: 'red' }}>{warning}</div> : utwarning ? <div style={{ color: 'red' }}>{utwarning}</div> : <div style={{ color: 'red' }} className='dispNone'>{utwarning}</div> : <div style={{ color: 'red' }} className='dispNone'>{utwarning}</div>} */}
                    <label id="qtlabel" className="qt_lbl">Quantity</label>
                    <div>
                    {(warning || utwarning) && <div className="quantiyerr">{warning || utwarning}</div>}
                        <div id="tqut_id">
                            {/* Input field for quantity */}
                            <input
                                type="text"
                                id="ttxtbx_option1"
                                className={`${unitFlag=='1' ? 'br77' : ''}`}
                                name="ttext_name1"
                                maxLength="12"
                                autoComplete="off"
                                value={inputValue}
                                onChange={handleInputChange}
                                ref={inputRef}
                            />

                            {form_param.ctaType == "Image" || form_param.ctaType == "Video"  || isPDfForm(form_param) ?
                                <select
                                    id="ttxtbx_option2"
                                    className={`inlUt ${unitFlag=='1' ? 'dispNone' : ''}`}
                                    value={selectedUnit}
                                    onChange={(e) => handleUnitChange(e.target.value)}
                                >
                                    {unitValues.map((unit, index) => (
                                        <option key={`unit_${index}`} value={unit}>
                                            {unit}
                                        </option>
                                    ))}
                                </select>
                                :
                                <>
                                    <ul id="tqt_display" className={`${unitFlag=='1' ? 'dispNone' : ''}`}>
                                        {unitValues.map((unit, index) => (
                                            selectedUnit !== unit ?
                                                <li key={`tqunit${index}`} className="radio-item" onClick={() => handleUnitChange(unit)}>
                                                    {unit}
                                                </li> : <li key={`tqunit${index}`} className="radio-item selUnit" onClick={() => handleUnitChange(unit)}>
                                                    {unit}
                                                </li>
                                        ))}
                                        {/* <input type="text" id="unitOther" name="unitOther" value={otherUnit} maxlength="1000" autocomplete="off" placeholder="Others" className={`${otherUnit ? 'selOther' : ''}`} onChange={handleInputCheck} onClick={otherselected}></input> */}
                                    </ul>
                                </>}
                        </div>
                    </div>
                </div>
            }
        </>
    );
};
export default QuantitySelector;
