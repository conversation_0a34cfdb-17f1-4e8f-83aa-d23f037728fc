import React, { useEffect } from 'react';
import { readCookieREC, getparamValREC, isset, Eventtracking, currentISO } from '../common/formCommfun';
import GlusrUpdateNEC from '../ContactDetails/GlusrUpdateNEC';
import { useGlobalState } from '../context/store';
import { validateEmail } from '../common/formCommfun';
import BlEnqUpdate from '../BlEnqUpdate';
import callMiniDetAPI from '../MinidtlsAPI';
import IntGenApi from "../main/IntGenApi";
import callPincodeAPI from '../callPincodeAPI';
import { updateCityForms } from '../components/LocalSellerCard/updateCityForms';

const SubmitButton = ({ userDetails, name, city, ctid, email, fn, em, mb, iso, mobile, form_param, warning, onSubmitcheck, MoreReq, gst, cname, wUrl ,blsearchval,seterr_msg,isCityFromSuggester,setValidCity,whichcity,isCityUpdate,setIsCityUpdate}) => {
    const { state, dispatch } = useGlobalState();
    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const s_country_iso = getparamValREC(imesh, 'iso') || currentISO();
    const glid = getparamValREC(imesh, 'glid');
    const s_ip = getparamValREC(iploc, 'gip');
    const s_ip_country = getparamValREC(iploc, 'gcnnm');
    const s_ip_country_iso = getparamValREC(iploc, 'gcniso');
    let glusrsend = false;
    if((iso == 'IN' && (name || city || email)) || (iso != 'IN' && (mobile)) || (MoreReq == true && (cname || gst || wUrl))) {
        glusrsend = true;
    }
    name = name == '' ? fn : name;
    mb = mb == '' ? mobile : mb;
    city = city || userDetails.cityname;
    const asked = document.getElementById("fromtochk");
    const handleSubmit = async (e) => {
        dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
                e.preventDefault();
        
        if(form_param.formType == "BL" && blsearchval == "" && !state.openinlnBLPopup){
            seterr_msg("Please enter your requirement");
            return false;
        }
        if (iso != 'IN') {
            if (warning !== '') {
                return;
            }
        } else {

            let pincodeCt = false; 

            if (/^\d{6}$/.test(city) && name) {
                    const result = await callPincodeAPI(form_param,city);
                    if (result && result !== 'nocity') {
                     pincodeCt=true;
                     city=result.city_name
                     ctid=result.city_id
                    } 
            }
           
            if ((name == '' || name.trim().length==0) && ((city == ''  || city.trim().length==0) && (ctid == ''  || ctid.trim().length==0))) {
                onSubmitcheck({ namecheck: false, citycheck: false, emailcheck: true });
                                return;
             } else  if ((name == '' || name.trim().length==0) && (!isCityFromSuggester && !pincodeCt)) {
                onSubmitcheck({ namecheck: false, citycheck: false, emailcheck: true });
                setValidCity(false);
                                return;
            } else if (name == '' || name.trim().length==0) {
                onSubmitcheck({ namecheck: false, citycheck: true, emailcheck: true });
                return;
            } else if ((city == ''  || city.trim().length==0) && (ctid == ''  || ctid.trim().length==0)) {
                onSubmitcheck({ namecheck: true, citycheck: false, emailcheck: true });
                return;
            } else if (email != '') {
                if (validateEmail(email) == false) {
                    onSubmitcheck({ namecheck: true, citycheck: true, emailcheck: false });
                    return;
                }
            }else if ( !asked && !isCityFromSuggester && !pincodeCt) {
                onSubmitcheck({ namecheck: true, citycheck: false, emailcheck: true });
                setValidCity(false);
                return;    
            }
            else if (asked && city && !isCityFromSuggester && !pincodeCt) {
                onSubmitcheck({ namecheck: true, citycheck: false, emailcheck: true });
                setValidCity(false);
                return;    
            }
        }
        email = email == '' ? em : email;
        try {
            const parmObj = {
                s_first_name: name && name != '1' ? name : '',
                s_glusrid: glid,
                curr_page_url: document.location.href,
                s_ip: s_ip,
                s_ip_country: s_ip_country,
                s_ip_country_iso: s_ip_country_iso,
                flag: "Enq",
                modid: form_param.modId,
                s_country_iso: s_country_iso,
            };
            if (userDetails.ctid != undefined && userDetails.ctid != '') {
                parmObj.s_city_id = userDetails.ctid;
            }
            if (mb != '' && mb != '1') {
                parmObj.s_mobile = mb;
            }
            if (getparamValREC(imesh, 'sessionKey')) {
                parmObj.SESSION_KEY = getparamValREC(imesh, 'sessionKey');
            }
            if (email != '' && email != '1') {
                parmObj.s_email = email;
            }
            if (iso == 'IN' && city) {
                parmObj.s_city_name = city;
            }
            if (MoreReq == true) {
                let app = '';
                let scrn = ''
                if (isset(() => cname) && cname != '') {
                    parmObj.s_companyName = cname;
                    scrn += 'CompanyName/';
                }

                if (iso == 'IN') {
                    if (isset(() => gst) && gst != '') {
                        parmObj.gst = gst;
                        scrn += 'GST/';
                    }
                    app = " Desktop Enquiry/BL Forms";
                }
                else {
                    if (isset(() => wUrl) && wUrl != '') {
                        parmObj.url = wUrl;
                        scrn += 'URL/';
                    }
                    app = " Desktop Enquiry/BL Forms Foreign"
                }
                if (scrn.endsWith('/')) {
                    scrn = scrn.slice(0, scrn.lastIndexOf('/'));
                }
                parmObj.scrnNm = scrn + app;
            }
            const visitorIso = getparamValREC(imesh, 'iso') || state.UserData.iso;
            const uv = getparamValREC(imesh, 'uv') || state.UserData.uv;
            const otp_con = (uv != 'V' && visitorIso == 'IN') ? true : false;
            if (glusrsend) {
                const responseData = await GlusrUpdateNEC(parmObj, form_param);
                if (responseData && responseData.DataCookie) {
                    dispatch({ type: 'UserData', payload: { UserData: responseData.DataCookie } });
                }
                if (city) {
                    callMiniDetAPI(form_param);
                }
            }
            dispatch({ type: "frscr", payload: { frscr: 2 } });
            dispatch({ type: 'userDetails', payload: { userDetails: userDetails } });
            dispatch({ type: 'NECcon', payload: { NECcon: false } });
            let eventcat = "";
            let emailcon = em == '' && email;
            if (!MoreReq && !(state.newEnq && state.RDNECcon)) {
                eventcat = "-NameCity";
                emailcon = emailcon && form_param.formType != 'Enq';
                dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack + "-NameCity" } });
                if(isCityUpdate?.to_city){
                    const res = { update: false, from_city: "", to_city: ""};
                    setIsCityUpdate(res);
                }
                if(isCityUpdate?.to_city || !otp_con){
                    state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + 2 } });
                }else{
                    state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + 1 } });
                }
                IntGenApi(form_param, "loginscreen");
            } else if (MoreReq) {
                eventcat = "-MoreDetails";
                if (cname) {
                    eventcat = eventcat + "|Company";
                }
                if (gst) {
                    eventcat = eventcat + "|GST";
                }
                if (wUrl) {
                    eventcat = eventcat + "|Website";
                }
                dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack + "-MoreDetails" } });
                state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
            } else {
                eventcat = "-UserDetails2";
                dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack + "-UserDetails2" } });
                state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
            }
            if (iso == 'IN') {
                if (name && fn == '') {
                    eventcat = eventcat + "|Name";
                }
                if (emailcon) {
                    eventcat = eventcat + "|Email";
                }
                if (city && ctid == '') {
                    eventcat = eventcat + "|City";
                    if(whichcity){
                        eventcat = eventcat + "-" + whichcity
                    }
                }

                
                if (asked ){
                   eventcat = eventcat + "|UC";
                   window.updatedVal = '|UC';
                   
                  if(city.trim().length!=0) {
                    eventcat = eventcat + "|F";
                     window.updatedVal += '|F';
                    if(city == isCityUpdate?.from_city){
                         eventcat = eventcat + "|Old";
                          window.updatedVal += '|Old';
                    }
                    else{
                         if(city == isCityUpdate?.to_city){
                            eventcat = eventcat + "|Sug";
                            window.updatedVal += '|Sug';
                        }else{
                            eventcat = eventcat + "|Oth";
                            window.updatedVal += '|Oth';
                        }
                         updateCityForms(city)
                         sessionStorage.removeItem(`ecv-${glid}`)
                         sessionStorage.removeItem(`minidtlsres`)
                    }             
                  }
                  else{
                    eventcat = eventcat + "|NF";
                    window.updatedVal += '|NF';
                  }

                }
            

            } else {
                if (mobile && !(getparamValREC(imesh, "mb1") || state.UserData.mb1)) {
                    eventcat = eventcat + "|MobileNumber";
                }
            }
            Eventtracking("SS" + window.screencount + eventcat, state.prevtrack, form_param, false);
            window.screencount++;
            if (state.newEnq == undefined || state.newEnq == false) {
                if (MoreReq == false) {
                    if (otp_con == true) {
                        dispatch({ type: 'OTPcon', payload: { OTPcon: true } });
                    }
                    else {
                        dispatch({ type: 'OTPcon', payload: { OTPcon: false } })
                        if (form_param.formType == "BL") {
                            dispatch({ type: 'IsqformBL', payload: { IsqformBL: true } });
                        } else {
                            dispatch({ type: 'Isqform', payload: { Isqform: true } })
                        }
                    }
                } else {
                    dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: false } });
                    BlEnqUpdate(form_param , state.postreq , window.rfq_queryDestinationRec ,state);
                    // if(state.localenqparam && state.postreqenqlocal){
                    //     BlEnqUpdate(state.localenqparam , state.postreqenqlocal , window.rfqDlocal ,state);
                    // }
                    dispatch({ type: 'thankyou', payload: { thankyou: true } });
                }
            } else {
                if (state.RDNECcon) {
                    dispatch({ type: 'RDNECcon', payload: { RDNECcon: false } });
                    dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: true } });
                } else {
                    if (otp_con == true) {
                        dispatch({ type: 'OTPcon', payload: { OTPcon: true } });
                    }
                    else {
                        dispatch({ type: 'OTPcon', payload: { OTPcon: false } })
                        if (form_param.formType == "BL") {
                            dispatch({ type: 'IsqformBL', payload: { IsqformBL: true } });
                        } else {
                            dispatch({ type: 'Isqform', payload: { Isqform: true } })
                        }
                    }
                }
            }

        } catch (error) {
            console.error('Error in glusrUpdate:', error);
            imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'Error in glusrUpdate', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
        }
    };
    var clsfrm = "";
    var clsinsfrm = "submit-button";
    if (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') {
        clsfrm = " form-group-img";
        clsinsfrm = "submit-button-img";
    }

    const handleKeyPress = (event) => {
        if (event.keyCode === 13) {
            handleSubmit(event);
        }
    };
    useEffect(() => {
        document.addEventListener('keydown', handleKeyPress);

        const el = document.querySelector("#t0901_cls.cityCrs.ber-cls-rec");
        const handleClick = (e) => {
          handleSubmit(e);
        };
        if (el) {
          el.addEventListener("click", handleClick);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyPress);
            if (el) {
             el.removeEventListener("click", handleClick);
            }
        };
    }, [userDetails, name, city, email, mobile, form_param, warning, onSubmitcheck,blsearchval]);
    useEffect(() => {
        window.userdata = state.UserData;
    }, [state.UserData]);
    return (
        <div className={`form-group${clsfrm}`}>
            <button type="submit" className={clsinsfrm} onClick={handleSubmit}>
                {asked ?  "Next" : "Submit"}
            </button>
        </div>
    );
};

export default SubmitButton;
