import React, { memo, useEffect, useRef, useState } from "react";
import Login_compt from "../Login/Login_compt";
import ISQDtl from "../ISQDtl";
import Left_sec from "./Left_sec";
import { useGlobalState } from "../context/store";
import Contactdtl from "../ContactDetails/ContactDtl";
import Req_detail from "../Req_detail";
import MoreReqDet from "../MoreReqDet";
import User_ver from "../OTP/User_ver";
import IntGenApi from "./IntGenApi";
import Thankyoumain from "../Thankyou/Thankyoumain";
import { 
  readCookieREC, getparamValREC, isset, stopBgScrollREC, 
  imeqglval, SaveISQonCross, currentISO, Eventtracking,
  isPresent, LoginNewUiPDP, shouldRenderPriceWidget, 
  updatePopupShow, lowSoldAd, medSoldAd 
} from "../common/formCommfun";
import callMiniDetAPI from "../MinidtlsAPI";
import ProgressBar from "../progressbar/ProgressBar";
import PriceWidget from "../components/PriceWidget/PriceWidget";
import SearchAdFor from "../common/SearchAdFor";
export function Form_enqMemo({form_param, id, close_fun, isCityUpdate, setIsCityUpdate}) {
  const { state, dispatch } = useGlobalState();
  const imesh = isset(() => readCookieREC("ImeshVisitor"))
        ? readCookieREC("ImeshVisitor")
        : "";
  const [fn, setFn] = useState(getparamValREC(imesh, "fn") || state.UserData.fn || "");
  const [em, setEm] = useState(getparamValREC(imesh, "em") || state.UserData.em || "");
  const [mb, setMb] = useState(getparamValREC(imesh, "mb1") || state.UserData.mb1 || "");
  const [iso, setIso] = useState(getparamValREC(imesh, "iso") || state.UserData.iso || currentISO());
  const [phext, setPhext] = useState(getparamValREC(imesh, "phcc") || state.UserData.phcc || "");
  const [uv, setUv] = useState(getparamValREC(imesh, "uv") || state.UserData.uv || "");
  const [mdresct, setMdresct] = useState(getparamValREC(imesh, "ctid")|| state.UserData.ctid || "");
  const [showThankyou, setShowThankyou] = useState(false);
  const [mindet, setmindet] = useState({});
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedOptscr, setSelectedOptscr] = useState([]); 
  const [prgsabtest, setPrgsabtest] = useState(false);
  const progressUpdated = useRef(false);
  window.isBLFormOpen = true;
  stopBgScrollREC();
  window.otpcountRes = 0;
  window.otpcountReser = 0;

  // Initial setup effect
  useEffect(() => {
    const ismshexist = readCookieREC("ImeshVisitor");
    if (ismshexist == null) {
      dispatch({ type: "Imeshform", payload: { Imeshform: false } });
    } else {
      dispatch({ type: "Imeshform", payload: { Imeshform: true } });
    }
    dispatch({ type: "Isqform", payload: { Isqform: false } });
    dispatch({ type: "RDform", payload: { RDform: false } });
    dispatch({ type: "MrEnrForm", payload: { MrEnrForm: false } });
    dispatch({ type: "thankyou", payload: { thankyou: false } });
  }, [form_param]);

  useEffect(() => {
    const settingUserData = async () => {
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      const visitorFn = getparamValREC(imesh, "fn") || state.UserData.fn;
      const visitorEm = getparamValREC(imesh, "em") || state.UserData.em;
      const visitorMb = getparamValREC(imesh, "mb1") || state.UserData.mb1;
      const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso || currentISO() || "IN";
      const phext = getparamValREC(imesh, "phcc") || state.UserData.phcc;
      const uv = getparamValREC(imesh, "uv") || state.UserData.uv;

      setFn(visitorFn);
      setEm(visitorEm);
      setMb(visitorMb);
      setIso(visitorIso);
      setPhext(phext);
      setUv(uv);
      // Fetch mini details
      let mdtlres = null;
      let city = "";
      try {
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
      } catch(e) {
        mdtlres = null;
      }
      
      if(mdtlres && mdtlres[getparamValREC(imesh, "glid")]) {
        city = isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[0]) 
          ? mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] : "";
        dispatch({ type: 'md_resp', payload: { md_resp: mdtlres} });
        setmindet(mdtlres[getparamValREC(imesh, "glid")]);
      } else {
        if(getparamValREC(imesh, "glid") != "") {
          const data = await callMiniDetAPI(form_param);
          city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0]) 
            ? data.Response.Data[0] : "";
          if (isset(() => data) && data && data.Response && data.Response.Data) { 
            setmindet(data);
            dispatch({ type: 'md_resp', payload: { md_resp: data.md_resp } });
          } else {
            setmindet("No Response From Service");
          }
        }
      }
      
      // Set user data
      city = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") 
        : state.UserData.ctid ? state.UserData.ctid : city;
      setMdresct(city ? "1" : "");
    };
    settingUserData();
  }, [state.Imeshform, state.UserData]);

  // MAIN CONSOLIDATED EFFECT - Handles strict execution order: NEC → OTP → ISQ
  useEffect(() => {
    const handleFormFlow = async () => {
      let progressstep = 0;
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      progressstep = imesh != "" ? 1 : 0;
      
      // Set user data
      city = mdresct ? "1" : "";

      // STRICT EXECUTION ORDER LOGIC
      // Step 1: Check if NEC is needed
      const nec_condition = iso == "IN" && ((fn == "" || city == "") || (isCityUpdate?.update && updatePopupShow(form_param)));
      if (nec_condition) {
        // NEC is needed - stop here
        dispatch({ type: "NECcon", payload: { NECcon: true } });
        dispatch({ type: "OTPcon", payload: { OTPcon: false } });
        dispatch({ type: "Isqform", payload: { Isqform: false } });
      } else {
        // NEC not needed, move to Step 2: Check if OTP is needed
        dispatch({ type: "NECcon", payload: { NECcon: false } });
        
        const otp_condition = uv != "V" && iso == "IN";
        
        if (otp_condition) {
          // OTP is needed - stop here
          dispatch({ type: "OTPcon", payload: { OTPcon: true } });
          dispatch({ type: "Isqform", payload: { Isqform: false } });
          progressstep = progressstep + 1;
        } else {
          // OTP not needed, move to Step 3: ISQ
          dispatch({ type: "OTPcon", payload: { OTPcon: false } });
          dispatch({ type: "Isqform", payload: { Isqform: true } });
          if(iso == "IN"){
            progressstep = progressstep + 2;
          }else{
            progressstep = progressstep + 1;
          }
        }
      }

      // Handle form type specific logic
      if(form_param.formType == "Enq") {
        dispatch({ type: "newEnq", payload: { newEnq: true } });
        dispatch({ type: "progBar", payload: { progBar: true } });
        if(imesh == '') {
          const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
          const iploc_iso = getparamValREC(iploc, 'gcniso') !== "" ? getparamValREC(iploc, 'gcniso') : "IN";
          dispatch({ type: "progressCnt", payload: { progressCnt: currentISO() || iploc_iso } });
        }
      }
      if(!progressUpdated.current && progressstep){
      dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + progressstep } });
      progressUpdated.current=true;
      }
    };
    
    // Execute the flow when dependencies change
    handleFormFlow();
    setPrgsabtest(LoginNewUiPDP(form_param) && !state.Imeshform ? false : true);
    
  }, [form_param,isCityUpdate]);

  // Reset effect
  useEffect(() => {
    dispatch({ type: "postreq", payload: { postreq: 0 } });
    const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
    id == "0901" && imesh != "" ? IntGenApi(form_param) : "";
  }, [form_param]);

  // Keyboard event handler
  const handleKeyPress = (event) => {
      if (event.keyCode === 27) {
          handleClose('EscapeKeyPressed'); 
      }
  };

 
  useEffect(() => {
      document.addEventListener('keydown', handleKeyPress);
      return () => {
          document.removeEventListener('keydown', handleKeyPress);
      };
  }, [form_param]);

  const handleClose = (source = '') => {
    const el = document.querySelector("#t0901_cls.cityCrs.ber-cls-rec");
     if (el && source == "CrossButtonClicked") {
      return;
     }
    
    Eventtracking("CS" + window.screencount + "|" + state.currentscreen + "|" + source, state.prevtrack, form_param, false);
    const imeqarr = imeqglval();
    if(!imeqarr.Enq && form_param.formType != 'BL'){
      window.showskipBut=true;
    }
    else{
      window.showskipBut=false;
  }
    if (!showThankyou && (state.postreq != undefined && state.postreq!=0) && !state.thankyou) {
      SaveISQonCross(form_param, selectedOptions, state.postreq)
      setShowThankyou(true);
    } else {
      close_fun();
    }
    state.progBar && dispatch({ type: 'progressstep', payload: { progressstep: 1} });
    progressUpdated.current=false;
    dispatch({ type: 'thankyou', payload: { thankyou: false } });
    dispatch({ type: "NECcon", payload: { NECcon: false } });
    dispatch({ type: "OTPcon", payload: { OTPcon: false } });
    dispatch({ type: "Isqform", payload: { Isqform: false } });
    dispatch({ type: "RDNECcon", payload: { RDNECcon: false } });
  };
  useEffect(() => {
    if (window.closeform) {
      
      handleClose('ForSoftwareRedirect');
      window.closeform = false;
    }
  }, [window.closeform]);

  return (
    <React.Fragment>
      <div className="ber-frwrap" id={`t${id}_bewrapper`} style={{zIndex:"999"}}>
        <div
          className="blckbg"
          id={`t${id}_blkwrap`}
          onClick={() => handleClose("OutsideClicked")}
        ></div>
        <div className="frmcont">
          {state.thankyou || showThankyou? (
            <Thankyoumain form_param={form_param} close_fun={close_fun} />
          ) : (
            <div
              className={`ber-mcont bezid oEq_r eqPdsec eqarch lrSplit ${(lowSoldAd(form_param) || medSoldAd(form_param)) && state.firstSubafterGen ? 'lwSld' : ''}`}
              id={`t${id}_mcontR`}
            >
              <div className="idsf splitid">
                <Left_sec form_param={form_param} id={id} />
                <div id={`t${id}_leftR`} className="ber-Rsc ber-frmpop btPd aligncls">
                  <div
                    id={`t${id}_cls`}
                    className={`ber-cls-rec cp ${(isCityUpdate?.update && updatePopupShow(form_param)) ? 'cityCrs' : ''}`}
                    onClick={() => handleClose("CrossButtonClicked")}
                  >
                    X
                  </div>
                  {(mindet || state.Imeshform == false) && <div id={`t${id}_rightsection`}>
                    {/* {state.secisqscreen && srchapi && srchapi.length>0 && <LocalSellerCard form_param={form_param} searchAPIdata={srchapi} selectedOptions={selectedOptions}/>} */}
                    {shouldRenderPriceWidget(form_param) && !state.isqScreenReached && <PriceWidget form_param={form_param} />}
                    {!state.Imeshform ? (
                      <Login_compt id={id} form_param={form_param}/>
                    ) : state.NECcon == true ? (
                      <Contactdtl
                        fn={fn}
                        em={em}
                        mb={mb}
                        ctid={mdresct}
                        iso={iso}
                        phext={phext}
                        form_param={form_param}
                        MoreReq={false}
                        isCityUpdate={isCityUpdate}
                        setIsCityUpdate={setIsCityUpdate}
                      />
                    ) : state.OTPcon == true ? (
                      <User_ver form_param={form_param} />
                    ) : state.Isqform == true ? (
                      <ISQDtl form_param={form_param} selectedOptions={selectedOptions} setSelectedOptions={setSelectedOptions} selectedOptscr={selectedOptscr} setSelectedOptscr={setSelectedOptscr} />
                    ) : state.RDform == true ? (
                      <Req_detail onIsq={0} form_param={form_param} />
                    ) : (
                      state.newEnq == true  && state.RDNECcon == true? (
                        <Contactdtl
                        fn={fn}
                        em={em}
                        mb={mb}
                        ctid={mdresct}
                        iso={iso}
                        phext={phext}
                        form_param={form_param}
                        MoreReq={false}
                      />
                      ) : ""
                    )}
                    {state.MrEnrForm && <MoreReqDet form_param = {form_param} md_resp={state.md_resp} />}
                  </div>}
                  {prgsabtest && (!(state.thankyou || showThankyou) && state.progBar && state.progressCnt) && <ProgressBar  progress={state.progressstep}/>}
                  {/* {lowSoldAd(form_param) && state.firstSubafterGen==1 ?
                  <> 
                   <RenderAd adUnitPath="/3047175/Enq_form_low_sold_after_generation" sizes={ [[120, 90], [216, 54], [320, 120], [168, 42], [300, 75], [300, 120], [220, 90], [320, 100], [120, 60], [120, 30], [216, 36], [168, 28], [300, 50], [120, 20], [234, 60], [468, 60], [320, 50], [88, 31], [300, 100]]} divId="div-gpt-ad-1756355926177-0" style={{ textAlign: 'center' }} />
                  </> : 
                  medSoldAd(form_param) && state.firstSubafterGen==1 ? 
                  <> 
                   <RenderAd adUnitPath="/3047175/Desktop_forms_Medium//High_after_generation" sizes={[[300, 120], [168, 42], [220, 90], [120, 90], [120, 20], [292, 30], [300, 100], [88, 31], [216, 36], [120, 60], [125, 125], [234, 60], [300, 31], [300, 75], [300, 150], [168, 28], [300, 200], [216, 54], [300, 50], [120, 30], [240, 133]]} divId="div-gpt-ad-1756712951922-0" style={{ textAlign: 'center' }} />
                  </>
                  : ''} */}
                </div>
                {(lowSoldAd(form_param) || medSoldAd(form_param)) && state.firstSubafterGen ? <SearchAdFor form_param={form_param} key={state.firstSubafterGen} /> : ''}
              </div>
            </div>
          )}
        </div>
      </div>
    </React.Fragment>
  );
}
const Enq_form = memo(Form_enqMemo);
export default Enq_form;



