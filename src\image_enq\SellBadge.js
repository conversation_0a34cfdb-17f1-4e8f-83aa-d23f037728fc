import React from "react";
function SellBadge({type}){
    var supplier = {
        1: { name: "Leading Supplier", class: "equLs" },
        2: { name: "Star Supplier", class: "equSs" },
      };
    return(
        type && type !== '0' ? (
            <div className="idsf id_aic emr10">
            <i className= {`imFsp oef0 ${supplier[type].class}`}></i>{supplier[type]["name"]}
        </div>
        ) : null
        
    )
}
export default SellBadge;