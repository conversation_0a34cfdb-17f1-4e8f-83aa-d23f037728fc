import { getparamValREC, isset, readCookieREC, isPresent, loadScriptGtag} from './common/formCommfun';
import callMiniDetAPI from './MinidtlsAPI';
import PostReq from './PostReqApi';

export default async function callPostreq(form_param) {
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    let mdtlres = null;
    let city = "";
    let gliddd = getparamValREC(imesh, "glid") ;
    try {
    mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
    }
    catch (e) {
    mdtlres = null;
    }
    if (mdtlres && mdtlres[gliddd] && mdtlres[gliddd].Response && mdtlres[gliddd].Response.Data) {
    city = isPresent(mdtlres[gliddd].Response.Data[0]) ? mdtlres[gliddd].Response.Data[0] : "";
    } else {
    if (gliddd != "") {
        const data = await callMiniDetAPI(form_param);
        city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0])  ? data.Response.Data[0] : "";
    }
    }

    const urlSearchParams = window.location && window.location.search ? new URLSearchParams(window.location.search) : '';
    let params = {};
    if (urlSearchParams) {
        urlSearchParams.forEach((value, key) => {
            params[key] = value;
        });
    }
    if (params && params.utm_campaign && params.utm_medium && params.utm_medium.includes("prd_ads")) {
        if (!window.gtag) {
            loadScriptGtag("https://www.googletagmanager.com/gtag/js?id=AW-1067418746", () => {
                window.dataLayer = window.dataLayer || []
                window.gtag = (function () { dataLayer.push(arguments) })
                window.gtag('js', new Date());
                window.gtag('config', 'AW-1067418746');
                window.gtag('event', 'conversion', { 'send_to': 'AW-1067418746/xCFQCLy4bRD6iP78Aw', 'value': 1.0, 'currency': 'INR' });
            })
        }
        else {
            window.gtag('event', 'conversion', { 'send_to': 'AW-1067418746/xCFQCLy4bRD6iP78Aw', 'value': 1.0, 'currency': 'INR' });
        }
    }
   
    
    const paramobjpostreq = {
        name: getparamValREC(imesh, 'fn') || "",
        glid: getparamValREC(imesh, 'glid'),
        s_country_iso: getparamValREC(iploc, 'gcniso'),
        s_ip_country: getparamValREC(iploc, 'gcnnm'),
        s_ip: getparamValREC(iploc, 'gip'),
        s_city_id: getparamValREC(imesh, 'ctid'),
    }

    try {
        const resdta = await PostReq(form_param, paramobjpostreq);
        if (resdta.queryid || resdta.ofr) {
            if (form_param.formType === 'Enq') {
                return resdta.queryid;
            }else if (form_param.formType === 'BL') {
                return resdta.ofr;
            }
        }
    } catch (error) {
        console.error("Error in PostReq:", error);
    }
    
    return '';
}
