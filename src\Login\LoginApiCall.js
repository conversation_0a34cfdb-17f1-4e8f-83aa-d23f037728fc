import { getparamValREC, isset, readCookieREC } from '../common/formCommfun';
import getLoginAPI from './LoginAPI';
export default function LoginApiCall(form_param, fld_val, login_flag,ip,iso){
    let Screenname = 'Enq Form on '+form_param.modId;
    if(form_param.formType =="BL"){
        Screenname = 'BL Form on '+form_param.modId;
    }
    const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
    const s_ip_country_iso = getparamValREC(iploc, 'gcniso');
    let login_data = {
        username: fld_val,
        iso: iso,
        identified: 1,
        modid: form_param.modId,
        token: 'imobile@15061981',
        format: 'JSON',
        screen_name: Screenname,
        IP: ip,
        USER_IP: ip,
        ip: ip,
        USER_IP_COUNTRY: form_param.rcvCountry || getparamValREC(iploc, 'gcnnm'),
        GEOIP_COUNTRY_ISO: s_ip_country_iso || window.countryiso,
        IPADDRESS: form_param.rcvCountry || getparamValREC(iploc, 'gcnnm'),
        originalreferer: window.location.href,
        create_user: 1
    };
    let loginapi =  getLoginAPI(login_data, login_flag);
    return loginapi;
}
