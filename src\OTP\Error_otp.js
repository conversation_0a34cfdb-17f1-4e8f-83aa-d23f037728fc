import React from 'react'
// import { clearInputValue } from './OtpInputs';

// export const handleOtpUI = (todo) => {
//     if (todo === "refresh") {
//       clearInputValue(1, 2, 3, 4);//      
//       refs["bl_enqOtp1"].current.focus();
//     }
//   };

function Error_otp() {
  
  return (
    <div id="t0901verifyerrotpdiv" className="errorOtpR" ><span id="t0901verify_err" >OTP sent on Your Mobile</span></div>
    // <div></div>
  )
}

export default Error_otp