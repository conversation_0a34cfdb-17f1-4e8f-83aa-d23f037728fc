import React, { useState, useEffect } from "react";
// import { useGlobalState } from '../context/store';
import { reqFormGATrackREC } from '../common/formCommfun';
// import callPostreq from "../callpostreq";

function ImgBtmCTA({ form_param, showRightSec }) {
    const [slide, setSlide] = useState(false);

    useEffect(() => {
        // setSlide(false);
        const timer = setTimeout(() => {
            setSlide(true);
        }, 300); 

        // return () => clearTimeout(timer);
    }, [form_param]);


    const handliclk = async () => {
        showRightSec(true);
        reqFormGATrackREC("New Image CTA",form_param);
    }

    return (
        <>
            <div ><button
                type="submit"
                className={`btmCTA ${slide ? "slideIn" : ""}`}
                onClick={handliclk}
            >
                <svg width="44" height="37" viewBox="0 0 44 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.780727 29.4299L9.97716 21.81H31.4355C32.2485 21.81 33.0282 21.5424 33.6031 21.0661C34.178 20.5897 34.501 19.9437 34.501 19.27V4.03023C34.501 3.35659 34.178 2.71054 33.6031 2.2342C33.0282 1.75787 32.2485 1.49026 31.4355 1.49026H3.8462C3.03319 1.49026 2.25347 1.75787 1.67859 2.2342C1.10369 2.71054 0.780727 3.35659 0.780727 4.03023V29.4299Z" fill="#05867B" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M9.37838 20.3355V25.5906C9.37838 26.2875 9.70135 26.9558 10.2762 27.4486C10.8511 27.9413 11.6308 28.2182 12.4439 28.2182H33.9022L43.0986 36.1008V9.82531C43.0986 9.12844 42.7757 8.46011 42.2008 7.96735C41.6259 7.47458 40.8462 7.19775 40.0332 7.19775H33.9022" stroke="#05867B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <span className='otSP'>
                    <span className='sendE'>Send Enquiry</span>
                    {form_param.rcvName && <span className='cNmSP'>to {form_param.rcvName}</span>}
                </span>
            </button></div>
        </>
    );

}
export default ImgBtmCTA;




