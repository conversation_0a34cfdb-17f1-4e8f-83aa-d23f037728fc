import React, { useState, useEffect } from "react";
import { isset } from "../common/formCommfun";


function ISQRadio({ item, index, saveIsqs }) {
  const [selectedRadio, setSelectedRadio] = useState(null);


  // useEffect(() => {
  //   const mcatid = window.forms_param.mcatId; 
  //   let userFilledData = [];
  //       try{
  //         userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
  //       }
  //       catch(e){
  //         userFilledData=[];
  //       }
  
  //   if (isset(()=>userFilledData) && isset(()=>userFilledData[mcatid]))  {
  //     const filledData = userFilledData[mcatid];
  
  //     Object.keys(filledData).forEach((key) => {
  //       const value = filledData[key];
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         setSelectedRadio(optionId);
  //       }
  //     });
  //   } else if (window.forms_param && window.forms_param.plsqArr) {
  //     const params = window.forms_param.plsqArr.split('#');
  //     params.forEach(param => {
  //       const [encodedKey, encodedValue] = param.split(':');
  //       const key = decodeURIComponent(encodedKey);
  //       const value = decodeURIComponent(encodedValue);
  //       if (key === item.IM_SPEC_MASTER_DESC && item.IM_SPEC_OPTIONS_DESC.split('##').includes(value)) {
  //         const optionIndex = item.IM_SPEC_OPTIONS_DESC.split('##').indexOf(value);
  //         const optionId = item.IM_SPEC_OPTIONS_ID.split('##')[optionIndex];
  //         saveIsqs(value, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
  //         setSelectedRadio(optionId);
  //       }
  //     });
  //   }
  // }, []);

  
  
  const handleRadioChange = (optionId, optionDesc) => {
    if (selectedRadio === optionId) {
      setSelectedRadio(null);
      saveIsqs("", item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, "");
    } else {
      setSelectedRadio(optionId);
      saveIsqs(optionDesc, item.IM_SPEC_MASTER_DESC, item.IM_SPEC_MASTER_ID, optionId);
    }
  };

  return (
    <div>
      {item.IM_SPEC_MASTER_TYPE === "2" && (
        <div className="row-be txtRw" key={index}>
          <div className="lbtool">
            <div className="beclr"></div>
            <div className="ber-dvtxt bewauto bepr tllb">
              <label id={`t0901ques${index}`} quesid={item.IM_SPEC_MASTER_ID}>
                {item.IM_SPEC_MASTER_DESC}
              </label>
            </div>
            <div className="beclr"></div>
          </div>
          <div>
            {item.IM_SPEC_OPTIONS_DESC.split("##").map((optionDesc, i) => {
              const optionId = item.IM_SPEC_OPTIONS_ID.split("##")[i];
              const isSelected = selectedRadio === optionId;

              return (
                <div
                  key={i}
                  className={`chkBox ${optionDesc.toLowerCase().includes("other") ? "oth_bx other_radio" : ""} ${isSelected ? "selRd" : ""}`}
                >
                  {optionDesc.toLowerCase().includes("other") ? (
                    <input
                      type="text"
                      className="ber-input isqoth"
                      name={`t0901other_checkbox_name${index}`}
                      id={`t0901other_checkbox_name${index}_option${i + 1}`}
                      placeholder="Other Option"
                    />
                  ) : (
                    
                    <input
                      type="radio"
                      className="eqVam bedblk radioClick"
                      name={`t0901radName${index}`}
                      id={`t0901_radName${index}_option${i + 1}`}
                      value={optionDesc}
                      checked={isSelected}
                      onClick={() => handleRadioChange(optionId, optionDesc)}
                    />
                  )}
                  {optionDesc.toLowerCase().includes("other") ? '' : (
                    <label
                      htmlFor={`t0901_radName${index}_option${i + 1}`}
                      optionid={optionId}
                      className="bepr"
                    >
                      <div className={`bechkin`}>
                        <div className={`beradio-sl ${isSelected ? "" : "dispNone"}`}></div>
                      </div>
                      <span className="bevtCss bedblk beisq3 webKflow" id={`t0901_radCheck${index}_option${i + 1}`}>
                        {optionDesc}
                      </span>
                    </label>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export default ISQRadio;
