import React, { useEffect} from 'react';
import QuantitySelector from './QuantityBox';
import ISQRadio from './ISQRadio'; 
import ISQcheckbox from './ISQcheckbox';
import ISQText from './ISQText'; 
import ISQSelect from './ISQSelect';
import { isset } from '../common/formCommfun';

function IsqQues({ data ,savecheckbox,saveIsqs,saveQt,setCansubmit,onInline , responseData, form_param, warning , setWarning}) {

    return data.map((item, index)=> {
        item = !isset(()=>item.IM_SPEC_MASTER_TYPE) && item.length==1 ? item[0] : item;

        if (Array.isArray(item) && item.length === 2) {
            return <QuantitySelector resdata={data} saveQt={saveQt} setCansubmit={setCansubmit} onInline={onInline} responseData={responseData} form_param={form_param} warning={warning} setWarning={setWarning}/>;
        }
        else if(item.IM_SPEC_MASTER_TYPE=='1'){
            return <ISQText item={item} index={index} saveIsqs={saveIsqs}/>;
        }
        else if(item.IM_SPEC_MASTER_TYPE=='2'){
            return <ISQRadio item={item} index={index} saveIsqs={saveIsqs}/>;
        }
        else if(item.IM_SPEC_MASTER_TYPE=='3'){
            return <ISQSelect item={item} index={index} saveIsqs={saveIsqs}/>;
        }
        else if(item.IM_SPEC_MASTER_TYPE=='4'){
            return <ISQcheckbox item={item} index={index} savecheckbox={savecheckbox}/>;
        }

        
    });
};

export default IsqQues