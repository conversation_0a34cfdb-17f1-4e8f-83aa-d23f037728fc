import React from "react";
export function isGDPRCountry(sel_iso) {
  var GdprCountries = [
    "AT",
    "BE",
    "BG",
    "HR",
    "CY",
    "CZ",
    "DK",
    "EE",
    "FI",
    "FR",
    "DE",
    "GR",
    "HU",
    "IE",
    "IT",
    "LV",
    "LT",
    "LU",
    "MT",
    "NL",
    "PL",
    "PT",
    "RO",
    "SK",
    "SI",
    "ES",
    "SE",
    "GB",
    "UK",
  ];
  const iso_exists = GdprCountries.indexOf(sel_iso) !== -1;
  return iso_exists;
}
export default function GDPRCountry({id, setisGDPRcheck, setGDPRerrmsg}){
  const handleCheckboxChange = (event) => {
    const checked = event.target.checked;
    checked == true ?setGDPRerrmsg(""): "";
    setisGDPRcheck(checked);
  };
    return(
        <div id={`t${id}_tCond`} className="pd_b" data-role=""style={{ display: "flex", marginTop: "10px" }}>
            <input type="checkbox" className="chckbx" id={`t${id}_tCondCheckBox`} onChange={handleCheckboxChange}  ></ input>
            <span className=" bevtCss bedblk">
                <span className={`t${id}_test1`}>I agree to the </span> 
                <a href="https://www.indiamart.com/terms-of-use.html" target="_blank" className="betrmt">terms</a> <span className={`t${id}_test1`}>and</span> <a href="https://www.indiamart.com/privacy-policy.html" target="_blank" className="betrmt">privacy policy</a>
            </span>
        </div>
    )
}