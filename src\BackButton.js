import React from 'react';
import './BackButton.css';
import { useGlobalState } from './context/store';
const BackButton = ({PassedFrom}) => {
    const { dispatch } = useGlobalState();
    const handleBack = () => {
        if(PassedFrom == "Req_Detail"){
            dispatch({ type: 'RDform', payload: { RDform: false } });
            dispatch({ type: 'Isqform', payload: { Isqform: true } });
        }
    };

    return (
        <div className="backdivr" onClick={handleBack}>
            <button className="ber-backbtn" type="button"></button>
            <div></div>
        </div>
    );
};

export default BackButton;
