import React, { useEffect, useRef } from "react";
import { isset } from '../common/formCommfun';

const ThankuAdUnit = ({ form_param }) => {
    const slotRef = useRef(null);

    useEffect(() => {
        // Ensure that the slot is cleaned up before re-creating it
        return () => {
            if (slotRef.current && googletag) {
                googletag.destroySlots([slotRef.current]);
                slotRef.current = null;
            }
        };
    }, []);

    useEffect(() => {
        if (window.googletag && googletag.apiReady && typeof googletag.defineSlot === "function") {
            blEnqAds();
        }
    }, [form_param]);

    const blEnqAds = () => {
        if (typeof googletag !== "undefined") {
            window.googletag = window.googletag || { cmd: [] };

            googletag.cmd.push(() => {
                // If a slot already exists, destroy it before creating a new one
                if (slotRef.current) {
                    googletag.destroySlots([slotRef.current]);
                }

                // Define the slot
                slotRef.current = googletag.defineSlot(
                    '/3047175/Desktop_PBRENQ_Adunit',
                    [[320, 50], [320, 100], [300, 50], [300, 100], [728, 90]],
                    'div-gpt-ad-1721029630180-0'
                );

                if (isset(() => slotRef.current)) {
                    slotRef.current.addService(googletag.pubads());
                    googletag.pubads().enableSingleRequest();
                    googletag.enableServices();
                    googletag.display("div-gpt-ad-1721029630180-0");
                }
            });
        }
    };

    return (
        <div id="div-gpt-ad-1721029630180-0" style={{ minWidth: '300px', minHeight: '50px', maxWidth: 'fit-content', margin: '5px auto' }}></div>
    );
};

export default ThankuAdUnit;
