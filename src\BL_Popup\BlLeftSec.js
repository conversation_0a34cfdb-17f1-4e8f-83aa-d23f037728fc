import React from "react";
import { useGlobalState } from "../context/store";
import { isInactBL, sessionValREC, isset,searchAllindia, isBlRevampd } from "../common/formCommfun";
export default function BlLeftSec({ form_param ,headProd , setHeadProd}) {
    const { state } = useGlobalState();
    var img_url = "";
    let isFirstImageUsed = false;

    if (state.openBLPopup && state.openBLPopup.Response && state.openBLPopup.Response.Code == 200 && state.openBLPopup.Response.Data && state.openBLPopup.Response.Data.glcat_mcat_img1_250x250 ) {
        img_url = state.openBLPopup.Response.Data.glcat_mcat_img1_250x250;
    }
    else if (form_param.displayImage != '') {
        img_url = form_param.displayImage;
    }
    else if (form_param.zoomImage != '') {
        img_url = form_param.zoomImage;
    }
    else if (form_param.blMultiImage && form_param.blMultiImage.length > 0) {
        img_url = form_param.blMultiImage[0];
        isFirstImageUsed = true;
    }

    if (isset(() => img_url)) {
        img_url = img_url.replace("http:", "");
    }
    const style = {
        backgroundImage: `url(${img_url})`
    };


    let brd_mcat_id = form_param.mcatId;
    let prodsData = isset(() => sessionValREC("prods-" + brd_mcat_id)) ? sessionValREC("prods-" + brd_mcat_id) : [];
    let catsData = isset(() => sessionValREC("cats-" + brd_mcat_id)) ? sessionValREC("cats-" + brd_mcat_id) : [];
    return (
        <div className={`ber-LscBL oef0 br24L ${isInactBL(form_param) ? ((catsData.length != 0 || prodsData.length != 0) && !searchAllindia(form_param)) ? 'ber-LscHeight boxsizeInact withRecom' : 'ber-LscHeight boxsizeInact' : ''}`}>
            {isInactBL(form_param) && state.frscr == 1 ? <div id="blheading" className="inactFrH "><div className="inactHead">Tell us your requirement and <strong>get free quotes</strong> from multiple sellers</div></div> : ''}

            { !isBlRevampd(form_param) ? 
            <div className="ber-pnmS">
                <div className={`ber-prdimg ${isInactBL(form_param) ? 'mtPrdimg' : ''}`} >
                    {img_url ?
                        <><img src={img_url}></img>
                            <span className="ber-blrprdimg" style={style}></span></> :

                        <><div id="defimg" className="ber-nobgimg"><div className="blnewfo_sprit be-noimgR"></div></div></>}


                </div>
            </div> : ''}

            {isBlRevampd(form_param) ?  <div className='inrDv'>
                <div className='imgDivs exclDiv'>
                     {img_url ? (
                        <><img src={img_url}></img>
                            {/* <span className="ber-blrprdimg imgInside" style={style}></span> */}
                            </>
                     ) : (
                        <><div id="defimg" className="ber-nobgimg imgInside"><div className="blnewfo_sprit be-noimgR"></div></div></>
                     )}
                </div>

                {form_param.blMultiImage && form_param.blMultiImage.length > (isFirstImageUsed ? 2 : 1) ?
                    <div className='idsf imgConDiv'>
                        {form_param.blMultiImage.slice(isFirstImageUsed ? 1 : 0, isFirstImageUsed ? 4 : 3).map((imgUrl, index) => {
                            return (
                                <div key={index} className='imgDivs'>
                                    <img src={imgUrl.replace("http:", "")} alt={`Product ${index + (isFirstImageUsed ? 2 : 1)}`} />
                                </div>
                            );
                        })}
                    </div>:
                   <div className='idsf imgConDiv'>
                        <div className='imgDivs boxsNn'></div>          
                    </div>
                }

                <div className="divhd">
                    {headProd ? <div id="t0901_hdg" data-role="" className="headBl txtElp"><span className="befwt marBTM">Looking to buy {headProd} ?</span></div> : <div id="t0901_hdg" data-role="" className="headBl"><span className="befwt marBTM">Looking to buy something?</span></div>}
                    
                    <div className="befs16 bemb20 blMarfn inHead">Just complete a few simple steps to get Instant quotes from Verified Suppliers</div>
                </div>

            </div> : ''
            }
           

             {(!isInactBL(form_param)) ? isBlRevampd(form_param) ? '' : 
             <div className="ber-help " style={{display:"block"}}>
                <div className="beclrW" >How it Works</div>
                <div className="bepr" >
                    <div className="ber-hlpd"></div>
                    <div className="behlp1 ">
                        <div className="bedtc-r"><i className="bedotW"></i></div>
                        <div className="bevtCss" >Tell us what you need by filling the form</div>
                    </div>
                    <div className="behlp1 ">
                        <div className="bedtc-r"><i className="bedotW"></i></div>
                        <div className="bevtCss" >Receive Verified supplier details</div>
                    </div>
                    <div className="behlp1 ">
                        <div className="bedtc-r"><i className="bedotW"></i></div>
                        <div className="bevtCss" >Compare Quotations and seal the deal</div>
                    </div>
                </div>
            </div> :  <div className="ber-help " style={{display:"block"}}>
                    <div className="beclrW inactBeclrW" >How it Works</div>
                    <div>
                        <div className="ber-hlpd"></div>
                        <div className="inBlsec ">
                            <div className="bedtc bed_icon"><svg width="17" height="18" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.2208 12.3234L0.990359 12.3243V9.65808L17.2189 9.65714L10.0677 2.50593L11.9533 0.62031L22.3242 10.9912L11.9533 21.3621L10.0677 19.4765L17.2208 12.3234Z" fill="black"></path></svg></div>
                            <div className="bevtCss" >Tell us what you need by filling the form</div>
                        </div>
                        <div className="inBlsec ">
                            <div className="bedtc bed_icon"><svg width="17" height="18" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.2208 12.3234L0.990359 12.3243V9.65808L17.2189 9.65714L10.0677 2.50593L11.9533 0.62031L22.3242 10.9912L11.9533 21.3621L10.0677 19.4765L17.2208 12.3234Z" fill="black"></path></svg></div>
                            <div className="bevtCss" >Receive Verified supplier details</div>
                        </div>
                        <div className="inBlsec ">
                            <div className="bedtc bed_icon"><svg width="17" height="18" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.2208 12.3234L0.990359 12.3243V9.65808L17.2189 9.65714L10.0677 2.50593L11.9533 0.62031L22.3242 10.9912L11.9533 21.3621L10.0677 19.4765L17.2208 12.3234Z" fill="black"></path></svg></div>
                            <div className="bevtCss" >Compare Quotations and seal the deal</div>
                        </div>
                    </div>
                </div>}


        </div>
    )
}