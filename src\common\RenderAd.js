import React, { useEffect } from 'react';

const RenderAd = ({ adUnitPath, sizes, divId, style }) => {
  useEffect(() => {
    if (!window.googletag) {
      window.googletag = { cmd: [] };
    }

    let slot;

    window.googletag.cmd.push(() => {
      slot = window.googletag.defineSlot(adUnitPath, sizes, divId);

      if (slot) {
        slot.addService(window.googletag.pubads());
        window.googletag.pubads().enableSingleRequest();
        window.googletag.pubads().collapseEmptyDivs();
        window.googletag.enableServices();
        window.googletag.display(divId);
      }
    });

    return () => {
      if (slot && window.googletag?.destroySlots) {
        window.googletag.cmd.push(() => {
          window.googletag.destroySlots([slot]);
        });
      }
    };
  }, [adUnitPath, sizes, divId]);

  return <div id={divId} style={style} />;
};

export default RenderAd;
