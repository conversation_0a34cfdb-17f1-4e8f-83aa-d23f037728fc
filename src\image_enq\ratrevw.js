import React from "react";
function RatRevw({form_param}) {
  var review = form_param.additionalDtls && form_param.additionalDtls.reviewCount && form_param.additionalDtls.reviewCount !== 0 ? form_param.additionalDtls.reviewCount : -1;

  var starCount = form_param.additionalDtls && form_param.additionalDtls.seller_rating && form_param.additionalDtls.seller_rating !== 0 ? form_param.additionalDtls.seller_rating : 0;
  var width_par = (starCount / 5) * 100;
  return(
    starCount && review && !(parseInt(starCount) <= 0 && parseInt(review) <= 0) ? (
        <div className="befs14 eqRC1 disp-inl bemt5 ">
            {
                parseInt(starCount) > 0 ?(
                <>
                    <span className="befwt">{starCount}</span>/5 
                    <div className="eqsRt disp-inl e_f18 txtl bepr">
                        <div className="eqflsRt eqd_l0 beabult" style={`width:${width_par}%`}>
                            <span>★★★★★</span>
                        </div>
                        <div className="eqemsRt"><span>★★★★★</span></div>
                    </div>
                </>
                ) : null
            }
            {
                parseInt(review) > 0 ?(
                <>
                    <span className="eqRC2 epf12">{`(${review} Reviews)`}</span>
                </>
                ) : null
            }</div>
    ) : null
  )
}
export default RatRevw;

