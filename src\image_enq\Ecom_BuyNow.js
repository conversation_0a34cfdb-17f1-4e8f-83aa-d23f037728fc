import React from "react";
function Ecom_BuyNow({form_param, close_fun, handleecomlgnSub}){
    function BuyNow_click(){
        handleecomlgnSub == "" ?  "" : handleecomlgnSub();
        window.open(form_param.ecomUrl, "_blank");
        close_fun();
    }
    return (
        <>
        <div id="t0901submit_wrapper" className="txt-cnt">
        <div id="t0901_fBtn" className="bepr eqClearfx" >
        <div id="t0901_submitdiv">
            <input value="Buy Now" className="befstgo2 hovsub befwt" id="t0901_submit" type="submit" onClick={handleecomlgnSub == "" ? BuyNow_click : handleecomlgnSub} /> 
        </div>
        <span className="befs11">* You will be redirected to 3rd party webstore<span></span></span>
        </div></div>
        </>
    )
}
export default Ecom_BuyNow;