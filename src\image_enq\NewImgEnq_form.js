

import React, { memo, useEffect, useRef, useState } from "react";
import NewImfleft_sec from "./NewImfleft_sec";
import './NewImgVar.css';
import { useGlobalState } from "../context/store";
import Thankyoumain from "../Thankyou/Thankyoumain";
import { readCookieREC, getparamValREC, isset, stopBgScrollREC, reqFormGATrackREC, imeqglval, SaveISQonCross, Eventtracking,isPresent,imageabtest } from "../common/formCommfun";
import ISQDtl from "../ISQDtl";
import Login_compt from "../Login/Login_compt";
import Req_detail from "../Req_detail";
import MoreReqDet from "../MoreReqDet";
import User_ver from "../OTP/User_ver";
import IntGenApi from "../main/IntGenApi";
import Contactdtl from "../ContactDetails/ContactDtl";
import CallMultiImg from "./CallMultiImg";
import CallPDPmultiImg from "./CallPDPmultiImg";
import callMiniDetAPI from "../MinidtlsAPI";
import Ecom_BuyNow from "./Ecom_BuyNow";
import ImgBtmCTA from "./ImgBtmCTA";
// import ImageformAd from "./ImageformAd";
import ImageAnchorAd from "./ImageAnchorAd";
import ProdISQ from "./ProdISQ";
import SellBadge from "./SellBadge";
import VerBadge from "./VerBadge";
import RatRevw from "./ratrevw";

export function NewFrm_imgenqMemo({ form_param, id, close_fun}) {
  const { state, dispatch } = useGlobalState();
  // const form_param = state.form_param;
  // State variables for ImeshVisitor data
  const [fn, setFn] = useState("");
  const [em, setEm] = useState("");
  const [mb, setMb] = useState("");
  const [iso, setIso] = useState("");
  const [mdresct, setMdresct] = useState("");
  const [phext, setPhext] = useState("");
  const [toBlur, setToBlur] = useState(false);
  const [showThankyou, setShowThankyou] = useState(false);
  const [download, setDownload] = useState(0);
  const [mindet, setmindet] = useState({});
  const [reset, setReset] = useState(true);
  const [pdfselected, setPdfselected] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedOptscr, setSelectedOptscr] = useState([]); 
  const [rightShow, setRightShow] = useState(true);
  window.isBLFormOpen = true;
  let compurl = form_param.redirectUrl != undefined ? form_param.redirectUrl.cmpUrl : "";
  let proddname = isset(() => form_param.prodDispName) && form_param.prodDispName != '' ? form_param.prodDispName : isset(() => form_param.prodName) && form_param.prodName != '' ? form_param.prodName : '';
  stopBgScrollREC();

  window.otpcountRes = 0;
  window.otpcountReser = 0;
  let multiImgres = '';
  const prodCountRef = useRef(0);

  useEffect(() => {
    const ismshexist = readCookieREC("ImeshVisitor");
    if (ismshexist == null) {
      dispatch({ type: "Imeshform", payload: { Imeshform: false } });
    } else {
      dispatch({ type: "Imeshform", payload: { Imeshform: true } });
    }
    dispatch({ type: "Isqform", payload: { Isqform: false } });
    dispatch({ type: "RDform", payload: { RDform: false } });
    dispatch({ type: "MrEnrForm", payload: { MrEnrForm: false } });
    dispatch({ type: "thankyou", payload: { thankyou: false } });
  }, [form_param]);
  useEffect(() => {
    const fetchMiniDetAPI = async () => {
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      let mdtlres = null;
      let city = "";
      try {
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
      }
      catch (e) {
        mdtlres = null;
      }
      if (mdtlres && mdtlres[getparamValREC(imesh, "glid")]) {
        city = isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[0]) ? mdtlres[getparamValREC(imesh, "glid")].Response.Data[0] : "";
        dispatch({ type: 'md_resp', payload: { md_resp: mdtlres} });
        setmindet(mdtlres[getparamValREC(imesh, "glid")]);
      } else {
        if (getparamValREC(imesh, "glid") != "") {
          const data = await callMiniDetAPI(form_param);
          city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0] )? data.Response.Data[0] : "";
          if (isset(() => data) && data && data.Response && data.Response.Data) {
            setmindet(data);
            dispatch({ type: 'md_resp', payload: { md_resp: data.md_resp } });
           } else { setmindet("No Response From Service"); }
        }
      }
      city = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : city ;
      setMdresct(city != "" ? "1" : "");
      const visitorFn = getparamValREC(imesh, "fn") || state.UserData.fn;
      const visitorEm = getparamValREC(imesh, "em") || state.UserData.em;
      const visitorMb = getparamValREC(imesh, "mb1") || state.UserData.mb1;
      const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
      const phext = getparamValREC(imesh, "phcc") || state.UserData.phcc;

      setFn(visitorFn);
      setEm(visitorEm);
      setMb(visitorMb);
      setIso(visitorIso);
      setPhext(phext);

      const nec_con =
        ((visitorFn == "" || city == "") && visitorIso == "IN") ||
          (visitorMb == "" && visitorIso != "IN")
          ? true
          : false;

      const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
      const otp_con = uv != "V" && visitorIso == "IN" ? true : false;

      if (nec_con == true) {
        dispatch({ type: "NECcon", payload: { NECcon: true } });
        if (toBlur && state.Imeshform) {
          blurImg(false);
          incrementProdCount(0);
          if (isset(() => window.downBlur) && window.downBlur == 1) {
            setDownload(1);
            window.downBlur = 0;
          }
        }

      } else {
        dispatch({ type: "NECcon", payload: { NECcon: false } });
        if (otp_con == true) {
          dispatch({ type: "OTPcon", payload: { OTPcon: true } });
          if (toBlur && state.Imeshform) {
            blurImg(false);
            incrementProdCount(0);
            if (isset(() => window.downBlur) && window.downBlur == 1) {
              setDownload(1);
              window.downBlur = 0;
            }
          }
        }
      }
    };

    fetchMiniDetAPI();
  }, [state.Imeshform == false, form_param]);
  useEffect(() => {
    dispatch({ type: "postreq", payload: { postreq: 0 } });

    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    id == "0901" && imesh != "" ? IntGenApi(form_param) : "";
  }, [form_param]);


  useEffect(() => {
    dispatch({ type: 'multiImg', payload: { multiImg: false } });
    dispatch({ type: 'singleimgsld', payload: { singleimgsld: false } });
    if (form_param.blurImg == "blurImg") {
      blurImg(true);
    }
  }, [form_param]);


  useEffect(() => {
    if (!(
      isset(() => form_param.multipleImageVideo) &&
      form_param.multipleImageVideo != ""
    )) {
      multiImgres = form_param.modId=='PRODDTL' || form_param.modId == 'MY' ? CallPDPmultiImg(form_param) :  CallMultiImg(form_param);
      if (multiImgres instanceof Promise) {
        multiImgres.then(response => {
          if (isset(() => response) && response != "") {
            dispatch({ type: 'multiImg', payload: { multiImg: true } });
            dispatch({ type: 'singleimgsld', payload: { singleimgsld: false } });
          }
          else {
            dispatch({ type: 'multiImg', payload: { multiImg: false } });
            dispatch({ type: 'singleimgsld', payload: { singleimgsld: true } });
          }
        }).catch(error => {
          console.error(error);
          imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'ImageMulti', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
        });
      }
    }
    else {
      dispatch({ type: 'multiImg', payload: { multiImg: true } });
      dispatch({ type: 'singleimgsld', payload: { singleimgsld: false } });
    }
  }, [form_param]);

  useEffect(() => {
    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
    const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
    const otp_con = uv != "V" && visitorIso == "IN" ? true : false;
    if (state.NECcon == false && otp_con == true) {
      dispatch({ type: "OTPcon", payload: { OTPcon: true } });
      if (toBlur && state.Imeshform) {
        blurImg(false);
        incrementProdCount(0);
        if (isset(() => window.downBlur) && window.downBlur == 1) {
          setDownload(1);
          window.downBlur = 0;
        }
      }
    } else {
      dispatch({ type: "OTPcon", payload: { OTPcon: false } });
      dispatch({ type: "Isqform", payload: { Isqform: true } });
      if (toBlur && state.Imeshform) {
        blurImg(false);
        incrementProdCount(0);
        if (isset(() => window.downBlur) && window.downBlur == 1) {
          setDownload(1);
          window.downBlur = 0;
        }
      }
    }
  }, [state.Imeshform, form_param]);

  const blurImg = (val) => {
    if (val === true) {
      showRightSec(true);
    }  
    setToBlur((prevToBlur) => {
      if (val === true && !prevToBlur) {
        reqFormGATrackREC('BlurFormView',form_param)
      } else if (val === false && prevToBlur) {
        reqFormGATrackREC('BlurFormSubmit',form_param)
      }  
      return val; 
    });
  };
  

  const showRightSec = (val) => {
    setRightShow(val);
  }


  const incrementProdCount = (value) => {
    if (!state.Imeshform) {
      if (value == 0) {
        prodCountRef.current = 0;
        // setprodCount(0);
      } else {
        prodCountRef.current++
        // setprodCount();
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.keyCode === 27) {
      handleClose('EscapeKeyPressed');
    }
  };

  const collapseRS = async () => {
    showRightSec(false);
    reqFormGATrackREC("popup_collapse",form_param);
}

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [form_param]);

  const handleClose = (source = '') => {
    Eventtracking("CS" + window.screencount + "|" + state.currentscreen + "|" + source, state.prevtrack, form_param, false);
    window.downBlur = 0;
    setReset(true);
    setPdfselected(false);
    const imeqarr = imeqglval();
    if (!imeqarr.Enq && form_param.formType != 'BL') {
      window.showskipBut = true;
    }
    else {
      window.showskipBut = false;
    }
    if (!showThankyou && (state.postreq != undefined && state.postreq != 0) && !state.thankyou) {
      SaveISQonCross(form_param, selectedOptions, state.postreq)
      setShowThankyou(true);
    } else {
      close_fun();
    }
    dispatch({ type: 'thankyou', payload: { thankyou: false } });
  };

  let price = form_param.price;
  let pr = "", ut = "";

  if (price) {
      // Remove currency symbols and unnecessary text
      price = price.replace(/₹|Approx.*?Rs|Rs/g, "").trim();

      // Extract price and unit safely
      [pr, ut] = price.includes("/") ? price.split("/").map(item => item.trim()) : [price, ""];

      // Format price with Rupee symbol (HTML encoded) and append "/" only if unit exists
      pr = pr ? `₹ ${pr}${ut ? "/" : ""}` : `₹ ${ut}`;
  }

  return (
    <React.Fragment>
      <div className="ber-frwrap" id={`t${id}_bewrapper`} style={{zIndex:"999"}}>
        <div
          className="blckbg"
          id={`t${id}_blkwrap`}
          onClick={() => handleClose("OutsideClicked")}
        ></div>
        <div className={`frmcont inf-scroll_new ${!(state.thankyou || showThankyou) ? 'fullImgfr' : ''} ${imageabtest(form_param) ? 'fbImage' : ''}`}>

          {state.thankyou || showThankyou ? (
            <Thankyoumain form_param={form_param} close_fun={close_fun} />
          ) : (
            <div
              className={`ber-mcont bezid oEq_r eq_img eqtstR arrprdch imgscroll posunset db ${toBlur ? 'blrpopR' : ''}`}
              id={`t${id}_mcontR`}
            >
              <div className="idsf ">

                {/* {toBlur && form_param?.pDispId == 22284769573 ? <ImageformAd/> : ''} */}
                <NewImfleft_sec form_param={form_param} id={id} blurImg={blurImg} incrementProdCount={incrementProdCount} prodCountRef={prodCountRef} download={download} setDownload={setDownload} setReset={setReset} reset={reset} showRightSec={showRightSec}setPdfselected={setPdfselected} pdfselected={pdfselected} toBlur={toBlur}/>

                
                <div
                  id={`t${id}_leftR`}
                  className={`ber-Rsc ber-frmpop btPd hidRight ${
                    rightShow ? (pdfselected ? 'pdfformright' : 'slideOut') : ''
                  }`}
                >
                  <div
                    id={`t${id}_cls`}
                    className="ber-cls-rec closeScrl cp"
                    onClick={() => handleClose("CrossButtonClicked")}
                  >

                  </div>
                  <div id="upperDv">
                    <span id='colps' className={`${imageabtest(form_param) ? 'dispNone' : ''}`} onClick={collapseRS}></span>
                    <span>Quick Requirement Form</span>
  
                    </div>
                  <div id={`t${id}_rightsection`}>
                    {(!state.Imeshform || state.NECcon || state.OTPcon || state.Isqform || state.RDform || state.MrEnrForm) && <div
                      id={`t${id}_rightproddetails`}
                      data-role=""
                      
                    >
                      <div className="epLf30">
                        <div className="pr">
                          {form_param.redirectUrl != undefined && form_param.redirectUrl.produrl != undefined ? (
                            <>
                              <a target='_blank' href={`${form_param.redirectUrl.produrl}`} id={`t${id}_Prodname0R`} className="eqElps eqElps2 ber-pnm eprod" data-role="" >
                                <svg
                                  fill="#2e3192"
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 -3 24 24"
                                  width="22px"
                                  height="22px"
                                  style={{ position: 'absolute', left: '-23px', top: '12px' }}
                                >
                                  <path d="M 5 3 C 3.9069372 3 3 3.9069372 3 5 L 3 19 C 3 20.093063 3.9069372 21 5 21 L 19 21 C 20.093063 21 21 20.093063 21 19 L 21 12 L 19 12 L 19 19 L 5 19 L 5 5 L 12 5 L 12 3 L 5 3 z M 14 3 L 14 5 L 17.585938 5 L 8.2929688 14.292969 L 9.7070312 15.707031 L 19 6.4140625 L 19 10 L 21 10 L 21 3 L 14 3 z"></path>
                                </svg>
                                {proddname}
                              </a>
                            </>
                          ) : (<>
                            <div id={`t${id}_Prodname0R`} className="eqElps eqElps2 ber-pnm eprod" data-role="" >
                              {proddname}
                            </div>
                          </>
                          )
                          }

                        </div>
                        {form_param.price && <div id={`t${id}_ProdPrice0R`} className="eqprodpr">
                          <span id={`t${id}_price0L`} className="eqpr">{pr}</span>
                          <span id="t0901_unit0L">{ut}</span>
                        </div>}
                        {/* {imageabtest(form_param) && form_param.plsqArr !== "" && <ProdISQ form_param={form_param} vcdShow={state.Imeshform} />} */}
                        {imageabtest(form_param) && window.screencount==1 && <div id="t0901sellerdiv0R" className="ibgc eqmt5 sldBy" data-role="" >
                          {compurl ?  <div><div id="t0901_soldBy0R" className="eqsoldby eqElps eqElps1" data-role="" >
                            {/* <span id="t0901_sold0R" className="eqsold befwt">Sold By - </span> */}
                            <span id="t0901_addr0R" className="sClr"><a href={compurl} target="_blank" className="enqa enqcmp sClr" id="t0901_ie_cmp_0R">{form_param.rcvName}</a></span></div>
                             <div id="t0901_soldBy0RC" className="eqsoldby eqElps eqElps1" data-role="" >
                            <span id="t0901_addr0RC" className="sClr highCity">{form_param.rcvCity}, {form_param.rcvState}</span></div></div>  : 
                          <div>
                          <div id="t0901_soldBy0R" className="eqsoldby eqElps eqElps1" data-role="" >
                            {/* <span id="t0901_sold0R" className="eqsold befwt">Sold By - </span> */}
                            <span id="t0901_addr0R" className="sClr">{form_param.rcvName}</span></div>
                            <div id="t0901_soldBy0RC" className="eqsoldby eqElps eqElps1 " data-role="" >
                            <span id="t0901_addr0RC" className="sClr highCity">{form_param.rcvCity}, {form_param.rcvState}</span></div></div>
                            }     
                          <RatRevw form_param={form_param} /><div className="idsf eflwp befs14 eqRC3 bemt10">{form_param.additionalDtls !== undefined && (
                          <>
                            <SellBadge type={form_param.additionalDtls.supplier} />
                            <VerBadge type={form_param.additionalDtls.verified} />
                          </>
                        )}</div></div>}
                      </div>
                    </div>}
                    {!imageabtest(form_param) && <div className='hrLine'></div>}
                     {(mindet || state.Imeshform == false) && <div id="t0901_questionouterwrapper" className="epLf30" data-role="" >

                      {toBlur ? window.downBlur == 1 ? <div className="blurMsg">Please login to Download the Photo</div> : state.multiImg ? <div className="blurMsg">Please login to view all {isset(() => form_param.multipleImageVideo) && form_param.multipleImageVideo.length > 1 ? form_param.multipleImageVideo.length : isset(() => window.pdpMultires) ? window.pdpMultires.length : ''} Photos</div> : <div className="blurMsg">Please login to view the Photo</div> : ''}
                      {!state.Imeshform ? (
                        <Login_compt id={id} form_param={form_param} close_fun={close_fun} />
                      ) : form_param.isEcom == 1 ? (
                        <Ecom_BuyNow form_param={form_param} close_fun={close_fun} handleecomlgnSub="" />
                      ) : state.NECcon == true ? (
                        <Contactdtl
                          fn={fn}
                          em={em}
                          mb={mb}
                          ctid={mdresct}
                          iso={iso}
                          phext={phext}
                          form_param={form_param}
                          MoreReq={false}
                        />
                      ) : state.OTPcon == true ? (
                        <User_ver form_param={form_param} />
                      ) : state.Isqform == true ? (
                        <ISQDtl form_param={form_param} selectedOptions={selectedOptions} setSelectedOptions={setSelectedOptions} selectedOptscr={selectedOptscr} setSelectedOptscr={setSelectedOptscr}/>
                      ) : state.RDform == true ? (
                        <Req_detail onIsq={0} form_param={form_param} />
                      ) : (
                        ""
                      )}
                      {/* {state.Isqform && <ISQDtl />} */}
                      {/* {state.RDform && <Req_detail />} */}
                      {state.MrEnrForm && <MoreReqDet md_resp={state.md_resp} form_param={form_param} />}
                    </div>}
                  </div>
                </div>
              </div>
              {toBlur && <ImageAnchorAd/>}

              {!rightShow && !imageabtest(form_param)? <ImgBtmCTA form_param={form_param} showRightSec={showRightSec} /> : ''}






            </div>
          )}
        </div>
      </div>
    </React.Fragment>
  );
}
const NewImgEnq_form = memo(NewFrm_imgenqMemo);
export default NewImgEnq_form;

