import React from 'react';
import { isset ,reqFormGATrackREC, sessionValREC,searchAllindia } from '../common/formCommfun';
import './imgEnq.css';

const ProdsInactive = ({ form_param }) => {
    let brd_mcat_id = form_param.mcatId;

    let prodsData = isset(() => sessionValREC("prods-" + brd_mcat_id)) ? sessionValREC("prods-" + brd_mcat_id) : [];

  if ((!isset(() => prodsData) || prodsData.length <= 1) || (searchAllindia(form_param))) {
    return null;
  }

  const text = "More Products For You";
  const proclass = "ProdList-Item-Name";
  const param = "blform=2";

  return (
    <div id="recommendProdImg" className="VSP-SECI" style={{ display: 'block' }}>
      <div className="vs-heading">
        <h3>{text}</h3>
      </div>
      <ul id="prodList" className="ProBoxULI">
        {prodsData.slice(0, 6).map((item, index) => {

          const itemUrl = isset(() => item.ProdUrl) && item.ProdUrl !== "" ?
            item.ProdUrl.endsWith(".html") ? `${item.ProdUrl}?${param}` : `${item.ProdUrl}&${param}` : "''";

          return (
            <li key={index} className="berds10 ibgc" id={`recomProd${index + 1}`} onClick={() => reqFormGATrackREC(`Prod_${index + 1}`,form_param)}>
              <a target="_blank" href={itemUrl} className="ProBox-Item disp-inl">
                <div className="Proimg">
                  <img src={item.ProdImage} alt={item.ProdName} />
                </div>
                <p className={`${proclass} color3 atxu cbl_fs16 befwt`}>{item.ProdName}</p>
                <p className="proPrice">{isset(() => item.Price) && item.Price !== "" ? item.Price : "Ask Price"}</p>
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default ProdsInactive;