import React from 'react';
import { recordOutboundd, resumeBgScrollREC } from './common/formCommfun';

export default class ErrorBoundary extends React.Component {
    constructor(props) {
      super(props);
      this.state = { error: null, errorInfo: null };
    }
    componentDidCatch(error, errorInfo) {
      this.setState({
        error: error,
        errorInfo: errorInfo
      });

      let errMsg = error;
      if(errorInfo && Object.keys(errorInfo).length !== 0) {
        errMsg+= ` :- ${JSON.stringify(errorInfo)}`;
      }
      recordOutboundd(0, "Forms-Error_B", errMsg, 0,window.location.href);
      resumeBgScrollREC();
    }

    render() {
      if (this.state.errorInfo) {
        // console.log(this.state.errorInfo);
        // console.log(this.state.error);
        
        return (
          <></>
        );
      }
      return this.props.children;
    }
}