import React, { useState, useEffect } from 'react';
import { useGlobalState } from '../../context/store';
import { Eventtracking, readCookieREC, shouldRenderPriceWidget } from '../../common/formCommfun';
import fetchPriceData from './PriceAPI';
import './PriceWidget.css';

// Component for non-authenticated users - no sensitive data in DOM
const LoginPromptWidget = () => {
  return (
    <div className="price-widget">
      <div className="price-widget-header">
        <span>Best Price Available!</span>
      </div>
      <div className="price-widget-content">
        <div className="login-prompt">
          <div className="locked-icon"><svg class="svg-inline--fa fa-lock" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="lock" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="#f97316" d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"></path></svg></div>
          <div className="prompt-text">
            <div className="main-message">Login to Know the Market Price</div>
            <div className="disclaimer">This price will help you in negotiation with seller</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Component for authenticated users - only loads when user is logged in
const AuthenticatedPriceWidget = ({ form_param }) => {
  const { dispatch } = useGlobalState();
  const [lowestPricedProduct, setLowestPricedProduct] = useState(null);
  const [allProducts, setAllProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [shouldRender, setShouldRender] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [startFadeOut, setStartFadeOut] = useState(false);

  useEffect(() => {
    const loadPriceData = async () => {
      try {
        setLoading(true);

        const result = await fetchPriceData(form_param);

        if (result.success && result.data.products && result.data.products.length > 0) {
          // Store all products for price comparison
          setAllProducts(result.data.products);

          // Find the lowest priced product
          const lowest = result.data.products.reduce((min, product) =>
            product.price < min.price ? product : min
          );

          // Start fade-out animation immediately when API responds
          setStartFadeOut(true);
          window.widgetformed = true;
          // Wait for fade-out animation to complete, then show content immediately
          setTimeout(() => {
            setLowestPricedProduct(lowest);
            setShouldRender(true);
            setLoading(false);

            // Track successful widget rendering
            const filteredProductsCount = result.data.products.filter(product => product.DISPLAY_ID !== form_param.pDispId).length;
            Eventtracking(`Price_Widget_Formed`, `Success - total_products: ${result.data.products.length} | filtered_products: ${filteredProductsCount}`, form_param, true);

            // Dispatch successful API response to global state
            dispatch({
              type: "priceWidgetApiResponse",
              payload: { priceWidgetApiResponse: true }
            });
          }, 800); // Wait for fade-out animation to complete
        } else {
          // No results or API failure - start fade-out immediately
          setStartFadeOut(true);

          // Track widget not rendering due to no products
          Eventtracking(`Price_Widget_Not_Formed`, `reason: no_products_available - count: ${result.data?.products?.length || 0}`, form_param, true);

          // Wait for fade-out, then hide widget
          setTimeout(() => {
            setShouldRender(false);
            setLoading(false);

            // Dispatch failed API response to global state
            dispatch({
              type: "priceWidgetApiResponse",
              payload: { priceWidgetApiResponse: false }
            });
          }, 800);
        }
      } catch (err) {
        console.error('Price widget error:', err);
        // API error - start fade-out immediately
        setStartFadeOut(true);

        // Track widget not rendering due to API error
        Eventtracking(`Price_Widget_Not_Formed`, `reason: api_error - error_message: ${err.message || 'Unknown error'} | error_type: ${err.name || 'API Error'}`, form_param, true);

        // Wait for fade-out, then hide widget
        setTimeout(() => {
          setShouldRender(false);
          setLoading(false);

          // Dispatch failed API response to global state
          dispatch({
            type: "priceWidgetApiResponse",
            payload: { priceWidgetApiResponse: false }
          });
        }, 800);
      }
    };

    loadPriceData();
  }, [form_param]);

  // Don't render widget if no data available
  if (!shouldRender) {
    return null;
  }

  // Convert lakh prices to numeric values (e.g., "1.02 lakh" -> 102000)
  const convertLakhToNumeric = (priceStr) => {
    if (!priceStr) return priceStr;

    const str = priceStr.toString().toLowerCase();

    // Check if price contains "lakh"
    if (str.includes('lakh')) {
      // Extract the numeric part before "lakh"
      const match = str.match(/(\d+\.?\d*)\s*lakh/);
      if (match) {
        const lakhValue = parseFloat(match[1]);
        const numericValue = lakhValue * 100000; // 1 lakh = 100,000

        // Replace the lakh part with numeric value in the original string
        // Keep the currency symbol and unit if present
        const beforeLakh = priceStr.substring(0, priceStr.toLowerCase().indexOf(match[0]));
        const afterLakh = priceStr.substring(priceStr.toLowerCase().indexOf(match[0]) + match[0].length);

        return beforeLakh + numericValue.toLocaleString('en-IN') + afterLakh;
      }
    }

    return priceStr;
  };

  // Extract numeric values from price strings
  const extractPrice = (priceStr) => {
    if (!priceStr) return 0;

    const str = priceStr.toString().toLowerCase();

    // Handle lakh values first
    if (str.includes('lakh')) {
      const match = str.match(/(\d+\.?\d*)\s*lakh/);
      if (match) {
        const lakhValue = parseFloat(match[1]);
        return lakhValue * 100000; // Convert to numeric
      }
    }

    // Handle regular numeric values
    const numericValue = priceStr.toString().replace(/[^\d.]/g, '');
    return parseFloat(numericValue);
  };

  // Calculate percentage difference between main product price and widget price
  const calculatePercentageLess = (mainPrice, widgetPrice) => {
    if (!mainPrice || !widgetPrice) return null;

    const mainPriceNum = extractPrice(mainPrice);
    const widgetPriceNum = extractPrice(widgetPrice);

    if (mainPriceNum && widgetPriceNum && mainPriceNum > widgetPriceNum) {
      const percentage = ((mainPriceNum - widgetPriceNum) / mainPriceNum * 100).toFixed(0);
      return percentage;
    }

    return null;
  };

  // Calculate price comparison data for gradient bar
  const calculatePriceComparison = () => {
    if (!allProducts || allProducts.length === 0) return null;

    const mainPrice = form_param.price;
    const convertedMainPrice = convertLakhToNumeric(mainPrice);
    const mainPriceNum = extractPrice(convertedMainPrice);

    if (!mainPriceNum) return null;

    // Filter products with same unit as main product
    // Use "/piece" as default if no unit is present in main price
    let inputUnit = '';
    if (form_param.price) {
      const priceStr = form_param.price.toString();
      const parts = priceStr.split('/');
      if (parts.length > 1) {
        inputUnit = parts.pop()?.trim().toLowerCase();
      }
    }

    if (!inputUnit) {
      inputUnit = 'piece'; // Default to piece if no unit specified
    }

    let sameUnitProducts = allProducts;
    if (inputUnit) {
      sameUnitProducts = allProducts.filter(product => {
        if (!product.priceDisplay) return false;
        const parts = product.priceDisplay.split('/');
        const productUnit = parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';
        return productUnit === inputUnit;
      });
    }

    if (sameUnitProducts.length === 0) return null;

    // Calculate prices
    const prices = sameUnitProducts.map(product => extractPrice(product.priceDisplay)).filter(price => price > 0);
    prices.push(mainPriceNum); // Add main product price

    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);

    // Calculate positions on the gradient bar (0-100%)
    // 0% = left (green/lowest), 100% = right (red/highest)
    const calculatePosition = (price) => {
      if (highestPrice === lowestPrice) return 50; // If all prices are same, center it
      return ((price - lowestPrice) / (highestPrice - lowestPrice)) * 100;
    };

    return {
      currentPrice: mainPriceNum,
      lowestPrice,
      highestPrice,
      currentPosition: calculatePosition(mainPriceNum),
      lowestPosition: calculatePosition(lowestPrice),
      highestPosition: calculatePosition(highestPrice),
      sameUnitProductsCount: sameUnitProducts.length,
      unit: inputUnit // Add the unit for dynamic formatting
    };
  };

  const priceComparison = calculatePriceComparison();
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.replace('#', ''), 16);
    return {
      r: (bigint >> 16) & 255,
      g: (bigint >> 8) & 255,
      b: bigint & 255,
    };
  };

  const interpolateColor = (color1, color2, factor) => {
    const result = {
      r: Math.round(color1.r + (color2.r - color1.r) * factor),
      g: Math.round(color1.g + (color2.g - color1.g) * factor),
      b: Math.round(color1.b + (color2.b - color1.b) * factor),
    };
    return `rgb(${result.r}, ${result.g}, ${result.b})`;
  };

  const calculateDynamicColor = (position) => {
    if (!priceComparison) return '#007bff';

    const red = hexToRgb('#dc3545');  // High price color (red)
    const blue = hexToRgb('#007bff');
    const green = hexToRgb('#1b5e20');  // Low price color (green)

    const normalizedPosition = position / 100;

    if (normalizedPosition <= 0.5) {
      // Interpolate between green and blue (lowest on left)
      const factor = normalizedPosition / 0.5;
      return interpolateColor(green, blue, factor);
    } else {
      // Interpolate between blue and red (highest on right)
      const factor = (normalizedPosition - 0.5) / 0.5;
      return interpolateColor(blue, red, factor);
    }
  };



  const currentProductColor = priceComparison ? calculateDynamicColor(priceComparison.currentPosition) : '#007bff';

  return (
    <div className="price-widget">
      <div className="price-widget-header">
        <span>Get Best Price Upto
          {loading ? (<></>) : (<span> {(() => {
            // Use the same lowest price from priceComparison for consistency
            const lowestPrice = priceComparison ? priceComparison.lowestPrice : null;
            if (!lowestPrice) return 'N/A';

            // Get the dynamic unit from priceComparison
            const dynamicUnit = priceComparison ? priceComparison.unit : 'piece';

            // Format the lowest price for display with dynamic unit
            const formattedPrice = `₹ ${lowestPrice.toLocaleString('en-IN')} / ${dynamicUnit}`;

            const parts = formattedPrice.toString().split('/');
            const priceValue = parts[0]?.trim();
            const unit = parts.length > 1 ? parts[1]?.trim() : dynamicUnit;

            return (
              <>
                <span className="price-value">{priceValue}</span>
                {unit && <span className="widget-price-unit-header">/{unit}</span>}
              </>
            );
          })()}</span>)}</span>
      </div>
      <div className="price-widget-content">
        {loading ? (
          <div className={`loading-content ${startFadeOut ? 'fade-out' : ''}`}>
            <span className="loading-text">Unlocking best prices...</span>
          </div>
        ) : (
          <div className="widget-content">
            {lowestPricedProduct && (
              <>
                <div className="price-comparison-bar">
                  <div className="price-bar-container">
                    {/* Current Product Marker - Only one with dot and tooltip */}
                    <div
                      className="price-marker-dot current-product-dot"
                      style={{
                        left: `${priceComparison.currentPosition}%`,
                        backgroundColor: currentProductColor
                      }}
                    >
                      <div
                        className="price-tooltip always-visible"
                        style={{ color: currentProductColor }}
                      >
                        ₹{priceComparison?.currentPrice?.toLocaleString('en-IN')} - Current Price
                        <span
                          className="tooltip-arrow"
                          style={{ borderTopColor: currentProductColor }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="lowest-price-product">
                  {/* Lowest Price Product */}
                  <div className="product-details">
                    <div className="product-price">
                      {(() => {
                        // Use the same lowest price from priceComparison for consistency
                        const lowestPrice = priceComparison ? priceComparison.lowestPrice : null;
                        if (!lowestPrice) return 'N/A';

                        // Get the dynamic unit from priceComparison
                        const dynamicUnit = priceComparison ? priceComparison.unit : 'piece';

                        // Format the lowest price for display with dynamic unit
                        const formattedPrice = `₹ ${lowestPrice.toLocaleString('en-IN')} / ${dynamicUnit}`;

                        const parts = formattedPrice.toString().split('/');
                        const priceValue = parts[0]?.trim();
                        const unit = parts.length > 1 ? parts[1]?.trim() : dynamicUnit;

                        return (
                          <>
                            <span className="price-value">{priceValue}</span>
                            {unit && <span className="widget-price-unit">/{unit}</span>}
                          </>
                        );
                      })()}
                      {(() => {
                        const mainPrice = form_param.price;
                        const widgetPrice = lowestPricedProduct.priceDisplay;
                        const percentageLess = calculatePercentageLess(mainPrice, widgetPrice);

                        return percentageLess ? (
                          <div className="price-comparison">
                            <span className="percentage-less">
                              <span className="down-arrow">↓</span>
                              {percentageLess}%
                            </span>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  </div>
                  {/* Highest Price Product */}
                  <div className="product-price-container">
                    <div className="main-product-price">
                      {(() => {
                        // Use the same highest price from priceComparison for consistency
                        const highestPrice = priceComparison ? priceComparison.highestPrice : null;
                        if (!highestPrice) return 'N/A';

                        // Get the dynamic unit from priceComparison
                        const dynamicUnit = priceComparison ? priceComparison.unit : 'piece';

                        // Format the highest price for display with dynamic unit
                        const formattedPrice = `₹ ${highestPrice.toLocaleString('en-IN')} / ${dynamicUnit}`;

                        const parts = formattedPrice.toString().split('/');
                        const priceValue = parts[0]?.trim();
                        const unit = parts.length > 1 ? parts[1]?.trim() : dynamicUnit;

                        return (
                          <>
                            <span className="price-value-red">{priceValue}</span>
                            {unit && <span className="widget-price-unit">/{unit}</span>}
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Main component with secure conditional rendering
const PriceWidget = ({ form_param }) => {
  // Check if widget should be Formed based on conditions
  if (!shouldRenderPriceWidget(form_param)) {
    // Track widget not rendering due to conditions not met
    Eventtracking(`Price_Widget_Not_Formed`, `reason: conditions_not_met - m_id: ${form_param?.modId || form_param?.modid || 'undefined'} | c_n: ${form_param?.ctaName || 'undefined'} | p_f: ${form_param?.price_flag || 'undefined'} | i_f: ${form_param?.imageFlag || 'undefined'} | p_id: ${form_param?.pDispId || 'undefined'}`, form_param, true);
    return null; // Don't render widget if conditions not met
  }

  const { state } = useGlobalState();

  // Check if user is logged in
  const isLoggedIn = state.Imeshform && readCookieREC("ImeshVisitor");

  // Early return for non-authenticated users - no sensitive data in DOM
  if (!isLoggedIn) {
    return <LoginPromptWidget />;
  }

  // Only render authenticated content for logged-in users
  return <AuthenticatedPriceWidget form_param={form_param} />;
};

export default PriceWidget;
