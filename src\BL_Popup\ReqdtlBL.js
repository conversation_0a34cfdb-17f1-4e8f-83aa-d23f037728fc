import React, { useEffect, useRef, useState } from 'react';
import "./Blpopup.css";
import callPostreq from '../callpostreq';
import { useGlobalState } from '../context/store';
import { Eventtracking, getparamValREC, isset, readCookieREC,scriptTag ,isPresent} from '../common/formCommfun';
import { callSaveEnrichAPI } from '../main/callSaveEnrichAPI';

function ReqdtlBL({form_param ,blsearchval , seterr_msg}) {
    const { state, dispatch } = useGlobalState();
    const [requirementDetails, setRequirementDetails] = useState('');
    const [moreReqShow, setmoreReqShow] = useState(0);
    const [warning, setWarning] = useState('');
    const textareaRef = useRef(null);

    useEffect(() => {
        Eventtracking("DS" + window.screencount + "-RDBox", state.prevtrack, form_param, false);
        dispatch({ type: 'currentscreen', payload: { currentscreen: "RDBox" } });
        if (textareaRef.current) {
            textareaRef.current.focus();
        }
        const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
        let mdtlres = null;
        try{
            mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
        }
        catch(e){
            mdtlres=null;
        }
        if(mdtlres && mdtlres[getparamValREC(imesh, "glid")] && mdtlres[getparamValREC(imesh, "glid")].Response && mdtlres[getparamValREC(imesh, "glid")].Response.Data){
            let gliddd = getparamValREC(imesh, "glid");
            let moreReqShw = isPresent(mdtlres[gliddd].Response.Data[1]) && isPresent(mdtlres[getparamValREC(imesh, "glid")].Response.Data[2]) && isPresent(mdtlres[gliddd].Response.Data[0]) ? 1: 0 ;
            setmoreReqShow(moreReqShw);
        }
    }, []);
    const handleRequirementDetailsChange = (e) => {
        setRequirementDetails(e.target.value);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if(form_param.formType == "BL" && blsearchval == "" && !state.openinlnBLPopup){
            seterr_msg("Please enter your requirement");
            return;
        }
        let value  = requirementDetails;
        let isInvalid = scriptTag(value); // Allows empty string
        setWarning(isInvalid ? 'Please do not use special symbols' : '');
        if(!isInvalid){
            if (state.postreq == 0) {
                let qid = await callPostreq(form_param);
                if (qid !== '') {
                    dispatch({ type: 'postreq', payload: { postreq: qid } });
                }
            }
            if(value == ''){
                Eventtracking(`SS${window.screencount}-RDBox-empty`,state.prevtrack,form_param,false);
            }else{
                Eventtracking(`SS${window.screencount}-RDBox-filled`,state.prevtrack,form_param,false);
                callSaveEnrichAPI(requirementDetails, state.postreq, '' , form_param);
            }
            dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-RDBox` } });
            dispatch({ type: "frscr", payload: { frscr: 2 } });
            dispatch({ type: 'RDformBL', payload: { RDformBL: false } });
            if(moreReqShow != 1)
                dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: true } });
            else
                dispatch({ type: 'thankyou', payload: { thankyou: true } });
            window.screencount++;
        }
       
    }

    return (
        <div className="screen4">
            {(state.frscr !=1) && <div className="ber-hdg-r">
                <div className="otphdg">Few more details to get your requirement fulfilled</div>
                <div className="blotpbl">Share a few more information and get best quotes</div>
            </div>}
            {<div className="reqbox bepdpreq bemt15">
                <div>
                    <label className="ber-lbl">Requirement Details</label>
                </div>
                <div className="betarea" data-role="">
                    <textarea
                        ref={textareaRef}
                        className={`slbox ber-txt ${warning ? 'highErr' : ''}`}
                        placeholder=""
                        value={requirementDetails}
                        onChange={handleRequirementDetailsChange}
                        onClick={()=>{setWarning('')}}
                    ></textarea>
                    {warning && <div className="errorRD">{warning}</div>}
                </div>
            </div>}
            <div className="subBut">
                <input value="Next" className="form-btn" onClick={handleSubmit} type="submit" style={{ backgroundColor: '#00a699' }} />
            </div>
        </div>
    );
}

export default ReqdtlBL;
