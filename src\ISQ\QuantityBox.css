/* QuantitySelector.css */

#tqut_id {
    display: flex;
    align-items: center; /* Center-align items vertically */
 /* Spacing between flex items */
}

.warning {
    color: red;
    margin-right: 10px;
}

#ttxtbx_option1,
#ttxtbx_option2,#unitOther{
    height: 34px;
    border: 1px solid;
    text-align: left;
    padding: 5px 10px;
    border-color: #c9c6c6;
}
#ttxtbx_option2{
    width: 135px;
    border-radius: 0px 20px 20px 0px ;
    border-left: none;
    background-color: #0aaa9d;
    color: white;
}
.inlUnit{
    width: 105px !important;
}
#ttxtbx_option1,#unitOther{
    /* border-right: none; */
    border-radius: 8px;
    width: 95px;
    
}
#unitOther{
    width: 90px;
}
#ttxtbx_option1:focus, #ttxtbx_option2:focus{
    outline: none;
    border-color: #029f93!important;
}

#tqt_display {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* Spacing between radio buttons */
    list-style: none;
    margin: 0px 0px 0px 10px;
    padding: 0px;
}

.radio-item {
    padding: 7px 21px;
    text-align: center;
    list-style: none;
    cursor: pointer;
    position: relative;
    border: 1px solid rgb(250, 250, 250);
    border-radius: 8px;
    transition: background-color 0.3s ease;
    background-color: #E4EFEE;
}
.radio-item:hover,.radio-item.selUnit,#unitOther:hover,#unitOther:focus,input.selOther {
    border: 1px solid #007A6E!important;
    color: #007A6E !important;
}

#unitOther{
    background-color: #E4EFEE;
}
.radio-item::before {
    content: '';
    display: inline-block;
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 4px;
    left: 5px;
    top:9px;
    border: 2px solid #b1b1b1;
}
.radio-item.selUnit::before {
    background-color: #007A6E;
    border-color: #007A6E;
}
.qt_lbl {
    font-size: 15px;
    color: #333;
    margin: 15px 0 10px 0;
    display: block;
}
.inEqlRec .qt_lbl{
    color: #111;
}
.inlQT{
    display: flex;
    margin-bottom: 15px;
    align-items: center;
}