import React, { useEffect, useState } from 'react';
import { useGlobalState } from "../context/store";
import mcatdtl from "./McatDTLServ";
import { isset,isInactBL,isInlineBl, isBlRevampd } from '../common/formCommfun';
import Head_scr from '../common/heading';
import PhEmError from '../Login/PhEmError';
import getMcatDetail from '../inlinebl/McatNameSugg';

function Bl_ProdSrch({form_param,layout,name,setblsearchval,err_msg,seterr_msg,headProd,setHeadProd,fetchAdvSearchData}) {
    const { state, dispatch } = useGlobalState();
    const [inputValue, setInputValue] = useState((form_param.prodDispName || form_param.prodName || ''));
    let id =form_param.tempId+form_param.instId;
    useEffect(() => {
        dispatch({ type: 'searchshown', payload: { searchshown:  true} });
    },[])
    useEffect(() => {
        setblsearchval && setblsearchval(inputValue);
        if(inputValue && err_msg && err_msg == "Please enter your requirement"){
            seterr_msg("");
        }
    },[inputValue])
    // Handler to update input value state
    const handleInputChange = (e) => {
        setInputValue(e.target.value);
        var pr_sugg = new Suggester({
            element:`prodtitle${id}`,
            getQuotation: false,
            onSelect: suggestor_fn,
            fields: "",
            type: "product",
            module: "BL-FORM",
            rowsToDisplay: 5,
            recentData: 1,
            autocompleteClass: `be-sugg t${id}_prodName`,
        });
    };
    async function McatDetail (mcatMatch) {  
        await getMcatDetail(mcatMatch,form_param);
        form_param.prodName = mcatMatch;
        if(layout=='idsf'){
            window.inlineparambl= form_param;
        }
        mcatdtlresponse();
    }
    const mcatdtlresponse = async () => {
        const resp = await mcatdtl(form_param);
        if(state.frscr==1 && state.RDformBL){
            dispatch({ type: 'IsqformBL', payload: { IsqformBL: true } });
        }
        dispatch({ type: 'openBLPopup', payload: { openBLPopup:  resp} });
    };
    const suggestor_fn = (e,ui) => {
        setInputValue(e.target.value);
        if(setHeadProd){
            setHeadProd(e.target.value)
        }
        if(form_param.prodName != e.target.value){
            if (isset(()=>ui) && isset(()=>ui.item)) {
                if (ui.item.value) {
                    setInputValue(ui.item.value);
                    form_param.prodName = ui.item.value;
                    form_param.blMultiImage=[];
                    form_param.prodDispName = "";
                    form_param.mcatName = "";
                }
                if (isset(()=>ui.item.mcat_id) && isset(()=>ui.item.mcat_id[0]) && isset(()=>ui.item.cat_id) && isset(()=>ui.item.cat_id[0])) {
                    form_param.mcatId = ui.item.mcat_id[0];
                    form_param.catId = ui.item.cat_id[0];
                    if(layout=='idsf'){
                        window.inlineparambl= form_param;
                    }
                    mcatdtlresponse();
                }
                else{
                    McatDetail(ui.item.value);
                }
            }else{
                McatDetail(e.target.value);
            }
            if(fetchAdvSearchData){
            fetchAdvSearchData(form_param)

            }

        }
};
let inactivecls = '';
if(form_param.ctaName == "Inactive"){
    inactivecls = 'incatinput';
}
let err = err_msg && err_msg == "Please enter your requirement" ? "ered" : "";
    return (
        <>  {!isInactBL(form_param) && !isInlineBl(form_param) ? <Head_scr hash={form_param} scr={'BlLogin'}/>  : ''}
        {!isInactBL(form_param) && !isInlineBl(form_param) ? isBlRevampd(form_param) ? '' : <div className="befs16 bemb20 blMarfn">Tell us your requirement and get free quotes from multiple sellers</div>  : ''}
           
            <div id="t0901_prodtitle" className={`porlt ${layout}`}>
                {err_msg && err_msg == "Please enter your requirement" && <PhEmError form_param={form_param} id={id} err_msg={"Enter product/service name"} />}
                <label id="t0901_name-l" className="prodsrchtitl">{name}
                <span className="redc">*</span>
                </label>
                <input
                    className={`${inactivecls} be-slbox inpt_errorbx be-row ui-autocomplete-input ${err}`}
                    type="text"
                    name="q_title"
                    id={`prodtitle${id}`}
                    // onBlur=""
                    maxLength="100"
                    placeholder="Enter Product / Service name"
                    autoComplete="off"
                    spellCheck="true"
                    role="textbox"
                    aria-autocomplete="list"
                    aria-haspopup="true"
                    value={inputValue} // Bind input value from state
                    onChange={handleInputChange} // Handle input change
                />
            </div>
        </>
    );
}

export default Bl_ProdSrch;
