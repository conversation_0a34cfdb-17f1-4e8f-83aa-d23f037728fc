.mb15 {
    margin-bottom: 15px;
}
.jcc{
    justify-content: center;
}
.contBLin {
    background: #fff;
    border: 1px solid #d3d3d3;
    position: relative;
    /* min-height: 180px; */
}
.inlinemsghead{
    font-size: 15px;
}
.ml12{
    margin-left: 12px;
    margin-right: 5px;
}
.arrow-right {
    border: solid #fff;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 4px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
  }
.inlinemsgcompnm{
    color: #000;
    font-weight: 600;
    text-decoration: underline;
}
.inlinemsgprodnm{
    color: #000;
    font-weight: 700;
}
.ber-mdl{
    margin-top: 17px;
}
.bgin15 {
    background: #241f55;
    font-size: 18px;
    color: #fff;
    padding: 8px 10px;
    border: 1px solid #242056;
}
.ber-pdg {
    padding: 10px;
    display: flex;
}

.bxs, .contlk, .pdin, .wful, .wid1 {
    box-sizing: border-box;
}
.mt5 {
    margin-top: 5px;
}
.mb8 {
    margin-bottom: 8px;
}
.dtbl {
    display: table;
}
.pbdrb {
    border-bottom: 1px solid #eaeaea;
}
.fs12 {
    font-size: 12px;
}

.frmimg {
    width: 70px;
    height: 80px;
    bottom: 52px;
    left: 10px;
    position: absolute;
}
.belft {
    float: left;
}
#obj_fit {
    width: 90px;
    height: 90px;
}
.frmimg img {
    max-width: 60px;
    max-height: 60px;
}

.inEqlRec label {
    width: 160px;
    flex-shrink: 0;
    padding: 12px 5px 0 0;
  }

  .inEqlRec input,
.inEqlRec textarea,
.inEqlRec select {
  display: block;
  width: 100%;
  padding: 5px 7px;
  font-size: 15px;
  height: 42px;
  color: #223540;
  background-color: #fff;
  border: 1px solid #cbcbcb;
  border-radius: 3px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.inlinebtn {
    background: #2e3192;
    border-radius: 2px;
    color: #fff;
    font: 600 16px / 1.3 Arial, Helvetica, sans-serif;
    width: 270px;
    border: none;
    height: 35px;
    padding: 8px 0px;
    margin: auto;
    cursor: pointer;
}
.inlinebtn:hover {
    /* border: 1px solid #2e3192; */
    box-shadow: 0 0 1px 0 #ddd, 0 1px 0 1px #2e3192;
}
.inEqlRec textarea {
    height: 70px;
    resize: none;
  }
  .inEqlRec .inlSbtn {
    background-color: #00a69a;
    color: #fff;
    height: 42px;
    border: none;
    width: 282px;
    font-weight: 700;
  }
  .inEqlRec .inlSbtn:hover {
    border: 1px solid #007a6e;
    box-shadow: 0 0 1px 0 #ddd, 0 1px 0 1px #00423d;
    width: 282px;
  }
  .name-input-inlineenq{
    display: block;
    width: 100%;
    padding: 5px 7px;
    font-size: 15px;
    height: 42px;
    color: #223540;
    background-color: #fff;
    border: 1px solid #cbcbcb;
    border-radius: 3px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  }
    .name-input-inlineenq:focus{
        border: 1px solid #2e3192;
    }
  .inline-enq-login{
    margin: 10px 0px;
  }
