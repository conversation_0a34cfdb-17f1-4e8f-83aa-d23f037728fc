import React from 'react';
import WidgetRatings from './WidgetRatings';
import ResponseRate from './ResponseRate';
import MembersSince from './MembersSince';
import GstShow from './GstShow';
import SupplierType from './SupplierType';
import { isset, formatINRPrices, reqFormGATrackREC } from '../common/formCommfun';
const AdvSearchUnit = ({ form_param, searchAPIdata, prodNm }) => {
    return (
        <>

            <div id="plawid" className="idsf pdv20adv ebgF3 eptb10 eqmt5">
                <div>
                    <div className="eqs16 befwt beclr3 BL_Fm8">More Sellers For {prodNm}</div>
                    <ul className="idsf eqEcmpF bemt5 plaADV">
                        {searchAPIdata.map((elem, index) => {
                            let { large_image, image, price_f, desktop_title_url, title, catalog_url, companyname, city, itemprice, moq_type, supplier_rating, rating_count, CustTypeWt, tscode, pns_success_ratio, gstVerifiedFlag, memberSinceDisplay, freeSupplierVerifiedFlag, isverifiedexporter } = elem.fields;
                            const img = isset(() => large_image) ? (large_image).replace("http://", "https://") : (isset(() => image) ? (image).replace("http://", "https://") : "");
                            let pdpUrl = desktop_title_url && desktop_title_url.split("?");
                            pdpUrl = pdpUrl && pdpUrl[0] ? pdpUrl[0] : '';
                            const name = isset(() => title) && title.includes('"') ? title.replace(/"/g, "") : title;
                            let icon = <i></i>;
                            let dispType = '';
                            if (catalog_url) {
                                if (isverifiedexporter == 1) {
                                    icon = <i className="sellericons oef0 veSlr" width="14" height="14"></i>;
                                    dispType = 'Verified Exporter';
                                } //verified exporter
                                else if (CustTypeWt == 149 || CustTypeWt == 179 || tscode) {
                                    icon = <i className="sellericons oef0 tvfSlr" width="14" height="14"></i>;
                                    dispType = 'TrustSEAL Verified';
                                } //trustseal
                                else if ((CustTypeWt <= 699)) {
                                    icon = <i className="sellericons oef0 vpsSlr" width="14" height="14"></i>
                                    dispType = 'Verified Plus Supplier';
                                } //verified plus
                                else if ((CustTypeWt > 699) && (CustTypeWt <= 1899) && freeSupplierVerifiedFlag == 1) {
                                    icon = <i className="sellericons oef0 vsSlr" width="14" height="14"></i>;
                                    dispType = 'Verified Supplier';
                                } //verified
                                else { icon = <i></i> }
                            }
                            let showgst = (gstVerifiedFlag === '1' || gstVerifiedFlag === '0') ? 1 : 0;

                            return (
                                <li key={index}>
                                    <div>
                                        <div className="txtElp txtElp1 pdpadv bemrg2">
                                            <a href={`${pdpUrl}?ecom`} target="_blank" onClick={() => { reqFormGATrackREC("TY_advance_prodname", form_param) }} className="eComtxt befs14 eplh16">{name}</a>
                                        </div>
                                        <div className="idsf advidsf" >
                                            <div className="ecmpImgadv fs0">
                                                <a href={`${pdpUrl}?ecom`} target="_blank" onClick={() => { reqFormGATrackREC("TY_advance_image", form_param) }} className="idsf pJc id_aic">
                                                    <img alt={name} src={img} />
                                                </a>
                                            </div>
                                            <div className="eplf6 bemt5 eplh16 easFd asFS">
                                                <div className="txtElp txtElp1 befs14 befwt beclr3 bemrg2"> {itemprice ?
                                                    <p className="fs15 fw formatINRPrice">&#8377;
                                                        <strong >{itemprice ? formatINRPrices(itemprice) : ''}</strong>
                                                        {moq_type ? <> / {moq_type}</> : ""}
                                                    </p> : 
                                                    (price_f && price_f.split(";") && price_f.split(";")[1] && price_f.split(";")[1].trim() ? <p className="fs15 fw">&#8377;<strong>{price_f.split(";")[1].trim().replace("/", " / ")}</strong></p> : "")}
                                                </div>
                                                <a href={`${catalog_url}?ecom`} target="_blank" onClick={() => { reqFormGATrackREC("TY_advance_sellerCard", form_param) }} className="cityadv inlBlock">
                                                    {companyname ? <div className="txtElp txtElp1 bemrg2 cnmAdv">
                                                        <div className="befs14">
                                                            <span className="cityadv txtElp">{companyname}</span></div>
                                                    </div> : <div></div>}  </a>
                                                {city ? <div className="txtElp txtElp1 bemrg2 clrgry" style={{ display: 'flex' }}>
                                                    <i className="sellericons oef0 cityPoint" width="14" height="14"></i>
                                                    <span style={{ lineHeight: '16px' }}>{city}</span>
                                                </div> : <div></div>}
                                                {dispType || showgst ? <div className="txtElp txtElp1 bemrg2 cnmAdv">
                                                    <div className="befs13 idsf mt5 flxwrp id_aic">
                                                        {showgst ? <GstShow gstVerifiedFlag={gstVerifiedFlag} /> : ''}
                                                        {dispType ? <SupplierType dispType={dispType} icon={icon} /> : ''}
                                                    </div>
                                                </div> : <div></div>}
                                                {memberSinceDisplay || supplier_rating ?
                                                    <div className="befs13 idsf mt5 flxwrp">
                                                        {memberSinceDisplay ? <MembersSince memberSinceDisplay={memberSinceDisplay} /> : ''}
                                                        {supplier_rating && rating_count ? <WidgetRatings rating_count={rating_count} supplier_rating={supplier_rating} /> : <div></div>}
                                                    </div> : ''}
                                                {pns_success_ratio ? <ResponseRate pns_success_ratio={pns_success_ratio} /> : <div></div>}
                                            </div></div>
                                        <a href={`${pdpUrl}?ecom`} className="emBynw adv" onClick={() => { reqFormGATrackREC("TY_advance_VMD", form_param) }} target="_blank">
                                            {'View More Details'}
                                        </a>

                                    </div>
                                </li>
                            );
                        })}
                    </ul>
                </div>
            </div>


        </>


    );
};

export default AdvSearchUnit;