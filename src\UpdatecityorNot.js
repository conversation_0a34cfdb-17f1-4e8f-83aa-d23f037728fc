import { readCookieREC, getparamValREC, isset } from './common/formCommfun';
export default async function UpdatecityorNot (form_param,glusr,latlong,behavct,glb,ipct){
    let webAddressLocation = location.hostname;
    let baseUrl = "https://apps.imimg.com/index.php";
    if(webAddressLocation.match(/^dev/)){
        baseUrl = "https://dev-apps.imimg.com/index.php";
    }
    let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const queryParams = new URLSearchParams({
        r:'UpdateCity/UpdateorNot',
         glid: getparamValREC(imeshcookie, 'glid') || '',
         glusr: glusr || '',
         latlong: latlong || '',
         behavct: behavct || '',
         glb: glb || '',
         ipct: ipct || '',
        _: new Date().getTime()
    });
    const url = `${baseUrl}?${queryParams.toString()}`;
    try {
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        let res = await response.json();
        // console.log(res);
        if(typeof(res)!="undefined" && res ){
            if(res.update == true && res.from_city && res.to_city){
                return { 
                    update: res.update, 
                    from_city: res.from_city,
                    to_city: res.to_city
                }
            }
            else{
                return { 
                    update:false, 
                    from_city: '',
                    to_city: ''
                }
            }
        }
    } catch (error) {
        console.error('Failed to call API:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'miniDtl','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
         return 'nocity';
    }
};