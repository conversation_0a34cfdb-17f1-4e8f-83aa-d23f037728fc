import { useEffect } from "react";

const IsqCitySuggester = ({ elementId, onSelect }) => {
  useEffect(() => {
    if (!elementId || !onSelect) return;

    const isq_city_sugg = new Suggester({
      element: elementId,
      onSelect: onSelect,
      type: "location",
      placeholder: "",
      autocompleteClass: "ber-sugg",
      displayFields: "value,state",
      minStringLengthToDisplaySuggestion: 1,
      displaySeparator: " >> ",
      fields: "state,stateid,flname,alias,location_id",
      filters: "typ:1",
      recentData: "false"
    });

    return () => {
    };
  }, [elementId, onSelect]);

  return null; 
};

export default IsqCitySuggester;
