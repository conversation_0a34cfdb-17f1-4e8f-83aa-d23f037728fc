import React from 'react';
import { isset, getAddressREC ,reqFormGATrackREC} from '../common/formCommfun';
import './imgEnq.css';

const ProdsImgRecom = ({ form_param, prodsData }) => {
  if (!isset(() => prodsData) || prodsData.length <= 1) {
    return null;
  }

  const text = "View Similar Products";
  const proclass = "ProdList-Item-Name";
  let pdpabex = "enqformpdp=1";
  const teststr = form_param.section;

  if (/\|EUI1/.test(teststr)) pdpabex = "enqformpdp=2";
  if (/\|CUI/.test(teststr)) pdpabex = "enqformpdp=3";

  const param = pdpabex;

  return (
    <div id="recommendProdImg" className="VSP-SECI" style={{ display: 'block' }}>
      <div className="vs-heading">
        <h3>{text}</h3>
      </div>
      <ul id="prodList" className="ProBoxULI">
        {prodsData.slice(0, 6).map((item, index) => {
          let address = '';

          if (isset(() => form_param.rcvCity) && form_param.rcvCity !== '') {
            address = "Deals In " + form_param.rcvCity;
            if (isset(() => item.CITY_NAME) && item.CITY_NAME !== '' && item.CITY_NAME === form_param.rcvCity) {
              const locality = isset(() => item.SDA_GLUSR_USR_LOCALITY) ? item.SDA_GLUSR_USR_LOCALITY : "";
              const state = isset(() => item.STATE_NAME) ? item.STATE_NAME : "";
              address = getAddressREC(locality, item.CITY_NAME, state);
            }
          }

          const itemUrl = isset(() => item.PDP_URL) && item.PDP_URL !== "" ?
            item.PDP_URL.endsWith(".html") ? `${item.PDP_URL}?${param}` : `${item.PDP_URL}&${param}` : "''";

          return (
            <li key={index} className="berds10 ibgc" id={`recomProd${index + 1}`}>
              <a target="_blank" href={itemUrl} className="ProBox-Item disp-inl" onClick={() =>{reqFormGATrackREC(`Prod_${index + 1}`,form_param)}}>
                <div className="Proimg">
                  <img src={item.IMAGE_250X250} alt={item.ITEM_NAME} />
                </div>
                <p className={`${proclass} color3 atxu cbl_fs16 befwt`}>{item.ITEM_NAME}</p>
                {address && <p className="addr-city ht34">{address}</p>}
                <p className="proPrice">{isset(() => item.PRICE_F) && item.PRICE_F !== "" ? item.PRICE_F : "Ask Price"}</p>
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default ProdsImgRecom;
