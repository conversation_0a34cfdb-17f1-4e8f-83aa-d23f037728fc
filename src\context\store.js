import React, { createContext, useReducer, useContext, useEffect } from 'react';
import { reducer } from './reducer';

let initialState = {};
const store = createContext(initialState);
const { Provider } = store;
const StateProvider = (props) => {
    initialState = props.serviceData;
    const [state, dispatch] = useReducer(reducer, initialState);

    return (
        <Provider value={{ state, dispatch }}>
            {props.children}
        </Provider>
    );
};

const useGlobalState = () => {
    const {state, dispatch} = useContext(store);
    return {state, dispatch};
  }

export { store, StateProvider, useGlobalState }