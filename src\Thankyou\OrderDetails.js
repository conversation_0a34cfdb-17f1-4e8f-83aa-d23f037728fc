import React from 'react';

const OrderDetails = ({ data }) => {
    return (
        <div className="order-details zz zg shd">
            <span className="headkey"><strong>Requirement Summary:</strong></span>

            <span className="order-detail-text clmp">
                {Object.entries(data).map(([key, value], index) => (
                   value && value.length>0 && ( <span className="order-detail-item" key={key}>
                        <span className="order-detail-key"><strong>{key}:</strong></span>
                        <span className="order-detail-value">{value}</span>
                        {/* Add a space between items except for the last one */}
                        {index < Object.entries(data).length - 1 && <span>  </span>}
                    </span>)
                ))}
            </span>
        </div>
    );
};

export default OrderDetails;
