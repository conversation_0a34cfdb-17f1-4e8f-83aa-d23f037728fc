import React, { useEffect, useState } from "react";
import Thankyoudiv from "./Thankyoudiv";
import Thankyoureg from "./Thankyoureg";
import ThankyouregOld from "./ThankyouregOld";
import ThankyouBusiness from "./ThankyouBusiness";
import './thankyou.css';
import { useGlobalState } from '../context/store';
import { currentISO, Eventtracking,newTYform,readCookieREC,getparamValREC,isset, isPresent } from '../common/formCommfun';
import FinishEnqService from '../main/FinishEnqService';
import TYsearchAd from "./TYsearchAd";
import ThankuAdUnit from "./ThankuAdUnit";
// import OrderDetails from "./OrderDetails";

function Thankyoumain({ form_param, close_fun }) {
    const { state, dispatch } = useGlobalState();
    // const [modifiedData, setModifiedData] = useState({}); // Initialize modifiedData as state
     const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    let utype = getparamValREC(imesh, 'utyp') || state.UserData.utyp;

        let mdtlres = null;
        let cname = "";
        let gliddd = getparamValREC(imesh, "glid") ;
        try {
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
        }
        catch (e) {
        mdtlres = null;
        }
        if (mdtlres && mdtlres[gliddd] && mdtlres[gliddd].Response && mdtlres[gliddd].Response.Data) {
        cname = isPresent(mdtlres[gliddd].Response.Data[1]) ? mdtlres[gliddd].Response.Data[1] : "";
        } 

    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                close_fun();
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [close_fun]);

    useEffect(() => {
        if (form_param.formType === 'Enq') {
            FinishEnqService(state.postreq, form_param , window.rfq_queryDestinationRec);
            // if(state.localenqparam && state.postreqenqlocal){
            //     FinishEnqService(state.postreqenqlocal, state.localenqparam , window.rfqDlocal);
            // }
        }
    }, [form_param, state.postreq]); // Add dependencies for FinishEnqService

    const handleClose = () => {
        dispatch({ type: 'thankyou', payload: { thankyou: false } });
        close_fun();
    };

    useEffect(() => {
        Eventtracking("DS" + window.screencount + "-ThankYou", state.prevtrack, form_param, false);
        dispatch({ type: 'currentscreen', payload: { currentscreen: "ThankYou" } });
    }, []);

    // useEffect(() => {
    //     const mcatid = form_param.mcatId;
    //     let userFilledData = [];
    //     try {
    //         userFilledData = JSON.parse(sessionStorage.getItem("userFilledData")) || {};
    //     } catch (e) {
    //         userFilledData = [];
    //     }

    //     if (isset(() => userFilledData) && isset(() => userFilledData[mcatid])) {
    //         const originalData = userFilledData[mcatid];
    //         const newModifiedData = {}; // Create a new object for modified data

    //         for (let key in originalData) {
    //             newModifiedData[key] = originalData[key]; // Copy the original data

    //             if (newModifiedData["Quantity"] && newModifiedData["Quantity Unit"]) {
    //                 newModifiedData["Quantity"] = `${newModifiedData["Quantity"]} ${newModifiedData["Quantity Unit"]}`;
    //                 delete newModifiedData["Quantity Unit"];
    //             }
    //         }

    //         setModifiedData(newModifiedData); // Update state with modified data
    //         console.log(newModifiedData);
    //     }
    // }, [form_param]); // Ensure this effect runs when form_param changes

    return (
        <>
        {newTYform() ? <div id="t0901_thankDiv" className="ber-mcont bezid oEq_r futhkCss frmscroll thAutoM BL_ThnkuRR">
            <div className="ths_w">
                <div className="ber-cls-rec cp" id="t0901_cls" onClick={handleClose}>X</div>
                <Thankyoudiv form_param={form_param} />
                {/* {Object.keys(modifiedData).length > 0 ? <OrderDetails data={modifiedData} /> : null} */}
                <Thankyoureg form_param={form_param} />                
                <TYsearchAd form_param={form_param} />
                {(state.UserData && state.UserData.iso ? state.UserData.iso : currentISO()) == 'IN' ? <ThankyouBusiness form_param={form_param} /> : null}
                {/* <ThankuAdUnit form_param={form_param} /> */}
            </div>
        </div> : 
         <div id="t0901_thankDiv" className="ber-mcont bezid oEq_r futhkCss frmscroll thAutoM BL_Thnku">
         <div className="ths_w">
             <div className="ber-cls-rec cp" id="t0901_cls" onClick={handleClose}>X</div>
             <Thankyoudiv form_param={form_param} />
             <ThankyouregOld form_param={form_param} />
             {((state.UserData && state.UserData.iso ? state.UserData.iso : currentISO()) == 'IN') && utype!='P' && utype!='F' && cname ? <ThankyouBusiness form_param={form_param} /> : null}
             {/* <ThankuAdUnit form_param={form_param} /> */}
             <TYsearchAd form_param={form_param} />
             
             
         </div>
     </div>
}
        </>
        
    );
}

export default Thankyoumain;
