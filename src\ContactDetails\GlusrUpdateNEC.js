import {getparamValRE<PERSON>, IsCookieHaveValue, readCookieRE<PERSON>, reqFormGATrackREC, updateimeshCombined} from '../common/formCommfun.js'
import SessionDetailApi from '../Login/SessionDeatilApi.js';
export default async function GlusrUpdateNEC(parmObj,form_param) {
    const appsServerName = location.hostname.match(/^dev/) ? "//dev-apps.imimg.com/" : location.hostname.match(/^stg/) ? "//stg-apps.imimg.com/" : "//apps.imimg.com/";
    try {

        const queryParams = new URLSearchParams(parmObj);
        
        const response = await fetch(`${appsServerName}index.php?r=Newreqform/GlusrUpdate&${queryParams}`);
        
        if (!response.ok) {
            reqFormGATrackREC("service:GlusrUpdate:failure" ,form_param);
            throw new Error('Network response was not ok');
        }
        
        let data = await response.json();
        reqFormGATrackREC("service:GlusrUpdate:success" ,form_param);
        const imesh = readCookieREC('ImeshVisitor');
        if(IsCookieHaveValue() && data && (data.fname || data.lname || data.mobile || data.user_email)){
            if(typeof maskimesh === "function") maskimesh({"fn" : data.fname, "ln": data.lname, "mb1" : data.mobile, "em" : data.user_email});
        }
        if(imesh && getparamValREC(imesh, 'sessionKey')&& data && data.msg && data.msg.MESSAGE && data.msg.MESSAGE.CODE == 200){
            data = await SessionDetailApi(getparamValREC(imesh, 'sessionKey'),form_param.modId);    
        }
        else{
            data.DataCookie=updateimeshCombined(imesh,false);
        }
        if (typeof getLoginStringv1 === "function") getLoginStringv1();
        return data;
    } catch (error) {
        // Handle errors
        reqFormGATrackREC("service:GlusrUpdate:failure",form_param);
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'GlusrUpdate_failed','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}