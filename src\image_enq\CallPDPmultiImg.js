
import { isset } from '../common/formCommfun';
import PDPMultiImageAPI from './PDPMultiImageAPI';


export default async function CallPDPmultiImg(form_param) {
    window.pdpMultires = '';

    const res = await PDPMultiImageAPI(form_param);

    if (
        isset(() => res) &&
        isset(() => res["Status"]) &&
        res["Status"] === 200 &&
        isset(() => res["Data"]) &&
        res["Data"].length > 0 &&
        isset(() => res["Data"][0]) &&
        isset(() => res["Data"][0]["ITEM_IMG"]) &&
        res["Data"][0]["ITEM_IMG"].length > 0
    ) {
        const dataArray = res["Data"][0]["ITEM_IMG"].map(item => {
            let displayImage = "";
            if (item.IMAGE_1000x1000) {
                displayImage = item.IMAGE_1000x1000;
            } else if (item.IMAGE_500x500) {
                displayImage = item.IMAGE_500x500;
            } else if (item.IMAGE_250x250) {
                displayImage = item.IMAGE_250x250;
            } else {
                displayImage = item.IMAGE_125x125;
            }

            return {
                displayImage: displayImage,
                type: "image",
                vidUrl: "",
                zoomImage: displayImage,
            };
        });
        let imageUrl = isset(() => form_param.displayImage) && form_param.displayImage.includes("1000x1000") ?
            form_param.displayImage :
            isset(() => form_param.origImage) && form_param.origImage!='' ?
                form_param.origImage :
                isset(() => form_param.displayImage) ?
                    form_param.displayImage : "";
        dataArray.unshift({
            displayImage: imageUrl,
            type: "image",
            vidUrl: "",
            zoomImage: form_param.zoomImage,
        });
        window.pdpMultires = dataArray;
        return dataArray;


    }
}
