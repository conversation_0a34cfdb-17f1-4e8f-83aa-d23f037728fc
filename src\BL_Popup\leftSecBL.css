.ber-pnmS {
    padding: 0 0px 10px 0px;
    line-height: 20px;
}
.ber-prdimg {
    height: 250px;
    width: 100%;
    margin: auto;
    z-index: 1;
    overflow: hidden;
    position: relative;
}
.mtPrdimg{
    margin-top: 10px !important;
    border-radius: 8px;
}
.imgscroll.ber-mcontbl {
    max-height: 97.5vh!important;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .imgscroll .ber-lbl{
    color: #05796f !important;
  }

  .inactHead{
    margin-bottom: 8px;
    font-size: 16px;
    color: #333;

  }

  .inactFrH{
    font-size: 16px;
    text-align: center;
    margin-top: 6px;
  }
.ber-prdimg img {
    max-width: 100%;
    max-height: 100%;
    position: absolute;
    left: 0px;
    right: 0px;
    margin: auto;
    top: 0px;
    bottom: 0px;
    z-index: 3;
}
.ber-blrprdimg {
    display: block;
    position: absolute;
    z-index: -1;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.ber-help {
    margin: 0px auto;
    padding-top: 15px;
    font-size: 14px;
}
.beclrW {
    color: #fff;
    font-size: 20px;
    margin-bottom: 15px !important;
    text-align: center;
}
.inactBeclrW.beclrW {
    color: black;
    font-size: 18px;
    margin-bottom: 6px !important;
    font-weight: bold;
    text-align: center;
}
.bepr {
    position: relative;
    line-height: 18px;
}
.ber-hlpd {
    height: 110px;
    width: 2px;
    border-left: 2px dotted #fff;
    position: absolute;
    left: 10px;
    top: 0px;
    float: left;
}
.behlp1 {
    display: table;
    margin-bottom: 15px !important;
    vertical-align: top; 
}
.inBlsec{
        display: table;
        margin-bottom: 4px !important;
        vertical-align: top; 

}
.bedtc-r{
    display: table-cell;
}
.bedotW {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    margin-right: 8px;
    display: inline-block;
}
.bevtCss {
    vertical-align: top;
    display: table-cell;
}
.oef0 {
    flex-shrink: 0;
}
.ber-LscBL{
    min-height: 500px;
    width: 260px;
    background: #4458a7;
    color: #fff;
    padding: 10px;
    vertical-align: top;
    position: relative;
}
.incatinput{
    border-radius: 8px;
    margin: 0px 0 16px 0;
    height: 34px;
    padding: 0 10px;
}
