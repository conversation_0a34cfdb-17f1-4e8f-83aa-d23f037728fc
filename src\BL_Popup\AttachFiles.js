import React, { useState } from 'react';
import "./AttachFiles.css"; // Make sure to create this CSS file for styling
import { getparamValREC, isset, readCookieREC, safeDecodeURIComponent } from '../common/formCommfun';

function AttachFiles({ attachedFiles, setAttachedFiles }) {
    const handleFileChange = (event) => {
        if (event.target.files) {
            const newFiles = Array.from(event.target.files);
            newFiles.forEach(file => {
                const validatedFile = validateFile(file);
                if (validatedFile) {
                    uploadFile(validatedFile.file, validatedFile.originalFile, validatedFile.apival);
                }
            });
        }
    };
    const validateFile = (file) => {
        let f = 0;
        let name = file.name.replace(/\'+/g, "_");
        let img_value = "";
        let apival = "";

        if (/\//.test(name)) {
            const my_array = name.split("/");
            img_value = my_array[my_array.length - 1].replace(/\\s+/g, "-").toLowerCase();
        } else {
            const my_array = name.split("\\");
            img_value = my_array[my_array.length - 1].replace(/\\s+/g, "-").toLowerCase();
        }

        if (img_value.indexOf("\\") > -1) {
            img_value = img_value.replace(/\\/g, "/");
        }
        if (img_value.indexOf(" ") > -1) {
            img_value = img_value.replace(/ /g, "-");
        }

        const img_value_name = img_value.substr(img_value.lastIndexOf("/") + 1);

        if (img_value_name.length > 65) {
            f = 1;
            alert("Filename cannot have more than 65 characters");
        }

        const img_value_name1 = img_value_name.split(".");

        if (img_value_name1.length > 2) {
            f = 1;
            alert("Filename cannot contain dot(.)");
        }
        if (img_value_name.lastIndexOf(" ") > -1) {
            f = 1;
            alert("Filename can contain only alphabets (a-z A-Z), numbers (0-9), underscore (_) or hyphen (-)");
        }

        const ext = img_value.substr(img_value.indexOf("."), img_value.length).toLowerCase();
        const imesh_cookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
        const imiss = isset(() => readCookieREC('im_iss')) ? readCookieREC('im_iss') : '';
        const imageExtensions = [".jpg", ".gif", ".png", ".jpeg", ".heic"];
        const documentExtensions = [".pdf",".doc",".docx",".xls",".xlsx",".txt",".rtf",".ppt",".pptx"];
        if (imageExtensions.includes(ext)) {
            if (imiss == '') {
                apival = "-external";
              }
        } else if (documentExtensions.includes(ext)) {
            if (imiss == '') {
                f = 1;
                alert("Please upload files in jpg / gif / png / jpeg / heic format only");
            }
        }else {
            f = 1;
            alert(
                "Attachment can be either of jpg / gif / png / pdf / doc / docx / xls / xlsx / txt / rtf / ppt / pptx file extensions only"
            );
        }

        if (f === 1) {
            return null;
        }

        const data = new FormData();
        data.append("IMAGE", file);
        data.append("MODID", window.forms_param.modId); // Add appropriate value
        data.append("USR_ID", getparamValREC(imesh_cookie, "glid")); // Add appropriate value
        data.append("IMAGE_TYPE", "Rfq");
        data.append("UPLOADED_BY", "User");
        if (imiss) {
            data.append("AK", safeDecodeURIComponent(imiss).slice(2));
        }

        return { file: data, originalFile: file , apival: apival};
    };

    const uploadFile = async (data, originalFile , apival) => {
        const webAddressLocation = location.hostname;
        const appsServerName = webAddressLocation.match(/^(dev)/) ? "dev-" : (webAddressLocation.match(/^stg/) ? "stg-" : "");
        var url = "";
        if (appsServerName === "//dev-apps.imimg.com/"){
            url = `https://dev-uploading${apival}.imimg.com/uploadimage`;}
        else if (appsServerName === "//stg-apps.imimg.com/"){
            url = `https://stg-uploading${apival}.imimg.com/uploadimage`;}
        else {url = `https://uploading${apival}.imimg.com/uploadimage`;}

        try {
            const response = await fetch(url, {
                method: 'POST',
                body: data,
            });
            const result = await response.json();

            if (result.Data && result.Data.AwsPath && result.Data.AwsPath.Image_Original_Path) {
                const filepath = result.Data.AwsPath.Image_Original_Path;
                const filename = filepath.substring(filepath.lastIndexOf("/") + 1);

                setAttachedFiles(prevFiles => [...prevFiles, { originalName: filename, serviceName: filepath }]);
            } else {
                alert(result.Reason);
            }
        } catch (error) {
            console.error('Error uploading file', error);
            imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Error uploading file','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
        }
    };

    const deleteFile = async (filename) => {
        const webAddressLocation = location.hostname;
        const appsServerName = webAddressLocation.match(/^(dev)/) ? "dev-" : (webAddressLocation.match(/^stg/) ? "stg-" : "");
        var url = "https://" + appsServerName + "apps.imimg.com/index.php?r=enrichform/DeleteAttachment" + "&file=+" + filename + "+";
        try {
            const response = await fetch(url, {
                method: 'GET',
                mode: 'cors',
                cache: 'no-store'
            });
            if (response.status === 200) {
                const result = await response.json();
                if (result == 1) {
                    console.log('File deleted successfully', result);
                    return true;
                } else {
                    alert("Error deleting file");
                    return false;
                }
            } else {
                alert("Error deleting file");
                return false;
            }
        } catch (error) {
            console.error('Error deleting file', error);
            imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Error deleting file','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
            return false;
        }
    };


    const handleFileRemove = (index) => {
        const file = attachedFiles[index];
        if (file) {
            deleteFile(file.originalName).then((success) => {
                if (success) {
                    setAttachedFiles(attachedFiles.filter((_, i) => i !== index));
                }
            }).catch((error) => {
                console.error('Error deleting file', error);
            });
        }
    };
    let textclor = "";
    let dis = "";
    if(attachedFiles.length >= 4){
        textclor = "disabledtxt";
        dis = "attach-files-label-disabled";
    }

    return (
        <div className="attach-files-container">
            <div className="file-input-wrapper">
                <label htmlFor="file-input" className={`attach-files-label ${dis}`}>
                    <span className='att'>
                        <span className={`attach-files-text ${textclor}`}>Attach Files</span>
                    </span>
                </label>
                <input
                    type="file"
                    id="file-input"
                    multiple
                    onChange={handleFileChange}
                    className="file-input"
                    disabled={attachedFiles.length >= 4}
                />
            </div>
            <ul className="attached-files-list">
                {attachedFiles.map((file, index) => (
                    <li key={index} className="attached-file-item">
                        <span className="file-name">{file.originalName}</span>
                        <button
                            type="button"
                            onClick={() => handleFileRemove(index)}
                            className="remove-file-button"
                        >
                        </button>
                    </li>
                ))}
            </ul>
        </div>
    );
}

export default AttachFiles;
