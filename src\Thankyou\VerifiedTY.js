import React from 'react';
import { readCookieREC, getparamValREC,isset } from '../common/formCommfun';
import { useGlobalState } from '../context/store';


const VerifiedTY = (form_param) => {
    const {state} = useGlobalState();
    const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const email = getparamValREC(imesh, 'em') || state.UserData.em;
    const ev = getparamValREC(imesh, 'ev') || state.UserData.ev;
    const mob = getparamValREC(imesh, 'mb1') || state.UserData.mb1;
    const uv = getparamValREC(imesh, 'uv') || state.UserData.uv;

//em verified
    const Svg1 = (
        <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="13" cy="13" r="13" fill="#F4F4F4" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.76206 8.01064L13.4989 13.6838L20.2357 8.01064H6.76206ZM5.03435 8.3302C4.35647 7.75936 4.76014 6.65332 5.64636 6.65332H21.3514C22.2376 6.65332 22.6413 7.75936 21.9634 8.3302L13.4989 15.4582L5.03435 8.3302Z" fill="black" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.99756 6.42676C4.89299 6.42676 3.99756 7.32219 3.99756 8.42676V17.9999C3.99756 19.1045 4.89299 19.9999 5.99756 19.9999H21C22.1046 19.9999 23 19.1045 23 17.9999V8.42676C23 7.32219 22.1046 6.42676 21 6.42676H5.99756ZM6.80729 8.23649C6.25501 8.23649 5.80729 8.68421 5.80729 9.23649V17.1896C5.80729 17.7419 6.25501 18.1896 6.80729 18.1896H20.1894C20.7417 18.1896 21.1894 17.7419 21.1894 17.1896V9.23649C21.1894 8.68421 20.7417 8.23649 20.1894 8.23649H6.80729Z" fill="black" />
            <circle cx="23" cy="22" r="7" fill="#0B8E17" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.3171 22.3171C18.7399 21.8943 19.4255 21.8943 19.8483 22.3171L21.8174 23.9688C22.2402 24.3916 22.2402 25.0772 21.8174 25.5C21.3946 25.9228 20.709 25.9228 20.2862 25.5L18.3171 23.8483C17.8943 23.4255 17.8943 22.7399 18.3171 22.3171Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M27.0774 19.0779C27.5002 19.5007 27.5002 20.1862 27.0774 20.609L22.0312 25.3173C21.6084 25.7402 20.9228 25.7402 20.5 25.3173C20.0772 24.8945 20.0772 24.209 20.5 23.7861L25.5462 19.0779C25.9691 18.655 26.6546 18.655 27.0774 19.0779Z" fill="white" />
        </svg>

    );
//em unverified
    const Svg2 = (
        <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="13" cy="13" r="13" fill="#F4F4F4" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.76206 8.01064L13.4989 13.6838L20.2357 8.01064H6.76206ZM5.03435 8.3302C4.35647 7.75936 4.76014 6.65332 5.64636 6.65332H21.3514C22.2376 6.65332 22.6413 7.75936 21.9634 8.3302L13.4989 15.4582L5.03435 8.3302Z" fill="black" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.99756 6.42676C4.89299 6.42676 3.99756 7.32219 3.99756 8.42676V17.9999C3.99756 19.1045 4.89299 19.9999 5.99756 19.9999H21C22.1046 19.9999 23 19.1045 23 17.9999V8.42676C23 7.32219 22.1046 6.42676 21 6.42676H5.99756ZM6.80729 8.23649C6.25501 8.23649 5.80729 8.68421 5.80729 9.23649V17.1896C5.80729 17.7419 6.25501 18.1896 6.80729 18.1896H20.1894C20.7417 18.1896 21.1894 17.7419 21.1894 17.1896V9.23649C21.1894 8.68421 20.7417 8.23649 20.1894 8.23649H6.80729Z" fill="black" />
            <circle cx="23" cy="22" r="7" fill="#D90E0E" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.3171 18.3171C19.7399 17.8943 20.4255 17.8943 20.8483 18.3171L26.6829 24.1517C27.1057 24.5745 27.1057 25.2601 26.6829 25.6829C26.2601 26.1057 25.5745 26.1057 25.1517 25.6829L19.3171 19.8483C18.8943 19.4255 18.8943 18.7399 19.3171 18.3171Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M26.6829 18.3171C27.1057 18.7399 27.1057 19.4255 26.6829 19.8483L20.8483 25.6829C20.4255 26.1057 19.7399 26.1057 19.3171 25.6829C18.8943 25.2601 18.8943 24.5745 19.3171 24.1517L25.1517 18.3171C25.5745 17.8943 26.2601 17.8943 26.6829 18.3171Z" fill="white" />
        </svg>

    );
//mb verified
    const Svg3 = (
        <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="13" cy="13" r="13" fill="#F4F4F4" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 5C7 3.89543 7.89543 3 9 3H16.5385C17.643 3 18.5385 3.89543 18.5385 5V21C18.5385 22.1046 17.643 23 16.5385 23H9C7.89543 23 7 22.1046 7 21V5ZM8.53846 6.30769C8.53846 5.75541 8.98618 5.30769 9.53846 5.30769H16C16.5523 5.30769 17 5.75541 17 6.30769V18.1538C17 18.7061 16.5523 19.1538 16 19.1538H9.53846C8.98618 19.1538 8.53846 18.7061 8.53846 18.1538V6.30769ZM15.4615 20.6923H10.0769V21.4615H15.4615V20.6923Z" fill="black" />
            <circle cx="23" cy="22" r="7" fill="#0B8E17" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.3171 22.3171C18.7399 21.8943 19.4255 21.8943 19.8483 22.3171L21.8174 23.9688C22.2402 24.3916 22.2402 25.0772 21.8174 25.5C21.3946 25.9228 20.709 25.9228 20.2862 25.5L18.3171 23.8483C17.8943 23.4255 17.8943 22.7399 18.3171 22.3171Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M27.0774 19.0779C27.5002 19.5007 27.5002 20.1862 27.0774 20.609L22.0312 25.3173C21.6084 25.7402 20.9228 25.7402 20.5 25.3173C20.0772 24.8945 20.0772 24.209 20.5 23.7861L25.5462 19.0779C25.9691 18.655 26.6546 18.655 27.0774 19.0779Z" fill="white" />
        </svg>
    );
//mb uverified
    const Svg4 = (

        <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="13" cy="13" r="13" fill="#F4F4F4" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 5C7 3.89543 7.89543 3 9 3H16.5385C17.643 3 18.5385 3.89543 18.5385 5V21C18.5385 22.1046 17.643 23 16.5385 23H9C7.89543 23 7 22.1046 7 21V5ZM8.53846 6.30769C8.53846 5.75541 8.98618 5.30769 9.53846 5.30769H16C16.5523 5.30769 17 5.75541 17 6.30769V18.1538C17 18.7061 16.5523 19.1538 16 19.1538H9.53846C8.98618 19.1538 8.53846 18.7061 8.53846 18.1538V6.30769ZM15.4615 20.6923H10.0769V21.4615H15.4615V20.6923Z" fill="black" />
            <circle cx="23" cy="22" r="7" fill="#D90E0E" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.3171 18.3171C19.7399 17.8943 20.4255 17.8943 20.8483 18.3171L26.6829 24.1517C27.1057 24.5745 27.1057 25.2601 26.6829 25.6829C26.2601 26.1057 25.5745 26.1057 25.1517 25.6829L19.3171 19.8483C18.8943 19.4255 18.8943 18.7399 19.3171 18.3171Z" fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd" d="M26.6829 18.3171C27.1057 18.7399 27.1057 19.4255 26.6829 19.8483L20.8483 25.6829C20.4255 26.1057 19.7399 26.1057 19.3171 25.6829C18.8943 25.2601 18.8943 24.5745 19.3171 24.1517L25.1517 18.3171C25.5745 17.8943 26.2601 17.8943 26.6829 18.3171Z" fill="white" />
        </svg>
    );



    // Determine SVG and value for email
    let emailSvg, emailStatus;
    if (email === '') {
        emailSvg = Svg2;
        emailStatus = 'Not present';
    } else if (ev !== 'V') {
        emailSvg = Svg2;
        emailStatus = 'Unverified';
    } else {
        emailSvg = Svg1;
        emailStatus = 'Verified';
    }

    // Determine SVG and status for mobile
    let mobileSvg, mobileStatus;
    if (mob === '') {
        mobileSvg = Svg4;
        mobileStatus = 'Not present';
    } else if (uv !== 'V') {
        mobileSvg = Svg4;
        mobileStatus = 'Unverified';
    } else {
        mobileSvg = Svg3;
        mobileStatus = 'Verified';
    }

    return (
        <div className='verStatus'>
            {mobileSvg} {mobileStatus} &nbsp; {emailSvg} {emailStatus}
        </div>
    );
};

export default VerifiedTY;