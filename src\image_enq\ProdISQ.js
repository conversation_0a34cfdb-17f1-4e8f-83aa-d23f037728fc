import React from "react";
import { isset, safeDecodeURIComponent} from "../common/formCommfun";
function ProdISQ({form_param,vcdShow}){
    let isq_arr = safeDecodeURIComponent(form_param.plsqArr).split('#');
    // let vcdfrm = form_param.redirectUrl["produrl"].endsWith(".html") ? form_param.redirectUrl["produrl"] + '?vcdimgform=1': form_param.redirectUrl["produrl"] + '&vcdimgform=1' ; 

    let vcdfrm = '';

if (isset(()=>form_param.redirectUrl) && form_param.redirectUrl["produrl"]) {
  let prodUrl = String(form_param.redirectUrl["produrl"]);
  vcdfrm = prodUrl.endsWith(".html") ? prodUrl + '?vcdimgform=1' : prodUrl + '&vcdimgform=1';
} 


    return(
        <>
        <div id="t0901_isqdetails0R" className="pl0 befs16 content-centre idsf epLf30 eQlh eqmt10 pr0" >
            <div id="enqImgIsq">
            {isq_arr.map((item, index) => {
            if (index < 3) {
                const isqkey = item.split(':');
                if(safeDecodeURIComponent(isqkey[0])!='Quantity' 
                && safeDecodeURIComponent(isqkey[0])!="Quantity Unit"){
                    return (
                        <div key={`t0901_isqkey${index}`} >
                            <div className="flex-container idsf id_aic isqbtm" >
                                <div id={`t0901_isqkey${index}_0`} className="flex-item iClr1 eqElps eqElps1" style={{ flex: '0 0 auto', marginRight: '8px' }}>
                                    {safeDecodeURIComponent(isqkey[0]) + " : "}
                                </div>
                                <div id={`t0901_isqkey${index}_1`} className="flex-item iClr2 eqElps eqElps1" style={{ flex: '1 1 auto' }}>
                                    {safeDecodeURIComponent(isqkey[1]).replace(/##/g, ', ')}
                                </div>
                            </div>
                        </div>
                    );

                }
               
            }
            
            return null; // Return null for items beyond index 4
            })}
            </div>
            {/* VCD */}
            {/* {vcdfrm!='' && (window.screencount==1 || vcdShow==false)? <div id="t0901vcdR" className="btmar tpmar asFe ">
  <a target="_blank" href={vcdfrm} className="viewdetails">
    View Complete Details
  </a>
</div>  : ''} */}
            
        </div>
        </>
    )
}
export default ProdISQ;