import { isset,currentISO } from "../common/formCommfun";
const ISQAPICall = (form_param) => {
  const retrieveIsqData = (id, country) => {
    try {
      return JSON.parse(sessionStorage.getItem("enqbl" + id + "-" + country)) || null;
    } catch (err) {
      console.error("Failed to retrieve sessionStorage item:", err);
      return null;
    }
  };

  const iso = currentISO();
  const toSaveISO = (iso!='IN') ? 'F' : 'IN';
  let mcatId = isset(()=>form_param) && isset(()=>form_param.mcatId) && form_param.mcatId!=''  ? form_param.mcatId : '';
  let mdId= isset(()=>form_param) ? form_param.modId : 'DIR';

  const fetchData = async () => {
    if(!mcatId && form_param.catId){
      mcatId=form_param.catId;
    }
    mcatId=mcatId||-1;
    let prod_name = '';
    if(form_param.prodName){ 
      prod_name = form_param.prodName.replace("&",'and'); 
      prod_name = encodeURIComponent(prod_name).replace(/%2F/g, '/');   
    }    
        
    let serviceUrl=''; 
    const appsServerName = location.hostname.match(/^dev/) ? "//apps.imimg.com/" : location.hostname.match(/^stg/) ? "//stg-apps.imimg.com/" : "//apps.imimg.com/"; 
    if(form_param.mcatId){  
        serviceUrl = `${appsServerName}index.php/getisqnew/mcatid=${form_param.mcatId}?r=Newreqform/GetIsq&modid=${mdId}&cat_type=3&isq_format=1&mcatid=${form_param.mcatId}&generic_flag=1&country_iso=${iso}`; 
    }
    else if(!form_param.mcatId && !prod_name){  
      serviceUrl = `${appsServerName}index.php/getisqnew/mcatid=-1?r=Newreqform/GetIsq&modid=${mdId}&cat_type=3&isq_format=1&mcatid=-1&generic_flag=1&country_iso=${iso}`; 
  }
    else{   
        serviceUrl=`${appsServerName}index.php/getisqnew/prod_name=${prod_name}?r=Newreqform/GetIsq&modid=${mdId}&cat_type=3&isq_format=1&prod_name=${prod_name}&generic_flag=1&country_iso=${iso}`; 
    } 

    const isq_data = retrieveIsqData(mcatId, toSaveISO);

    if (!isq_data && isset(()=>iso)) {
      try {
        const response = await fetch(serviceUrl, {
          method: "GET",
          mode: 'cors',
        });

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const resdata = await response.json();

        if (resdata) {
          try {
            sessionStorage.setItem(`enqbl${mcatId}-${toSaveISO}`, JSON.stringify(resdata));
          } catch (err) {
            console.error('Error in storing data:', err);
          }
          return resdata;
        }
      } catch (error) {
        console.error("Error fetching ISQ data:", error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'IsqAPI_failure','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
      }
    } else {
      return isq_data;
    }
  };
  const res = fetchData();
  return res;
};

export default ISQAPICall;
